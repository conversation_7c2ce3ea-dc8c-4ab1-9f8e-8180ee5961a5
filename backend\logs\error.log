{"error":"Upload service failed: Upload failed: getaddrinfo ENOTFOUND api.cloudinary.com","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"Error: Upload service failed: Upload failed: getaddrinfo ENOTFOUND api.cloudinary.com\n    at uploadFile (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\services\\fileUpload.service.js:62:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MessageService.handleFileUpload (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\services\\message.service.js:48:17)\n    at async MessageService.sendMessage (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\services\\message.service.js:710:24)\n    at async Object.sendMessage (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\graphql\\messageResolvers.js:721:24)","timestamp":"2025-05-26 02:21:39"}
{"error":"Upload service failed: Upload failed: getaddrinfo ENOTFOUND api.cloudinary.com","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"Error: Upload service failed: Upload failed: getaddrinfo ENOTFOUND api.cloudinary.com\n    at uploadFile (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\services\\fileUpload.service.js:62:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MessageService.handleFileUpload (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\services\\message.service.js:48:17)\n    at async MessageService.sendMessage (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\services\\message.service.js:710:24)\n    at async Object.sendMessage (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\graphql\\messageResolvers.js:721:24)","timestamp":"2025-05-26 02:21:40"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:23:35"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:23:35"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:23:41"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:23:42"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:23:48"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:23:48"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:24:23"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:24:24"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:24:57"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:24:57"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294542b5d5c86fd2e56e4b","senderId":"68294132b5d5c86fd2e56e23","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:30:51"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294542b5d5c86fd2e56e4b","senderId":"68294132b5d5c86fd2e56e23","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:30:52"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"682bb95fb9b407dd58126686","senderId":"68294132b5d5c86fd2e56e23","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 11:52:57"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"682bb95fb9b407dd58126686","senderId":"68294132b5d5c86fd2e56e23","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 11:52:58"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 14:09:00"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 14:09:05"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 18:43:18"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 18:43:19"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 22:58:51"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 22:58:52"}
