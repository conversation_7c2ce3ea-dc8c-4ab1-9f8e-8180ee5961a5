{"ast": null, "code": "import { Subject, of, BehaviorSubject } from 'rxjs';\nimport { MessageType } from 'src/app/models/message.model';\nimport { catchError, map, takeUntil, take, debounceTime, distinctUntilChanged, filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/message.service\";\nimport * as i2 from \"@app/services/theme.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = [\"notificationContainer\"];\nfunction NotificationListComponent_div_8_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"label\", 34)(2, \"input\", 35);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_div_3_Template_input_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.toggleSelectAll($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"span\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r10.allSelected);\n  }\n}\nfunction NotificationListComponent_div_8_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.markAllAsRead());\n    });\n    i0.ɵɵelement(1, \"i\", 38);\n    i0.ɵɵtext(2, \" Tout marquer comme lu \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_8_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.deleteAllNotifications());\n    });\n    i0.ɵɵelement(1, \"i\", 40);\n    i0.ɵɵtext(2, \" Tout supprimer \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.loadNotifications());\n    });\n    i0.ɵɵelement(2, \"i\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, NotificationListComponent_div_8_div_3_Template, 4, 1, \"div\", 26);\n    i0.ɵɵpipe(4, \"async\");\n    i0.ɵɵelementStart(5, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.toggleUnreadFilter());\n    });\n    i0.ɵɵelement(6, \"i\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.toggleSound());\n    });\n    i0.ɵɵelement(8, \"i\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, NotificationListComponent_div_8_button_9_Template, 3, 0, \"button\", 31);\n    i0.ɵɵpipe(10, \"async\");\n    i0.ɵɵtemplate(11, NotificationListComponent_div_8_button_11_Template, 3, 0, \"button\", 32);\n    i0.ɵɵpipe(12, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(4, 9, ctx_r0.hasNotifications()));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", ctx_r0.showOnlyUnread);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", !ctx_r0.isSoundMuted);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r0.isSoundMuted ? \"Activer le son\" : \"D\\u00E9sactiver le son\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.isSoundMuted ? \"fa-volume-mute\" : \"fa-volume-up\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(10, 11, ctx_r0.unreadCount$) || 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(12, 13, ctx_r0.hasNotifications()));\n  }\n}\nfunction NotificationListComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"span\", 42);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_9_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.markSelectedAsRead());\n    });\n    i0.ɵɵelement(4, \"i\", 43);\n    i0.ɵɵtext(5, \" Marquer comme lu \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_9_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.deleteSelectedNotifications());\n    });\n    i0.ɵɵelement(7, \"i\", 40);\n    i0.ɵɵtext(8, \" Supprimer \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_9_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r26 = i0.ɵɵnextContext();\n      ctx_r26.selectedNotifications.clear();\n      ctx_r26.showSelectionBar = false;\n      return i0.ɵɵresetView(ctx_r26.allSelected = false);\n    });\n    i0.ɵɵelement(10, \"i\", 46);\n    i0.ɵɵtext(11, \" Annuler \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.selectedNotifications.size, \" s\\u00E9lectionn\\u00E9(s)\");\n  }\n}\nfunction NotificationListComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"div\", 48);\n    i0.ɵɵelementStart(2, \"p\", 49);\n    i0.ɵɵtext(3, \"Chargement des notifications...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 51);\n    i0.ɵɵelement(2, \"i\", 52);\n    i0.ɵɵelementStart(3, \"div\")(4, \"h3\", 53);\n    i0.ɵɵtext(5, \"Erreur de chargement\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 54);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_11_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.loadNotifications());\n    });\n    i0.ɵɵtext(9, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r3.getErrorMessage());\n  }\n}\nfunction NotificationListComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57);\n    i0.ɵɵelement(2, \"i\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 59);\n    i0.ɵɵtext(4, \"Aucune notification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 60);\n    i0.ɵɵtext(6, \"Vous \\u00EAtes \\u00E0 jour !\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_12_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.loadNotifications());\n    });\n    i0.ɵɵtext(8, \" V\\u00E9rifier les nouvelles notifications \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notification_r34 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", notification_r34.message == null ? null : notification_r34.message.content, \" \");\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90);\n    i0.ɵɵelement(1, \"i\", 91);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notification_r34 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", notification_r34.message == null ? null : notification_r34.message.attachments == null ? null : notification_r34.message.attachments.length, \" pi\\u00E8ce(s) jointe(s) \");\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 92);\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_button_24_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r45);\n      const notification_r34 = i0.ɵɵnextContext().$implicit;\n      const ctx_r43 = i0.ɵɵnextContext(2);\n      ctx_r43.getNotificationAttachments(notification_r34.id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"i\", 91);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_button_25_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 97);\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_button_25_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 98);\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_button_25_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r50);\n      const notification_r34 = i0.ɵɵnextContext().$implicit;\n      const ctx_r48 = i0.ɵɵnextContext(2);\n      ctx_r48.joinConversation(notification_r34);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtemplate(1, NotificationListComponent_div_14_ng_container_2_button_25_i_1_Template, 1, 0, \"i\", 95);\n    i0.ɵɵtemplate(2, NotificationListComponent_div_14_ng_container_2_button_25_i_2_Template, 1, 0, \"i\", 96);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r39 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r39.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r39.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r39.loading);\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r53 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 99);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_button_28_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r53);\n      const notification_r34 = i0.ɵɵnextContext().$implicit;\n      const ctx_r51 = i0.ɵɵnextContext(2);\n      ctx_r51.markAsRead(notification_r34.id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"i\", 100);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r55 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 66)(2, \"div\", 67)(3, \"label\", 34)(4, \"input\", 35);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_Template_input_click_4_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r55);\n      const notification_r34 = restoredCtx.$implicit;\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r54.toggleSelection(notification_r34.id, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"span\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 68);\n    i0.ɵɵelement(7, \"img\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 70)(9, \"div\", 71)(10, \"div\", 72)(11, \"div\", 73)(12, \"span\", 74);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 75);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 76)(18, \"span\", 77);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(20, NotificationListComponent_div_14_ng_container_2_div_20_Template, 2, 1, \"div\", 78);\n    i0.ɵɵtemplate(21, NotificationListComponent_div_14_ng_container_2_div_21_Template, 3, 1, \"div\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, NotificationListComponent_div_14_ng_container_2_div_22_Template, 1, 0, \"div\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 81);\n    i0.ɵɵtemplate(24, NotificationListComponent_div_14_ng_container_2_button_24_Template, 2, 0, \"button\", 82);\n    i0.ɵɵtemplate(25, NotificationListComponent_div_14_ng_container_2_button_25_Template, 3, 3, \"button\", 83);\n    i0.ɵɵelementStart(26, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_Template_button_click_26_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r55);\n      const notification_r34 = restoredCtx.$implicit;\n      const ctx_r56 = i0.ɵɵnextContext(2);\n      ctx_r56.openNotificationDetails(notification_r34);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(27, \"i\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, NotificationListComponent_div_14_ng_container_2_button_28_Template, 2, 0, \"button\", 86);\n    i0.ɵɵelementStart(29, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_Template_button_click_29_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r55);\n      const notification_r34 = restoredCtx.$implicit;\n      const ctx_r57 = i0.ɵɵnextContext(2);\n      ctx_r57.deleteNotification(notification_r34.id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(30, \"i\", 88);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const notification_r34 = ctx.$implicit;\n    const ctx_r32 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"futuristic-notification-unread\", !notification_r34.isRead)(\"futuristic-notification-read\", notification_r34.isRead)(\"futuristic-notification-selected\", ctx_r32.isSelected(notification_r34.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", ctx_r32.isSelected(notification_r34.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", (notification_r34.senderId == null ? null : notification_r34.senderId.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate((notification_r34.senderId == null ? null : notification_r34.senderId.username) || \"Syst\\u00E8me\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(16, 17, notification_r34.timestamp, \"shortTime\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(notification_r34.content);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notification_r34.message == null ? null : notification_r34.message.content);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notification_r34.message == null ? null : notification_r34.message.attachments == null ? null : notification_r34.message.attachments.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !notification_r34.isRead);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", notification_r34.message == null ? null : notification_r34.message.attachments == null ? null : notification_r34.message.attachments.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notification_r34.type === \"NEW_MESSAGE\" || notification_r34.type === \"GROUP_INVITE\" || notification_r34.type === \"MESSAGE_REACTION\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !notification_r34.isRead);\n  }\n}\nfunction NotificationListComponent_div_14_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101);\n    i0.ɵɵelement(1, \"div\", 102);\n    i0.ɵɵelementStart(2, \"p\", 103);\n    i0.ɵɵtext(3, \" Chargement des notifications plus anciennes... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r59 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62, 63);\n    i0.ɵɵlistener(\"scroll\", function NotificationListComponent_div_14_Template_div_scroll_0_listener() {\n      i0.ɵɵrestoreView(_r59);\n      const _r31 = i0.ɵɵreference(1);\n      const ctx_r58 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r58.onScroll(_r31));\n    });\n    i0.ɵɵtemplate(2, NotificationListComponent_div_14_ng_container_2_Template, 31, 20, \"ng-container\", 64);\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵtemplate(4, NotificationListComponent_div_14_div_4_Template, 4, 0, \"div\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(3, 2, ctx_r5.filteredNotifications$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.loadingMore);\n  }\n}\nfunction NotificationListComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"div\", 48);\n    i0.ɵɵelementStart(2, \"p\", 49);\n    i0.ɵɵtext(3, \"Chargement des pi\\u00E8ces jointes...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57);\n    i0.ɵɵelement(2, \"i\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 59);\n    i0.ɵɵtext(4, \"Aucune pi\\u00E8ce jointe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 60);\n    i0.ɵɵtext(6, \" Aucune pi\\u00E8ce jointe n'a \\u00E9t\\u00E9 trouv\\u00E9e pour cette notification. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_27_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r67 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 120)(1, \"img\", 121);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_27_div_1_div_1_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r67);\n      const attachment_r61 = i0.ɵɵnextContext().$implicit;\n      const ctx_r65 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r65.openAttachment(attachment_r61.url));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const attachment_r61 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", attachment_r61.url, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction NotificationListComponent_div_27_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 122);\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const attachment_r61 = i0.ɵɵnextContext().$implicit;\n    const ctx_r63 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r63.getFileIcon(attachment_r61.type));\n  }\n}\nfunction NotificationListComponent_div_27_div_1_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 123);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const attachment_r61 = i0.ɵɵnextContext().$implicit;\n    const ctx_r64 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r64.formatFileSize(attachment_r61.size));\n  }\n}\nfunction NotificationListComponent_div_27_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r72 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵtemplate(1, NotificationListComponent_div_27_div_1_div_1_Template, 2, 1, \"div\", 108);\n    i0.ɵɵtemplate(2, NotificationListComponent_div_27_div_1_div_2_Template, 2, 2, \"div\", 109);\n    i0.ɵɵelementStart(3, \"div\", 110)(4, \"div\", 111);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 112)(7, \"span\", 113);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, NotificationListComponent_div_27_div_1_span_9_Template, 2, 1, \"span\", 114);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 115)(11, \"button\", 116);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_27_div_1_Template_button_click_11_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r72);\n      const attachment_r61 = restoredCtx.$implicit;\n      const ctx_r71 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r71.openAttachment(attachment_r61.url));\n    });\n    i0.ɵɵelement(12, \"i\", 117);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 118);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_27_div_1_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r72);\n      const attachment_r61 = restoredCtx.$implicit;\n      const ctx_r73 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r73.downloadAttachment(attachment_r61));\n    });\n    i0.ɵɵelement(14, \"i\", 119);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const attachment_r61 = ctx.$implicit;\n    const ctx_r60 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r60.isImage(attachment_r61.type));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r60.isImage(attachment_r61.type));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", attachment_r61.name || \"Pi\\u00E8ce jointe\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r60.getFileTypeLabel(attachment_r61.type));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", attachment_r61.size);\n  }\n}\nfunction NotificationListComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵtemplate(1, NotificationListComponent_div_27_div_1_Template, 15, 5, \"div\", 106);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.currentAttachments);\n  }\n}\nfunction NotificationListComponent_div_36_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 147)(1, \"strong\");\n    i0.ɵɵtext(2, \"Message original :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r74 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r74.currentNotification.message == null ? null : ctx_r74.currentNotification.message.content, \" \");\n  }\n}\nfunction NotificationListComponent_div_36_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 137)(1, \"span\", 138);\n    i0.ɵɵtext(2, \"Lu le :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 139);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r75 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, ctx_r75.currentNotification.readAt, \"medium\"));\n  }\n}\nfunction NotificationListComponent_div_36_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 148)(1, \"span\", 138);\n    i0.ɵɵelement(2, \"i\", 149);\n    i0.ɵɵtext(3, \" Note : \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 150);\n    i0.ɵɵtext(5, \" Ouvrir les d\\u00E9tails ne marque pas automatiquement comme lu \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_36_div_37_div_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r87 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 164)(1, \"img\", 121);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_36_div_37_div_5_div_1_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r87);\n      const attachment_r81 = i0.ɵɵnextContext().$implicit;\n      const ctx_r85 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r85.openAttachment(attachment_r81.url));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const attachment_r81 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", attachment_r81.url, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction NotificationListComponent_div_36_div_37_div_5_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 165);\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const attachment_r81 = i0.ɵɵnextContext().$implicit;\n    const ctx_r83 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r83.getFileIcon(attachment_r81.type));\n  }\n}\nfunction NotificationListComponent_div_36_div_37_div_5_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 166);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const attachment_r81 = i0.ɵɵnextContext().$implicit;\n    const ctx_r84 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r84.formatFileSize(attachment_r81.size));\n  }\n}\nfunction NotificationListComponent_div_36_div_37_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r92 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 153);\n    i0.ɵɵtemplate(1, NotificationListComponent_div_36_div_37_div_5_div_1_Template, 2, 1, \"div\", 154);\n    i0.ɵɵtemplate(2, NotificationListComponent_div_36_div_37_div_5_div_2_Template, 2, 2, \"div\", 155);\n    i0.ɵɵelementStart(3, \"div\", 156)(4, \"div\", 157);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 158)(7, \"span\", 159);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, NotificationListComponent_div_36_div_37_div_5_span_9_Template, 2, 1, \"span\", 160);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 161)(11, \"button\", 162);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_36_div_37_div_5_Template_button_click_11_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r92);\n      const attachment_r81 = restoredCtx.$implicit;\n      const ctx_r91 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r91.openAttachment(attachment_r81.url));\n    });\n    i0.ɵɵelement(12, \"i\", 117);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 163);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_36_div_37_div_5_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r92);\n      const attachment_r81 = restoredCtx.$implicit;\n      const ctx_r93 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r93.downloadAttachment(attachment_r81));\n    });\n    i0.ɵɵelement(14, \"i\", 119);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const attachment_r81 = ctx.$implicit;\n    const ctx_r80 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r80.isImage(attachment_r81.type));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r80.isImage(attachment_r81.type));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", attachment_r81.name || \"Pi\\u00E8ce jointe\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r80.getFileTypeLabel(attachment_r81.type));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", attachment_r81.size);\n  }\n}\nfunction NotificationListComponent_div_36_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 124)(1, \"h4\", 125);\n    i0.ɵɵelement(2, \"i\", 16);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 151);\n    i0.ɵɵtemplate(5, NotificationListComponent_div_36_div_37_div_5_Template, 15, 5, \"div\", 152);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r77 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Pi\\u00E8ces jointes (\", ctx_r77.currentAttachments.length, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r77.currentAttachments);\n  }\n}\nfunction NotificationListComponent_div_36_button_39_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 170);\n  }\n}\nfunction NotificationListComponent_div_36_button_39_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 171);\n  }\n}\nfunction NotificationListComponent_div_36_button_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r97 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 167);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_36_button_39_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r97);\n      const ctx_r96 = i0.ɵɵnextContext(2);\n      ctx_r96.joinConversation(ctx_r96.currentNotification);\n      return i0.ɵɵresetView(ctx_r96.closeNotificationDetailsModal());\n    });\n    i0.ɵɵtemplate(1, NotificationListComponent_div_36_button_39_i_1_Template, 1, 0, \"i\", 168);\n    i0.ɵɵtemplate(2, NotificationListComponent_div_36_button_39_i_2_Template, 1, 0, \"i\", 169);\n    i0.ɵɵtext(3, \" Rejoindre la conversation \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r78 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r78.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r78.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r78.loading);\n  }\n}\nfunction NotificationListComponent_div_36_button_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r99 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 172);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_36_button_40_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r99);\n      const ctx_r98 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r98.markAsRead(ctx_r98.currentNotification.id));\n    });\n    i0.ɵɵelement(1, \"i\", 173);\n    i0.ɵɵtext(2, \" Marquer comme lu \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r101 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 124)(2, \"h4\", 125);\n    i0.ɵɵelement(3, \"i\", 126);\n    i0.ɵɵtext(4, \" Exp\\u00E9diteur \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 127);\n    i0.ɵɵelement(6, \"img\", 128);\n    i0.ɵɵelementStart(7, \"div\", 129)(8, \"span\", 130);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 131);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 124)(14, \"h4\", 125);\n    i0.ɵɵelement(15, \"i\", 132);\n    i0.ɵɵtext(16, \" Message \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 133);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, NotificationListComponent_div_36_div_19_Template, 4, 1, \"div\", 134);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 124)(21, \"h4\", 125);\n    i0.ɵɵelement(22, \"i\", 135);\n    i0.ɵɵtext(23, \" Informations \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 136)(25, \"div\", 137)(26, \"span\", 138);\n    i0.ɵɵtext(27, \"Type :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\", 139);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 137)(31, \"span\", 138);\n    i0.ɵɵtext(32, \"Statut :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"span\", 139);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(35, NotificationListComponent_div_36_div_35_Template, 6, 4, \"div\", 140);\n    i0.ɵɵtemplate(36, NotificationListComponent_div_36_div_36_Template, 6, 0, \"div\", 141);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(37, NotificationListComponent_div_36_div_37_Template, 6, 2, \"div\", 142);\n    i0.ɵɵelementStart(38, \"div\", 143);\n    i0.ɵɵtemplate(39, NotificationListComponent_div_36_button_39_Template, 4, 3, \"button\", 144);\n    i0.ɵɵtemplate(40, NotificationListComponent_div_36_button_40_Template, 3, 0, \"button\", 145);\n    i0.ɵɵelementStart(41, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_36_Template_button_click_41_listener() {\n      i0.ɵɵrestoreView(_r101);\n      const ctx_r100 = i0.ɵɵnextContext();\n      ctx_r100.deleteNotification(ctx_r100.currentNotification.id);\n      return i0.ɵɵresetView(ctx_r100.closeNotificationDetailsModal());\n    });\n    i0.ɵɵelement(42, \"i\", 146);\n    i0.ɵɵtext(43, \" Supprimer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"src\", (ctx_r9.currentNotification.senderId == null ? null : ctx_r9.currentNotification.senderId.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r9.currentNotification.senderId == null ? null : ctx_r9.currentNotification.senderId.username) || \"Syst\\u00E8me\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(12, 16, ctx_r9.currentNotification.timestamp, \"medium\"), \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.currentNotification.content, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.currentNotification.message == null ? null : ctx_r9.currentNotification.message.content);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r9.currentNotification.type);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"text-green-500\", ctx_r9.currentNotification.isRead)(\"text-orange-500\", !ctx_r9.currentNotification.isRead);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.currentNotification.isRead ? \"Lu\" : \"Non lu\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.currentNotification.readAt);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.currentNotification.isRead);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.currentAttachments.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.currentNotification.type === \"NEW_MESSAGE\" || ctx_r9.currentNotification.type === \"GROUP_INVITE\" || ctx_r9.currentNotification.type === \"MESSAGE_REACTION\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.currentNotification.isRead);\n  }\n}\nexport class NotificationListComponent {\n  constructor(messageService, themeService, router) {\n    this.messageService = messageService;\n    this.themeService = themeService;\n    this.router = router;\n    this.loading = true;\n    this.loadingMore = false;\n    this.hasMoreNotifications = true;\n    this.error = null;\n    this.showOnlyUnread = false;\n    this.isSoundMuted = false;\n    // Propriétés pour la sélection multiple\n    this.selectedNotifications = new Set();\n    this.allSelected = false;\n    this.showSelectionBar = false;\n    // Propriétés pour le modal des pièces jointes\n    this.showAttachmentsModal = false;\n    this.loadingAttachments = false;\n    this.currentAttachments = [];\n    // Propriétés pour le modal des détails de notification\n    this.showNotificationDetailsModal = false;\n    this.currentNotification = null;\n    this.destroy$ = new Subject();\n    this.scrollPosition$ = new BehaviorSubject(0);\n    this.notifications$ = this.messageService.notifications$;\n    this.filteredNotifications$ = this.notifications$; // Par défaut, afficher toutes les notifications\n    this.unreadCount$ = this.messageService.notificationCount$;\n    this.isDarkMode$ = this.themeService.darkMode$;\n    // Vérifier l'état du son\n    this.isSoundMuted = this.messageService.isMuted();\n  }\n  /**\n   * Rejoint une conversation ou un groupe à partir d'une notification\n   * @param notification Notification contenant les informations de la conversation ou du groupe\n   */\n  joinConversation(notification) {\n    // Marquer la notification comme lue d'abord\n    this.markAsRead(notification.id);\n    // Extraire les informations pertinentes de la notification\n    const conversationId = notification.conversationId || notification.metadata && notification.metadata['conversationId'] || (notification.relatedEntity && notification.relatedEntity.includes('conversation') ? notification.relatedEntity : null);\n    const groupId = notification.groupId || notification.metadata && notification.metadata['groupId'] || (notification.relatedEntity && notification.relatedEntity.includes('group') ? notification.relatedEntity : null);\n    // Déterminer où rediriger l'utilisateur\n    if (conversationId) {\n      this.router.navigate(['/messages/conversations/chat', conversationId]);\n    } else if (groupId) {\n      this.router.navigate(['/messages/group', groupId]);\n    } else if (notification.senderId && notification.senderId.id) {\n      this.loading = true;\n      this.messageService.getOrCreateConversation(notification.senderId.id).subscribe({\n        next: conversation => {\n          this.loading = false;\n          if (conversation && conversation.id) {\n            this.router.navigate(['/messages/conversations/chat', conversation.id]);\n          } else {\n            this.router.navigate(['/messages']);\n          }\n        },\n        error: error => {\n          this.loading = false;\n          this.error = error;\n          this.router.navigate(['/messages']);\n        }\n      });\n    } else {\n      this.router.navigate(['/messages']);\n    }\n  }\n  onScroll(target) {\n    if (!target) return;\n    const scrollPosition = target.scrollTop;\n    const scrollHeight = target.scrollHeight;\n    const clientHeight = target.clientHeight;\n    // Si on est proche du bas (à 200px du bas)\n    if (scrollHeight - scrollPosition - clientHeight < 200) {\n      this.scrollPosition$.next(scrollPosition);\n    }\n  }\n  ngOnInit() {\n    // Charger la préférence de son depuis le localStorage\n    const savedMutePreference = localStorage.getItem('notificationSoundMuted');\n    if (savedMutePreference !== null) {\n      this.isSoundMuted = savedMutePreference === 'true';\n      this.messageService.setMuted(this.isSoundMuted);\n    }\n    this.loadNotifications();\n    this.setupSubscriptions();\n    this.setupInfiniteScroll();\n    this.filterDeletedNotifications();\n  }\n  /**\n   * Filtre les notifications supprimées lors du chargement initial\n   */\n  filterDeletedNotifications() {\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    if (deletedNotificationIds.size > 0) {\n      this.notifications$.pipe(take(1)).subscribe(notifications => {\n        const filteredNotifications = notifications.filter(notification => !deletedNotificationIds.has(notification.id));\n        this.messageService.notifications.next(filteredNotifications);\n        const unreadCount = filteredNotifications.filter(n => !n.isRead).length;\n        this.messageService.notificationCount.next(unreadCount);\n        this.updateNotificationCache(filteredNotifications);\n      });\n    }\n  }\n  setupInfiniteScroll() {\n    // Configurer le chargement des anciennes notifications lors du défilement\n    this.scrollPosition$.pipe(takeUntil(this.destroy$), debounceTime(200),\n    // Attendre 200ms après le dernier événement de défilement\n    distinctUntilChanged(),\n    // Ne déclencher que si la position de défilement a changé\n    filter(() => !this.loadingMore && this.hasMoreNotifications) // Ne charger que s'il y a plus de notifications et qu'on n'est pas déjà en train de charger\n    ).subscribe(() => {\n      this.loadMoreNotifications();\n    });\n  }\n  loadNotifications() {\n    this.loading = true;\n    this.loadingMore = false;\n    this.error = null;\n    this.hasMoreNotifications = true;\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    this.messageService.getNotifications(true).pipe(takeUntil(this.destroy$), map(notifications => {\n      if (deletedNotificationIds.size > 0) {\n        return notifications.filter(notification => !deletedNotificationIds.has(notification.id));\n      }\n      return notifications;\n    })).subscribe({\n      next: notifications => {\n        this.messageService.notifications.next(notifications);\n        const unreadCount = notifications.filter(n => !n.isRead).length;\n        this.messageService.notificationCount.next(unreadCount);\n        this.loading = false;\n        this.hasMoreNotifications = this.messageService.hasMoreNotifications();\n      },\n      error: err => {\n        this.error = err;\n        this.loading = false;\n        this.hasMoreNotifications = false;\n      }\n    });\n  }\n  loadMoreNotifications() {\n    if (this.loadingMore || !this.hasMoreNotifications) return;\n    this.loadingMore = true;\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    this.messageService.loadMoreNotifications().pipe(takeUntil(this.destroy$), map(notifications => {\n      if (deletedNotificationIds.size > 0) {\n        return notifications.filter(notification => !deletedNotificationIds.has(notification.id));\n      }\n      return notifications;\n    })).subscribe({\n      next: notifications => {\n        this.notifications$.pipe(take(1)).subscribe(existingNotifications => {\n          const allNotifications = [...existingNotifications, ...notifications];\n          this.messageService.notifications.next(allNotifications);\n          const unreadCount = allNotifications.filter(n => !n.isRead).length;\n          this.messageService.notificationCount.next(unreadCount);\n          this.updateNotificationCache(allNotifications);\n        });\n        this.loadingMore = false;\n        this.hasMoreNotifications = this.messageService.hasMoreNotifications();\n      },\n      error: err => {\n        this.loadingMore = false;\n        this.hasMoreNotifications = false;\n      }\n    });\n  }\n  setupSubscriptions() {\n    this.messageService.subscribeToNewNotifications().pipe(takeUntil(this.destroy$), catchError(error => {\n      console.error('Notification stream error:', error);\n      return of(null);\n    })).subscribe();\n    this.messageService.subscribeToNotificationsRead().pipe(takeUntil(this.destroy$), catchError(error => {\n      console.error('Notifications read stream error:', error);\n      return of(null);\n    })).subscribe();\n  }\n  markAsRead(notificationId) {\n    if (!notificationId) {\n      this.error = new Error('ID de notification invalide');\n      return;\n    }\n    this.notifications$.pipe(take(1)).subscribe(notifications => {\n      const notification = notifications.find(n => n.id === notificationId);\n      if (notification) {\n        if (notification.isRead) return;\n        const updatedNotifications = notifications.map(n => n.id === notificationId ? {\n          ...n,\n          isRead: true,\n          readAt: new Date().toISOString()\n        } : n);\n        this.updateUIWithNotifications(updatedNotifications);\n        this.messageService.markAsRead([notificationId]).pipe(takeUntil(this.destroy$)).subscribe({\n          next: result => {\n            if (result && result.success) {\n              if (this.error && this.error.message.includes('mark')) {\n                this.error = null;\n              }\n            }\n          },\n          error: err => {\n            const revertedNotifications = notifications.map(n => n.id === notificationId ? {\n              ...n,\n              isRead: false,\n              readAt: undefined\n            } : n);\n            this.messageService.notifications.next(revertedNotifications);\n            const revertedUnreadCount = revertedNotifications.filter(n => !n.isRead).length;\n            this.messageService.notificationCount.next(revertedUnreadCount);\n          }\n        });\n      } else {\n        this.loadNotifications();\n      }\n    });\n  }\n  /**\n   * Met à jour l'interface utilisateur avec les nouvelles notifications\n   * @param notifications Notifications à afficher\n   */\n  updateUIWithNotifications(notifications) {\n    // Mettre à jour l'interface utilisateur immédiatement\n    this.messageService.notifications.next(notifications);\n    // Mettre à jour le compteur de notifications non lues\n    const unreadCount = notifications.filter(n => !n.isRead).length;\n    this.messageService.notificationCount.next(unreadCount);\n    // Mettre à jour le cache de notifications dans le service\n    this.updateNotificationCache(notifications);\n  }\n  /**\n   * Met à jour le cache de notifications dans le service\n   * @param notifications Notifications à mettre à jour\n   */\n  updateNotificationCache(notifications) {\n    notifications.forEach(notification => {\n      this.messageService.updateNotificationCache?.(notification);\n    });\n  }\n  /**\n   * Réinitialise la sélection des notifications\n   */\n  resetSelection() {\n    this.selectedNotifications.clear();\n    this.allSelected = false;\n    this.showSelectionBar = false;\n  }\n  markAllAsRead() {\n    this.notifications$.pipe(take(1)).subscribe(notifications => {\n      const unreadIds = notifications.filter(n => !n.isRead).map(n => n.id);\n      if (unreadIds.length === 0) return;\n      const validIds = unreadIds.filter(id => id && typeof id === 'string' && id.trim() !== '');\n      if (validIds.length !== unreadIds.length) {\n        this.error = new Error('Invalid notification IDs');\n        return;\n      }\n      const updatedNotifications = notifications.map(n => validIds.includes(n.id) ? {\n        ...n,\n        isRead: true,\n        readAt: new Date().toISOString()\n      } : n);\n      this.updateUIWithNotifications(updatedNotifications);\n      this.messageService.markAsRead(validIds).pipe(takeUntil(this.destroy$)).subscribe({\n        next: result => {\n          if (result && result.success) {\n            if (this.error && this.error.message.includes('mark')) {\n              this.error = null;\n            }\n          }\n        },\n        error: err => {\n          // Ne pas définir d'erreur pour éviter de perturber l'interface utilisateur\n        }\n      });\n    });\n  }\n  hasNotifications() {\n    return this.notifications$.pipe(map(notifications => notifications?.length > 0));\n  }\n  hasUnreadNotifications() {\n    return this.unreadCount$.pipe(map(count => count > 0));\n  }\n  /**\n   * Active/désactive le filtre pour n'afficher que les notifications non lues\n   */\n  toggleUnreadFilter() {\n    this.showOnlyUnread = !this.showOnlyUnread;\n    if (this.showOnlyUnread) {\n      this.filteredNotifications$ = this.messageService.getUnreadNotifications();\n    } else {\n      this.filteredNotifications$ = this.notifications$;\n    }\n  }\n  /**\n   * Active/désactive le son des notifications\n   */\n  toggleSound() {\n    this.isSoundMuted = !this.isSoundMuted;\n    this.messageService.setMuted(this.isSoundMuted);\n    if (!this.isSoundMuted) {\n      setTimeout(() => {\n        this.messageService.playNotificationSound();\n        setTimeout(() => {\n          this.messageService.playNotificationSound();\n        }, 1000);\n      }, 100);\n    }\n    localStorage.setItem('notificationSoundMuted', this.isSoundMuted.toString());\n  }\n  /**\n   * Récupère les pièces jointes d'une notification et ouvre le modal\n   * @param notificationId ID de la notification\n   */\n  getNotificationAttachments(notificationId) {\n    if (!notificationId) return;\n    this.currentAttachments = [];\n    this.loadingAttachments = true;\n    this.showAttachmentsModal = true;\n    let notification;\n    this.notifications$.pipe(take(1)).subscribe(notifications => {\n      notification = notifications.find(n => n.id === notificationId);\n    });\n    if (notification && notification.message && notification.message.attachments && notification.message.attachments.length > 0) {\n      this.loadingAttachments = false;\n      this.currentAttachments = notification.message.attachments.map(attachment => ({\n        id: '',\n        url: attachment.url || '',\n        type: this.convertAttachmentTypeToMessageType(attachment.type),\n        name: attachment.name || '',\n        size: attachment.size || 0,\n        duration: 0\n      }));\n      return;\n    }\n    this.messageService.getNotificationAttachments(notificationId).pipe(takeUntil(this.destroy$)).subscribe({\n      next: attachments => {\n        this.loadingAttachments = false;\n        this.currentAttachments = attachments;\n      },\n      error: err => {\n        this.loadingAttachments = false;\n      }\n    });\n  }\n  /**\n   * Ferme le modal des pièces jointes\n   */\n  closeAttachmentsModal() {\n    this.showAttachmentsModal = false;\n  }\n  /**\n   * Ouvre le modal des détails de notification\n   * @param notification Notification à afficher\n   */\n  openNotificationDetails(notification) {\n    this.currentNotification = notification;\n    this.showNotificationDetailsModal = true;\n    if (notification.message?.attachments?.length) {\n      this.getNotificationAttachmentsForModal(notification.id);\n    }\n  }\n  /**\n   * Ferme le modal des détails de notification\n   */\n  closeNotificationDetailsModal() {\n    this.showNotificationDetailsModal = false;\n    this.currentNotification = null;\n    this.currentAttachments = [];\n  }\n  /**\n   * Récupère les pièces jointes d'une notification pour le modal de détails\n   */\n  getNotificationAttachmentsForModal(notificationId) {\n    this.currentAttachments = [];\n    if (this.currentNotification?.message?.attachments?.length) {\n      this.currentAttachments = this.currentNotification.message.attachments.map(attachment => ({\n        id: '',\n        url: attachment.url || '',\n        type: this.convertAttachmentTypeToMessageType(attachment.type),\n        name: attachment.name || '',\n        size: attachment.size || 0,\n        duration: 0\n      }));\n    }\n  }\n  /**\n   * Convertit AttachmentType en MessageType\n   */\n  convertAttachmentTypeToMessageType(type) {\n    switch (type) {\n      case 'IMAGE':\n      case 'image':\n        return MessageType.IMAGE;\n      case 'VIDEO':\n      case 'video':\n        return MessageType.VIDEO;\n      case 'AUDIO':\n      case 'audio':\n        return MessageType.AUDIO;\n      case 'FILE':\n      case 'file':\n        return MessageType.FILE;\n      default:\n        return MessageType.FILE;\n    }\n  }\n  /**\n   * Vérifie si un type de fichier est une image\n   */\n  isImage(type) {\n    return type?.startsWith('image/') || false;\n  }\n  /**\n   * Obtient l'icône FontAwesome correspondant au type de fichier\n   * @param type Type MIME du fichier\n   * @returns Classe CSS de l'icône\n   */\n  getFileIcon(type) {\n    if (!type) return 'fas fa-file';\n    if (type.startsWith('image/')) return 'fas fa-file-image';\n    if (type.startsWith('video/')) return 'fas fa-file-video';\n    if (type.startsWith('audio/')) return 'fas fa-file-audio';\n    if (type.startsWith('text/')) return 'fas fa-file-alt';\n    if (type.includes('pdf')) return 'fas fa-file-pdf';\n    if (type.includes('word') || type.includes('document')) return 'fas fa-file-word';\n    if (type.includes('excel') || type.includes('sheet')) return 'fas fa-file-excel';\n    if (type.includes('powerpoint') || type.includes('presentation')) return 'fas fa-file-powerpoint';\n    if (type.includes('zip') || type.includes('compressed')) return 'fas fa-file-archive';\n    return 'fas fa-file';\n  }\n  /**\n   * Obtient le libellé du type de fichier\n   * @param type Type MIME du fichier\n   * @returns Libellé du type de fichier\n   */\n  getFileTypeLabel(type) {\n    if (!type) return 'Fichier';\n    if (type.startsWith('image/')) return 'Image';\n    if (type.startsWith('video/')) return 'Vidéo';\n    if (type.startsWith('audio/')) return 'Audio';\n    if (type.startsWith('text/')) return 'Texte';\n    if (type.includes('pdf')) return 'PDF';\n    if (type.includes('word') || type.includes('document')) return 'Document';\n    if (type.includes('excel') || type.includes('sheet')) return 'Feuille de calcul';\n    if (type.includes('powerpoint') || type.includes('presentation')) return 'Présentation';\n    if (type.includes('zip') || type.includes('compressed')) return 'Archive';\n    return 'Fichier';\n  }\n  /**\n   * Formate la taille du fichier en unités lisibles\n   * @param size Taille en octets\n   * @returns Taille formatée (ex: \"1.5 MB\")\n   */\n  formatFileSize(size) {\n    if (!size) return '';\n    const units = ['B', 'KB', 'MB', 'GB', 'TB'];\n    let i = 0;\n    let formattedSize = size;\n    while (formattedSize >= 1024 && i < units.length - 1) {\n      formattedSize /= 1024;\n      i++;\n    }\n    return `${formattedSize.toFixed(1)} ${units[i]}`;\n  }\n  /**\n   * Ouvre une pièce jointe dans un nouvel onglet\n   * @param url URL de la pièce jointe\n   */\n  openAttachment(url) {\n    if (!url) return;\n    window.open(url, '_blank');\n  }\n  /**\n   * Télécharge une pièce jointe\n   * @param attachment Pièce jointe à télécharger\n   */\n  downloadAttachment(attachment) {\n    if (!attachment?.url) return;\n    const link = document.createElement('a');\n    link.href = attachment.url;\n    link.download = attachment.name || 'attachment';\n    link.target = '_blank';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  }\n  acceptFriendRequest(notification) {\n    this.markAsRead(notification.id);\n  }\n  /**\n   * Supprime une notification et la stocke dans le localStorage\n   * @param notificationId ID de la notification à supprimer\n   */\n  deleteNotification(notificationId) {\n    if (!notificationId) {\n      this.error = new Error('ID de notification invalide');\n      return;\n    }\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    deletedNotificationIds.add(notificationId);\n    this.saveDeletedNotificationIds(deletedNotificationIds);\n    this.messageService.deleteNotification(notificationId).pipe(takeUntil(this.destroy$)).subscribe({\n      next: result => {\n        if (result && result.success) {\n          if (this.error && this.error.message.includes('suppression')) {\n            this.error = null;\n          }\n        }\n      },\n      error: err => {\n        this.error = err;\n      }\n    });\n  }\n  /**\n   * Supprime toutes les notifications et les stocke dans le localStorage\n   */\n  deleteAllNotifications() {\n    console.log('Suppression de toutes les notifications');\n    // Récupérer toutes les notifications actuelles\n    this.notifications$.pipe(take(1)).subscribe(notifications => {\n      // Récupérer les IDs des notifications supprimées du localStorage\n      const deletedNotificationIds = this.getDeletedNotificationIds();\n      // Ajouter tous les IDs des notifications actuelles\n      notifications.forEach(notification => {\n        deletedNotificationIds.add(notification.id);\n      });\n      // Sauvegarder les IDs dans le localStorage\n      this.saveDeletedNotificationIds(deletedNotificationIds);\n      // Appeler le service pour supprimer toutes les notifications\n      this.messageService.deleteAllNotifications().pipe(takeUntil(this.destroy$)).subscribe({\n        next: result => {\n          console.log('Résultat de la suppression de toutes les notifications:', result);\n          if (result && result.success) {\n            console.log(`${result.count} notifications supprimées avec succès`);\n            // Si l'erreur était liée à cette opération, la réinitialiser\n            if (this.error && this.error.message.includes('suppression')) {\n              this.error = null;\n            }\n          }\n        },\n        error: err => {\n          console.error('Erreur lors de la suppression de toutes les notifications:', err);\n          // Même en cas d'erreur, conserver les IDs dans le localStorage\n          this.error = err;\n        }\n      });\n    });\n  }\n  getErrorMessage() {\n    return this.error?.message || 'Unknown error occurred';\n  }\n  /**\n   * Récupère les IDs des notifications supprimées du localStorage\n   * @returns Set contenant les IDs des notifications supprimées\n   */\n  getDeletedNotificationIds() {\n    try {\n      const deletedIdsJson = localStorage.getItem('deletedNotificationIds');\n      if (deletedIdsJson) {\n        return new Set(JSON.parse(deletedIdsJson));\n      }\n      return new Set();\n    } catch (error) {\n      console.error('Erreur lors de la récupération des IDs de notifications supprimées:', error);\n      return new Set();\n    }\n  }\n  /**\n   * Sauvegarde les IDs des notifications supprimées dans le localStorage\n   * @param deletedIds Set contenant les IDs des notifications supprimées\n   */\n  saveDeletedNotificationIds(deletedIds) {\n    try {\n      localStorage.setItem('deletedNotificationIds', JSON.stringify(Array.from(deletedIds)));\n      console.log(`${deletedIds.size} IDs de notifications supprimées sauvegardés dans le localStorage`);\n    } catch (error) {\n      console.error('Erreur lors de la sauvegarde des IDs de notifications supprimées:', error);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  /**\n   * Sélectionne ou désélectionne une notification\n   * @param notificationId ID de la notification\n   * @param event Événement de la case à cocher\n   */\n  toggleSelection(notificationId, event) {\n    event.stopPropagation(); // Empêcher la propagation de l'événement\n    if (this.selectedNotifications.has(notificationId)) {\n      this.selectedNotifications.delete(notificationId);\n    } else {\n      this.selectedNotifications.add(notificationId);\n    }\n    // Mettre à jour l'état de sélection globale\n    this.updateSelectionState();\n    // Afficher ou masquer la barre de sélection\n    this.showSelectionBar = this.selectedNotifications.size > 0;\n  }\n  /**\n   * Sélectionne ou désélectionne toutes les notifications\n   * @param event Événement de la case à cocher\n   */\n  toggleSelectAll(event) {\n    event.stopPropagation(); // Empêcher la propagation de l'événement\n    this.allSelected = !this.allSelected;\n    this.filteredNotifications$.pipe(take(1)).subscribe(notifications => {\n      if (this.allSelected) {\n        // Sélectionner toutes les notifications\n        notifications.forEach(notification => {\n          this.selectedNotifications.add(notification.id);\n        });\n      } else {\n        // Désélectionner toutes les notifications\n        this.selectedNotifications.clear();\n      }\n      // Afficher ou masquer la barre de sélection\n      this.showSelectionBar = this.selectedNotifications.size > 0;\n    });\n  }\n  /**\n   * Met à jour l'état de sélection globale\n   */\n  updateSelectionState() {\n    this.filteredNotifications$.pipe(take(1)).subscribe(notifications => {\n      this.allSelected = notifications.length > 0 && this.selectedNotifications.size === notifications.length;\n    });\n  }\n  /**\n   * Supprime les notifications sélectionnées\n   */\n  deleteSelectedNotifications() {\n    if (this.selectedNotifications.size === 0) {\n      return;\n    }\n    const selectedIds = Array.from(this.selectedNotifications);\n    console.log('Suppression des notifications sélectionnées:', selectedIds);\n    // Supprimer localement les notifications sélectionnées\n    this.notifications$.pipe(take(1)).subscribe(notifications => {\n      const updatedNotifications = notifications.filter(notification => !this.selectedNotifications.has(notification.id));\n      // Mettre à jour l'interface utilisateur immédiatement\n      this.updateUIWithNotifications(updatedNotifications);\n      // Réinitialiser la sélection\n      this.resetSelection();\n    });\n    // Appeler le service pour supprimer les notifications sélectionnées\n    this.messageService.deleteMultipleNotifications(selectedIds).pipe(takeUntil(this.destroy$)).subscribe({\n      next: result => {\n        console.log('Résultat de la suppression multiple:', result);\n        if (result && result.success) {\n          console.log(`${result.count} notifications supprimées avec succès`);\n        }\n      },\n      error: err => {\n        console.error('Erreur lors de la suppression multiple des notifications:', err);\n      }\n    });\n  }\n  /**\n   * Marque les notifications sélectionnées comme lues\n   */\n  markSelectedAsRead() {\n    if (this.selectedNotifications.size === 0) {\n      return;\n    }\n    const selectedIds = Array.from(this.selectedNotifications);\n    console.log('Marquage des notifications sélectionnées comme lues:', selectedIds);\n    // Marquer localement les notifications sélectionnées comme lues\n    this.notifications$.pipe(take(1)).subscribe(notifications => {\n      const updatedNotifications = notifications.map(notification => this.selectedNotifications.has(notification.id) ? {\n        ...notification,\n        isRead: true,\n        readAt: new Date().toISOString()\n      } : notification);\n      // Mettre à jour l'interface utilisateur immédiatement\n      this.updateUIWithNotifications(updatedNotifications);\n      // Réinitialiser la sélection\n      this.resetSelection();\n    });\n    // Appeler le service pour marquer les notifications comme lues\n    this.messageService.markAsRead(selectedIds).pipe(takeUntil(this.destroy$)).subscribe({\n      next: result => {\n        console.log('Résultat du marquage comme lu:', result);\n        if (result && result.success) {\n          console.log('Notifications marquées comme lues avec succès');\n        }\n      },\n      error: err => {\n        console.error('Erreur lors du marquage des notifications comme lues:', err);\n      }\n    });\n  }\n  /**\n   * Vérifie si une notification est sélectionnée\n   * @param notificationId ID de la notification\n   * @returns true si la notification est sélectionnée, false sinon\n   */\n  isSelected(notificationId) {\n    return this.selectedNotifications.has(notificationId);\n  }\n  static {\n    this.ɵfac = function NotificationListComponent_Factory(t) {\n      return new (t || NotificationListComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.ThemeService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NotificationListComponent,\n      selectors: [[\"app-notification-list\"]],\n      viewQuery: function NotificationListComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.notificationContainer = _t.first);\n        }\n      },\n      hostBindings: function NotificationListComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"scroll\", function NotificationListComponent_scroll_HostBindingHandler($event) {\n            return ctx.onScroll($event.target);\n          });\n        }\n      },\n      decls: 37,\n      vars: 22,\n      consts: [[1, \"futuristic-notifications-container\", \"main-grid-container\"], [1, \"background-elements\", \"background-grid\"], [1, \"futuristic-notifications-card\", \"content-card\", \"relative\", \"z-10\"], [1, \"futuristic-notifications-header\"], [1, \"futuristic-title\"], [1, \"fas\", \"fa-bell\", \"mr-2\"], [\"class\", \"flex space-x-2\", 4, \"ngIf\"], [\"class\", \"flex space-x-2 selection-actions\", 4, \"ngIf\"], [\"class\", \"futuristic-loading-container\", 4, \"ngIf\"], [\"class\", \"futuristic-error-message\", 4, \"ngIf\"], [\"class\", \"futuristic-empty-state\", 4, \"ngIf\"], [\"class\", \"futuristic-notifications-list\", 3, \"scroll\", 4, \"ngIf\"], [1, \"futuristic-modal-overlay\", 3, \"click\"], [1, \"futuristic-modal-container\", 3, \"click\"], [1, \"futuristic-modal-header\"], [1, \"futuristic-modal-title\"], [1, \"fas\", \"fa-paperclip\", \"mr-2\"], [1, \"futuristic-modal-close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"futuristic-modal-body\"], [\"class\", \"futuristic-attachments-list\", 4, \"ngIf\"], [1, \"fas\", \"fa-info-circle\", \"mr-2\"], [\"class\", \"futuristic-modal-body\", 4, \"ngIf\"], [1, \"flex\", \"space-x-2\"], [\"title\", \"Rafra\\u00EEchir\", 1, \"futuristic-action-button\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [\"class\", \"select-all-checkbox\", 4, \"ngIf\"], [\"title\", \"Filtrer les non lues\", 1, \"futuristic-action-button\", 3, \"click\"], [1, \"fas\", \"fa-filter\"], [1, \"futuristic-action-button\", 3, \"title\", \"click\"], [1, \"fas\", 3, \"ngClass\"], [\"class\", \"futuristic-primary-button\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"futuristic-danger-button\", \"title\", \"Supprimer toutes les notifications\", 3, \"click\", 4, \"ngIf\"], [1, \"select-all-checkbox\"], [1, \"futuristic-checkbox\"], [\"type\", \"checkbox\", 3, \"checked\", \"click\"], [1, \"checkmark\"], [1, \"futuristic-primary-button\", 3, \"click\"], [1, \"fas\", \"fa-check-double\", \"mr-1\"], [\"title\", \"Supprimer toutes les notifications\", 1, \"futuristic-danger-button\", 3, \"click\"], [1, \"fas\", \"fa-trash-alt\", \"mr-1\"], [1, \"flex\", \"space-x-2\", \"selection-actions\"], [1, \"selection-count\"], [1, \"fas\", \"fa-check\", \"mr-1\"], [1, \"futuristic-danger-button\", 3, \"click\"], [1, \"futuristic-cancel-button\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"mr-1\"], [1, \"futuristic-loading-container\"], [1, \"futuristic-loading-circle\"], [1, \"futuristic-loading-text\"], [1, \"futuristic-error-message\"], [1, \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-triangle\", \"futuristic-error-icon\"], [1, \"futuristic-error-title\"], [1, \"futuristic-error-text\"], [1, \"futuristic-retry-button\", \"ml-auto\", 3, \"click\"], [1, \"futuristic-empty-state\"], [1, \"futuristic-empty-icon\"], [1, \"fas\", \"fa-bell-slash\"], [1, \"futuristic-empty-title\"], [1, \"futuristic-empty-text\"], [1, \"futuristic-check-button\", 3, \"click\"], [1, \"futuristic-notifications-list\", 3, \"scroll\"], [\"notificationContainer\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"futuristic-loading-more\", 4, \"ngIf\"], [1, \"futuristic-notification-card\"], [1, \"notification-checkbox\"], [1, \"notification-avatar\"], [\"alt\", \"Avatar\", \"onerror\", \"this.src='assets/images/default-avatar.png'\", 3, \"src\"], [1, \"notification-main-content\"], [1, \"notification-content\"], [1, \"notification-header\"], [1, \"notification-header-top\"], [1, \"notification-sender\"], [1, \"notification-time\"], [1, \"notification-text-container\"], [1, \"notification-text\"], [\"class\", \"notification-message-preview\", 4, \"ngIf\"], [\"class\", \"notification-attachments-indicator\", 4, \"ngIf\"], [\"class\", \"unread-indicator\", 4, \"ngIf\"], [1, \"notification-actions\"], [\"class\", \"notification-action-button notification-attachment-button\", \"title\", \"Voir les pi\\u00E8ces jointes\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"notification-action-button notification-join-button\", \"title\", \"Rejoindre la conversation\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"title\", \"Voir les d\\u00E9tails (ne marque PAS comme lu automatiquement)\", 1, \"notification-action-button\", \"notification-details-button\", 3, \"click\"], [1, \"fas\", \"fa-info-circle\"], [\"class\", \"notification-action-button notification-read-button\", \"title\", \"Marquer cette notification comme lue\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Supprimer cette notification\", 1, \"notification-action-button\", \"notification-delete-button\", 3, \"click\"], [1, \"fas\", \"fa-trash-alt\"], [1, \"notification-message-preview\"], [1, \"notification-attachments-indicator\"], [1, \"fas\", \"fa-paperclip\"], [1, \"unread-indicator\"], [\"title\", \"Voir les pi\\u00E8ces jointes\", 1, \"notification-action-button\", \"notification-attachment-button\", 3, \"click\"], [\"title\", \"Rejoindre la conversation\", 1, \"notification-action-button\", \"notification-join-button\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-comments\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin\", 4, \"ngIf\"], [1, \"fas\", \"fa-comments\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [\"title\", \"Marquer cette notification comme lue\", 1, \"notification-action-button\", \"notification-read-button\", 3, \"click\"], [1, \"fas\", \"fa-check\"], [1, \"futuristic-loading-more\"], [1, \"futuristic-loading-circle-small\"], [1, \"futuristic-loading-text-small\"], [1, \"fas\", \"fa-file-alt\"], [1, \"futuristic-attachments-list\"], [\"class\", \"futuristic-attachment-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"futuristic-attachment-item\"], [\"class\", \"futuristic-attachment-preview\", 4, \"ngIf\"], [\"class\", \"futuristic-attachment-icon\", 4, \"ngIf\"], [1, \"futuristic-attachment-info\"], [1, \"futuristic-attachment-name\"], [1, \"futuristic-attachment-meta\"], [1, \"futuristic-attachment-type\"], [\"class\", \"futuristic-attachment-size\", 4, \"ngIf\"], [1, \"futuristic-attachment-actions\"], [\"title\", \"Ouvrir\", 1, \"futuristic-attachment-button\", 3, \"click\"], [1, \"fas\", \"fa-external-link-alt\"], [\"title\", \"T\\u00E9l\\u00E9charger\", 1, \"futuristic-attachment-button\", 3, \"click\"], [1, \"fas\", \"fa-download\"], [1, \"futuristic-attachment-preview\"], [\"alt\", \"Image\", 3, \"src\", \"click\"], [1, \"futuristic-attachment-icon\"], [1, \"futuristic-attachment-size\"], [1, \"notification-detail-section\"], [1, \"notification-detail-title\"], [1, \"fas\", \"fa-user\", \"mr-2\"], [1, \"notification-sender-info\"], [\"alt\", \"Avatar\", \"onerror\", \"this.src='assets/images/default-avatar.png'\", 1, \"notification-sender-avatar\", 3, \"src\"], [1, \"notification-sender-details\"], [1, \"notification-sender-name\"], [1, \"notification-timestamp\"], [1, \"fas\", \"fa-message\", \"mr-2\"], [1, \"notification-content-detail\"], [\"class\", \"notification-message-detail\", 4, \"ngIf\"], [1, \"fas\", \"fa-tag\", \"mr-2\"], [1, \"notification-info-grid\"], [1, \"notification-info-item\"], [1, \"notification-info-label\"], [1, \"notification-info-value\"], [\"class\", \"notification-info-item\", 4, \"ngIf\"], [\"class\", \"notification-info-item\", \"style\", \"\\n              background: rgba(255, 140, 0, 0.1);\\n              border: 1px solid rgba(255, 140, 0, 0.3);\\n            \", 4, \"ngIf\"], [\"class\", \"notification-detail-section\", 4, \"ngIf\"], [1, \"notification-detail-actions\"], [\"class\", \"futuristic-primary-button\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"futuristic-secondary-button\", 3, \"click\", 4, \"ngIf\"], [1, \"fas\", \"fa-trash-alt\", \"mr-2\"], [1, \"notification-message-detail\"], [1, \"notification-info-item\", 2, \"background\", \"rgba(255, 140, 0, 0.1)\", \"border\", \"1px solid rgba(255, 140, 0, 0.3)\"], [1, \"fas\", \"fa-info-circle\", \"mr-1\"], [1, \"notification-info-value\", 2, \"color\", \"#ff8c00\", \"font-style\", \"italic\"], [1, \"notification-attachments-grid\"], [\"class\", \"notification-attachment-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"notification-attachment-item\"], [\"class\", \"notification-attachment-preview\", 4, \"ngIf\"], [\"class\", \"notification-attachment-icon\", 4, \"ngIf\"], [1, \"notification-attachment-info\"], [1, \"notification-attachment-name\"], [1, \"notification-attachment-meta\"], [1, \"notification-attachment-type\"], [\"class\", \"notification-attachment-size\", 4, \"ngIf\"], [1, \"notification-attachment-actions\"], [\"title\", \"Ouvrir\", 1, \"notification-attachment-button\", 3, \"click\"], [\"title\", \"T\\u00E9l\\u00E9charger\", 1, \"notification-attachment-button\", 3, \"click\"], [1, \"notification-attachment-preview\"], [1, \"notification-attachment-icon\"], [1, \"notification-attachment-size\"], [1, \"futuristic-primary-button\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-comments mr-2\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin mr-2\", 4, \"ngIf\"], [1, \"fas\", \"fa-comments\", \"mr-2\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"mr-2\"], [1, \"futuristic-secondary-button\", 3, \"click\"], [1, \"fas\", \"fa-check\", \"mr-2\"]],\n      template: function NotificationListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵelement(2, \"div\", 1);\n          i0.ɵɵelementStart(3, \"div\", 2)(4, \"div\", 3)(5, \"h2\", 4);\n          i0.ɵɵelement(6, \"i\", 5);\n          i0.ɵɵtext(7, \" Notifications \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, NotificationListComponent_div_8_Template, 13, 15, \"div\", 6);\n          i0.ɵɵtemplate(9, NotificationListComponent_div_9_Template, 12, 1, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, NotificationListComponent_div_10_Template, 4, 0, \"div\", 8);\n          i0.ɵɵtemplate(11, NotificationListComponent_div_11_Template, 10, 1, \"div\", 9);\n          i0.ɵɵtemplate(12, NotificationListComponent_div_12_Template, 9, 0, \"div\", 10);\n          i0.ɵɵpipe(13, \"async\");\n          i0.ɵɵtemplate(14, NotificationListComponent_div_14_Template, 5, 4, \"div\", 11);\n          i0.ɵɵpipe(15, \"async\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 12);\n          i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_div_click_16_listener() {\n            return ctx.closeAttachmentsModal();\n          });\n          i0.ɵɵelementStart(17, \"div\", 13);\n          i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_div_click_17_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(18, \"div\", 14)(19, \"h3\", 15);\n          i0.ɵɵelement(20, \"i\", 16);\n          i0.ɵɵtext(21, \" Pi\\u00E8ces jointes \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_button_click_22_listener() {\n            return ctx.closeAttachmentsModal();\n          });\n          i0.ɵɵelement(23, \"i\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"div\", 19);\n          i0.ɵɵtemplate(25, NotificationListComponent_div_25_Template, 4, 0, \"div\", 8);\n          i0.ɵɵtemplate(26, NotificationListComponent_div_26_Template, 7, 0, \"div\", 10);\n          i0.ɵɵtemplate(27, NotificationListComponent_div_27_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"div\", 12);\n          i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_div_click_28_listener() {\n            return ctx.closeNotificationDetailsModal();\n          });\n          i0.ɵɵelementStart(29, \"div\", 13);\n          i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_div_click_29_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(30, \"div\", 14)(31, \"h3\", 15);\n          i0.ɵɵelement(32, \"i\", 21);\n          i0.ɵɵtext(33, \" D\\u00E9tails de la notification \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_button_click_34_listener() {\n            return ctx.closeNotificationDetailsModal();\n          });\n          i0.ɵɵelement(35, \"i\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(36, NotificationListComponent_div_36_Template, 44, 19, \"div\", 22);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"dark\", i0.ɵɵpipeBind1(1, 16, ctx.isDarkMode$));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", !ctx.showSelectionBar);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showSelectionBar);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !i0.ɵɵpipeBind1(13, 18, ctx.hasNotifications()));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && i0.ɵɵpipeBind1(15, 20, ctx.hasNotifications()));\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"display\", ctx.showAttachmentsModal ? \"flex\" : \"none\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.loadingAttachments);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loadingAttachments && ctx.currentAttachments.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loadingAttachments && ctx.currentAttachments.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"display\", ctx.showNotificationDetailsModal ? \"flex\" : \"none\");\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentNotification);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.AsyncPipe, i4.DatePipe],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n.futuristic-notifications-container[_ngcontent-%COMP%] {\\n  padding: 0;\\n  min-height: calc(100vh - 4rem);\\n  position: relative;\\n  overflow: hidden;\\n  width: 100%;\\n  display: flex;\\n  justify-content: center;\\n  padding-top: 1rem; \\n\\n  margin-bottom: 0;\\n  height: 100vh; \\n\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-container[_ngcontent-%COMP%] {\\n  background-color: #edf1f4; \\n\\n  color: #6d6870;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notifications-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notifications-container[_ngcontent-%COMP%] {\\n  background-color: #121212; \\n\\n  color: #a0a0a0;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.futuristic-notifications-container[_ngcontent-%COMP%]   .background-elements[_ngcontent-%COMP%] {\\n  position: absolute;\\n  inset: 0;\\n  overflow: hidden;\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]::before, :not(.dark)   [_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  opacity: 0.05;\\n  background-image: linear-gradient(to right, #4f5fad 1px, transparent 1px),\\n    linear-gradient(to bottom, #4f5fad 1px, transparent 1px);\\n  background-size: calc(100% / 12) 100%, 100% calc(100% / 12);\\n  z-index: 0;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]::before, .dark   [_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  opacity: 0.05; \\n\\n  background-image: linear-gradient(\\n      to right,\\n      rgba(255, 140, 0, 0.3) 1px,\\n      transparent 1px\\n    ),\\n    linear-gradient(to bottom, rgba(255, 140, 0, 0.3) 1px, transparent 1px); \\n\\n  background-size: calc(100% / 20) 100%, 100% calc(100% / 20); \\n\\n  z-index: 0;\\n  animation: _ngcontent-%COMP%_grid-pulse 4s ease-in-out infinite; \\n\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]::after, .dark   [_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  left: 0;\\n  right: 0;\\n  height: 2px;\\n  background: linear-gradient(\\n    to right,\\n    transparent 0%,\\n    rgba(255, 140, 0, 0.5) 50%,\\n    transparent 100%\\n  );\\n  box-shadow: 0 0 10px rgba(255, 140, 0, 0.5);\\n  z-index: 1;\\n  animation: _ngcontent-%COMP%_scan 8s linear infinite; \\n\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_grid-pulse {\\n  0% {\\n    opacity: 0.03;\\n  }\\n  50% {\\n    opacity: 0.07;\\n  }\\n  100% {\\n    opacity: 0.03;\\n  }\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_scan {\\n  0% {\\n    top: -10%;\\n    opacity: 0.5;\\n  }\\n  50% {\\n    opacity: 0.8;\\n  }\\n  100% {\\n    top: 110%;\\n    opacity: 0.5;\\n  }\\n}\\n\\n\\n\\n.futuristic-notifications-card[_ngcontent-%COMP%] {\\n  border-radius: 0.5rem;\\n  overflow: hidden;\\n  position: relative;\\n  z-index: 1;\\n  margin: 0.5rem auto; \\n\\n  display: flex;\\n  flex-direction: column;\\n  width: 100%;\\n  max-width: 1200px; \\n\\n  height: calc(100vh - 1rem); \\n\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-card[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-card[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  border: 1px solid rgba(79, 95, 173, 0.1);\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notifications-card[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notifications-card[_ngcontent-%COMP%] {\\n  background-color: #1e1e1e; \\n\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\\n  border: 1px solid rgba(109, 120, 201, 0.1); \\n\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n\\n\\n.futuristic-notifications-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1rem;\\n  position: sticky;\\n  top: 0;\\n  z-index: 10;\\n  margin-bottom: 1.5rem; \\n\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-header[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-header[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid rgba(79, 95, 173, 0.1);\\n  background-color: rgba(240, 244, 248, 0.5);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notifications-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notifications-header[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid rgba(0, 247, 255, 0.1);\\n  background-color: rgba(0, 0, 0, 0.2);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #4f5fad;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: var(--accent-color);\\n  display: flex;\\n  align-items: center;\\n}\\n\\n\\n\\n.futuristic-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  \\n\\n  position: relative;\\n  top: 0px; \\n\\n  \\n\\n  width: 22px;\\n  height: 22px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  \\n\\n  border-radius: 50%;\\n  background: rgba(0, 247, 255, 0.1);\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n  box-shadow: 0 0 8px rgba(0, 247, 255, 0.3);\\n  transition: all 0.3s ease;\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  background: rgba(79, 95, 173, 0.1);\\n  border: 1px solid rgba(79, 95, 173, 0.3);\\n  box-shadow: 0 0 8px rgba(79, 95, 173, 0.3);\\n  color: #4f5fad;\\n}\\n\\n\\n\\n.futuristic-title[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n  box-shadow: 0 0 12px rgba(0, 247, 255, 0.5);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 12px rgba(79, 95, 173, 0.5);\\n}\\n\\n\\n\\n.futuristic-loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 4rem 2rem;\\n}\\n\\n.futuristic-loading-circle[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  border: 2px solid transparent;\\n  border-top-color: var(--accent-color);\\n  border-bottom-color: var(--secondary-color);\\n  animation: _ngcontent-%COMP%_futuristic-spin 1.2s linear infinite;\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.3);\\n}\\n\\n@keyframes _ngcontent-%COMP%_futuristic-spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.futuristic-loading-text[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n  color: var(--text-dim);\\n  font-size: 0.875rem;\\n  text-align: center;\\n}\\n\\n\\n\\n.futuristic-error-message[_ngcontent-%COMP%] {\\n  margin: 1rem;\\n  padding: 1rem;\\n  background-color: rgba(255, 0, 76, 0.1);\\n  border-left: 4px solid var(--error-color);\\n  border-radius: var(--border-radius-md);\\n}\\n\\n.futuristic-error-icon[_ngcontent-%COMP%] {\\n  color: var(--error-color);\\n  font-size: 1.25rem;\\n  margin-right: 0.75rem;\\n}\\n\\n.futuristic-error-title[_ngcontent-%COMP%] {\\n  color: var(--error-color);\\n  font-weight: 600;\\n  font-size: 0.875rem;\\n}\\n\\n.futuristic-error-text[_ngcontent-%COMP%] {\\n  color: var(--text-dim);\\n  font-size: 0.75rem;\\n  margin-top: 0.25rem;\\n}\\n\\n.futuristic-retry-button[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.75rem;\\n  background-color: rgba(255, 0, 76, 0.1);\\n  color: var(--error-color);\\n  border: 1px solid var(--error-color);\\n  border-radius: var(--border-radius-sm);\\n  font-size: 0.75rem;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n\\n.futuristic-retry-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 0, 76, 0.2);\\n  box-shadow: 0 0 10px rgba(255, 0, 76, 0.3);\\n}\\n\\n\\n\\n.futuristic-empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 4rem 2rem;\\n  text-align: center;\\n}\\n\\n.futuristic-empty-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  color: var(--accent-color);\\n  margin-bottom: 1rem;\\n  opacity: 0.5;\\n}\\n\\n.futuristic-empty-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: var(--text-light);\\n  margin-bottom: 0.5rem;\\n}\\n\\n.futuristic-empty-text[_ngcontent-%COMP%] {\\n  color: var(--text-dim);\\n  font-size: 0.875rem;\\n  margin-bottom: 1rem;\\n}\\n\\n.futuristic-check-button[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  background-color: rgba(0, 247, 255, 0.1);\\n  color: var(--accent-color);\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n  border-radius: var(--border-radius-md);\\n  font-size: 0.875rem;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n\\n.futuristic-check-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.2);\\n  transform: translateY(-2px);\\n  box-shadow: var(--glow-effect);\\n}\\n\\n\\n\\n.futuristic-notifications-list[_ngcontent-%COMP%] {\\n  padding: 0;\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n  overflow-y: auto;\\n  position: relative;\\n  scrollbar-width: thin;\\n  z-index: 1;\\n  width: 100%;\\n}\\n\\n.futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n\\n.futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%] {\\n  scrollbar-color: #4f5fad transparent;\\n  background-color: white;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background-color: #4f5fad;\\n  border-radius: 10px;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%] {\\n  scrollbar-color: var(--accent-color) transparent;\\n  background-color: transparent;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, .dark   [_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background-color: var(--accent-color);\\n  border-radius: 10px;\\n}\\n\\n\\n\\n.futuristic-notification-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 30px 20px 16px 20px; \\n\\n  position: relative;\\n  transition: all 0.2s ease;\\n  margin: 0.5rem 1rem; \\n\\n  border-radius: 8px; \\n\\n  flex-wrap: nowrap; \\n\\n  justify-content: space-between; \\n\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid rgba(79, 95, 173, 0.1);\\n  background-color: white;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n  border-radius: 15px; \\n\\n  transition: all 0.3s ease;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(79, 95, 173, 0.05);\\n  transform: translateY(-1px);\\n  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%] {\\n  background: linear-gradient(\\n    135deg,\\n    rgba(79, 95, 173, 0.1),\\n    rgba(61, 74, 133, 0.2)\\n  ); \\n\\n  border: 1px solid rgba(79, 95, 173, 0.3); \\n\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); \\n\\n  border-bottom-right-radius: 0; \\n\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]::after, :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  border-radius: inherit;\\n  pointer-events: none;\\n  z-index: -1;\\n  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.2); \\n\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  background: linear-gradient(\\n    135deg,\\n    rgba(79, 95, 173, 0.15),\\n    rgba(61, 74, 133, 0.25)\\n  ); \\n\\n  box-shadow: 0 4px 12px rgba(79, 95, 173, 0.2); \\n\\n}\\n\\n\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%] {\\n  border-bottom: none;\\n  background-color: var(\\n    --dark-medium-bg,\\n    #252740\\n  ); \\n\\n  border: 1px solid rgba(255, 255, 255, 0.1); \\n\\n  border-radius: 15px; \\n\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2); \\n\\n  margin-bottom: 15px; \\n\\n  margin-left: 15px; \\n\\n  margin-right: 15px; \\n\\n  transition: all 0.3s ease; \\n\\n  color: var(\\n    --text-light,\\n    #ffffff\\n  ); \\n\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(\\n    -2px\\n  ); \\n\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3); \\n\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n  background: linear-gradient(\\n    135deg,\\n    #00f7ff20,\\n    #00c3ff30\\n  ); \\n\\n  border: 1px solid rgba(0, 247, 255, 0.3); \\n\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2); \\n\\n  border-bottom-right-radius: 0; \\n\\n  transition: all 0.3s ease;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]::after, .dark   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  border-radius: inherit;\\n  pointer-events: none;\\n  z-index: -1;\\n  box-shadow: inset 0 0 0 1px rgba(0, 247, 255, 0.3); \\n\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 15px rgba(0, 247, 255, 0.2); \\n\\n  background: linear-gradient(\\n    135deg,\\n    #00f7ff30,\\n    #00c3ff40\\n  ); \\n\\n}\\n\\n\\n\\n.futuristic-notification-unread[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_scaleIn {\\n  from {\\n    transform: scale(0.9);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_borderFlow {\\n  0% {\\n    background-position: 0% 0%;\\n  }\\n  100% {\\n    background-position: 200% 0%;\\n  }\\n}\\n\\n\\n\\n.futuristic-modal-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-color: rgba(0, 0, 0, 0.7);\\n  -webkit-backdrop-filter: blur(5px);\\n          backdrop-filter: blur(5px);\\n  display: none; \\n\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 1000;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n  opacity: 0; \\n\\n  transition: opacity 0.3s ease;\\n}\\n\\n\\n\\n.futuristic-modal-overlay[style*=\\\"display: flex\\\"][_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-modal-overlay[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-overlay[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.8);\\n  -webkit-backdrop-filter: blur(8px);\\n          backdrop-filter: blur(8px);\\n  animation: _ngcontent-%COMP%_modalBackdropFadeIn 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_modalBackdropFadeIn {\\n  from {\\n    background-color: rgba(0, 0, 0, 0);\\n    -webkit-backdrop-filter: blur(0px);\\n            backdrop-filter: blur(0px);\\n  }\\n  to {\\n    background-color: rgba(0, 0, 0, 0.8);\\n    -webkit-backdrop-filter: blur(8px);\\n            backdrop-filter: blur(8px);\\n  }\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%] {\\n  width: 90%;\\n  max-width: 600px;\\n  max-height: 80vh;\\n  background-color: #ffffff;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\\n  display: flex;\\n  flex-direction: column;\\n  animation: _ngcontent-%COMP%_scaleIn 0.3s ease;\\n  border: 1px solid rgba(79, 95, 173, 0.2);\\n  position: relative;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]::before, :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(\\n    90deg,\\n    transparent 0%,\\n    rgba(79, 95, 173, 0.2) 20%,\\n    rgba(79, 95, 173, 0.8) 50%,\\n    rgba(79, 95, 173, 0.2) 80%,\\n    transparent 100%\\n  );\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_borderFlow 3s infinite linear;\\n  box-shadow: 0 0 10px rgba(79, 95, 173, 0.4);\\n  z-index: 1;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%] {\\n  width: 90%;\\n  max-width: 600px;\\n  max-height: 80vh;\\n  background-color: rgba(18, 18, 18, 0.95);\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 0 40px rgba(0, 247, 255, 0.4),\\n    inset 0 0 20px rgba(0, 247, 255, 0.1);\\n  display: flex;\\n  flex-direction: column;\\n  animation: _ngcontent-%COMP%_modalFadeIn 0.4s cubic-bezier(0.19, 1, 0.22, 1);\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n  position: relative;\\n}\\n\\n@keyframes _ngcontent-%COMP%_modalFadeIn {\\n  0% {\\n    opacity: 0;\\n    transform: scale(0.9) translateY(20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: scale(1) translateY(0);\\n  }\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]::after, .dark   [_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(\\n    90deg,\\n    transparent 0%,\\n    rgba(0, 247, 255, 0.2) 20%,\\n    rgba(0, 247, 255, 0.8) 50%,\\n    rgba(0, 247, 255, 0.2) 80%,\\n    transparent 100%\\n  );\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_borderFlow 3s infinite linear;\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.7);\\n  z-index: 1;\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-modal-header[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-header[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  border-bottom: 1px solid rgba(79, 95, 173, 0.1);\\n  background-color: rgba(79, 95, 173, 0.05);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-modal-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #4f5fad;\\n  margin: 0;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-color: rgba(79, 95, 173, 0.1);\\n  color: #4f5fad;\\n  border: none;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(79, 95, 173, 0.2);\\n  transform: scale(1.1);\\n  box-shadow: 0 0 10px rgba(79, 95, 173, 0.3);\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-modal-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-header[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  border-bottom: 1px solid rgba(0, 247, 255, 0.1);\\n  background-color: rgba(0, 0, 0, 0.2);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-modal-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #00f7ff;\\n  margin: 0;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-color: rgba(0, 247, 255, 0.1);\\n  color: #00f7ff;\\n  border: none;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.2);\\n  transform: scale(1.1);\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.5);\\n}\\n\\n\\n\\n.futuristic-modal-body[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  overflow-y: auto;\\n  max-height: calc(80vh - 70px);\\n}\\n\\n\\n\\n.futuristic-attachments-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 12px;\\n  border-radius: 8px;\\n  background-color: rgba(79, 95, 173, 0.05);\\n  border: 1px solid rgba(79, 95, 173, 0.1);\\n  transition: all 0.2s ease;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(79, 95, 173, 0.1);\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-preview[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-preview[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  margin-right: 12px;\\n  flex-shrink: 0;\\n  border: 1px solid rgba(79, 95, 173, 0.2);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 4px;\\n  background-color: rgba(79, 95, 173, 0.1);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 12px;\\n  flex-shrink: 0;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #4f5fad;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-color: rgba(79, 95, 173, 0.1);\\n  color: #4f5fad;\\n  border: none;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(79, 95, 173, 0.2);\\n  transform: scale(1.1);\\n  box-shadow: 0 0 10px rgba(79, 95, 173, 0.3);\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 12px;\\n  border-radius: 8px;\\n  background-color: rgba(0, 247, 255, 0.05);\\n  border: 1px solid rgba(0, 247, 255, 0.1);\\n  transition: all 0.2s ease;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.1);\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-attachment-preview[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-preview[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  margin-right: 12px;\\n  flex-shrink: 0;\\n  border: 1px solid rgba(0, 247, 255, 0.2);\\n  box-shadow: 0 0 10px rgba(0, 247, 255, 0.2);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 4px;\\n  background-color: rgba(0, 247, 255, 0.1);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 12px;\\n  flex-shrink: 0;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #00f7ff;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-color: rgba(0, 247, 255, 0.1);\\n  color: #00f7ff;\\n  border: none;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.2);\\n  transform: scale(1.1);\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.5);\\n}\\n\\n\\n\\n.futuristic-attachment-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n}\\n\\n.futuristic-attachment-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n\\n.futuristic-attachment-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n\\n.futuristic-attachment-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.futuristic-attachment-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 0.8rem;\\n  color: var(--text-dim);\\n}\\n\\n.futuristic-attachment-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-left: 12px;\\n}\\n\\n\\n\\n.futuristic-loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 0;\\n}\\n\\n.futuristic-loading-circle[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: 3px solid transparent;\\n  margin-bottom: 16px;\\n  animation: _ngcontent-%COMP%_spin 1.2s linear infinite;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%] {\\n  border-top-color: #4f5fad;\\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.3);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%] {\\n  border-top-color: #00f7ff;\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.5);\\n}\\n\\n.futuristic-loading-text[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: var(--text-dim);\\n}\\n\\n.futuristic-empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 0;\\n  text-align: center;\\n}\\n\\n.futuristic-empty-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  margin-bottom: 16px;\\n  opacity: 0.5;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%] {\\n  color: #4f5fad;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%] {\\n  color: #00f7ff;\\n}\\n\\n.futuristic-empty-title[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 500;\\n  margin-bottom: 8px;\\n}\\n\\n.futuristic-empty-text[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: var(--text-dim);\\n  max-width: 300px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n\\n\\n.notification-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  flex-shrink: 0;\\n  margin-right: 12px;\\n  margin-left: 10px; \\n\\n}\\n\\n.notification-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n\\n\\n\\n.notification-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n  padding-right: 16px;\\n}\\n\\n.notification-header[_ngcontent-%COMP%] {\\n  margin-bottom: 6px; \\n\\n}\\n\\n.notification-header-top[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between; \\n\\n  align-items: center; \\n\\n  width: 100%; \\n\\n}\\n\\n.notification-sender[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 0.95rem; \\n\\n  color: #4f5fad;\\n  padding: 2px 0; \\n\\n  transition: all 0.3s ease; \\n\\n}\\n\\n.notification-sender[_ngcontent-%COMP%]:hover {\\n  color: #3d4a85; \\n\\n  text-shadow: 0 0 1px rgba(79, 95, 173, 0.3); \\n\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-sender[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-sender[_ngcontent-%COMP%] {\\n  color: #ff8c00; \\n\\n  text-shadow: 0 0 5px rgba(255, 140, 0, 0.3); \\n\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-sender[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .notification-sender[_ngcontent-%COMP%]:hover {\\n  color: #ffa040; \\n\\n  text-shadow: 0 0 8px rgba(255, 140, 0, 0.5); \\n\\n}\\n\\n.notification-text[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 0.9rem;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-text[_ngcontent-%COMP%] {\\n  color: #ffffff; \\n\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-notification-read[_ngcontent-%COMP%]   .notification-text[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-read[_ngcontent-%COMP%]   .notification-text[_ngcontent-%COMP%] {\\n  color: var(\\n    --light-text,\\n    #333333\\n  ); \\n\\n  font-weight: 400; \\n\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-read[_ngcontent-%COMP%]   .notification-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-read[_ngcontent-%COMP%]   .notification-text[_ngcontent-%COMP%] {\\n  color: var(\\n    --dark-text,\\n    #ffffff\\n  ); \\n\\n  font-weight: 400; \\n\\n}\\n\\n.notification-message-preview[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #666;\\n  margin-top: 4px;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  max-width: 100%;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-message-preview[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-message-preview[_ngcontent-%COMP%] {\\n  color: #cccccc; \\n\\n}\\n\\n\\n\\n.notification-attachments-indicator[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #ff8c00;\\n  margin-top: 0.25rem;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-attachments-indicator[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachments-indicator[_ngcontent-%COMP%] {\\n  color: rgba(0, 247, 255, 0.9);\\n}\\n\\n.notification-attachments-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n\\n\\n\\n\\n\\n.notification-action-button[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.9rem;\\n  transition: all 0.3s ease;\\n  margin: 6px; \\n\\n  border: none;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.notification-time[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 10px; \\n\\n  right: 10px; \\n\\n  font-size: 0.75rem; \\n\\n  color: rgba(0, 247, 255, 0.9);\\n  font-weight: 600;\\n  padding: 5px 10px; \\n\\n  border-radius: 8px;\\n  background: linear-gradient(\\n    135deg,\\n    rgba(0, 247, 255, 0.15),\\n    rgba(0, 200, 255, 0.1)\\n  );\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n  box-shadow: 0 2px 8px rgba(0, 247, 255, 0.2);\\n  z-index: 15;\\n  transition: all 0.3s ease;\\n  white-space: nowrap;\\n  \\n\\n  letter-spacing: 0.5px;\\n  text-rendering: optimizeLegibility;\\n  -webkit-font-smoothing: antialiased;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%] {\\n  background: linear-gradient(\\n    135deg,\\n    rgba(0, 247, 255, 0.25),\\n    rgba(0, 200, 255, 0.2)\\n  );\\n  border: 1px solid rgba(0, 247, 255, 0.5);\\n  text-shadow: 0 0 4px rgba(0, 247, 255, 0.3);\\n  box-shadow: 0 2px 10px rgba(0, 247, 255, 0.4);\\n  -webkit-backdrop-filter: none;\\n          backdrop-filter: none; \\n\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%] {\\n  color: #4f5fad;\\n  background: linear-gradient(\\n    135deg,\\n    rgba(79, 95, 173, 0.1),\\n    rgba(79, 95, 173, 0.05)\\n  );\\n  border: 1px solid rgba(79, 95, 173, 0.2);\\n  box-shadow: 0 2px 8px rgba(79, 95, 173, 0.15);\\n}\\n\\n\\n\\n.notification-time[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px) scale(1.05);\\n  box-shadow: 0 4px 15px rgba(0, 247, 255, 0.4);\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(\\n    135deg,\\n    rgba(0, 247, 255, 0.35),\\n    rgba(0, 200, 255, 0.25)\\n  );\\n  box-shadow: 0 4px 15px rgba(0, 247, 255, 0.6);\\n  text-shadow: 0 0 6px rgba(0, 247, 255, 0.4);\\n  border-color: rgba(0, 247, 255, 0.7);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(\\n    135deg,\\n    rgba(79, 95, 173, 0.15),\\n    rgba(79, 95, 173, 0.1)\\n  );\\n  box-shadow: 0 4px 15px rgba(79, 95, 173, 0.25);\\n}\\n\\n\\n\\n.notification-join-button[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  background: linear-gradient(135deg, #00c853, #00a843);\\n  color: white;\\n  border: 2px solid #00c853;\\n  clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);\\n  position: relative;\\n}\\n\\n.notification-join-button[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #00e676, #00c853);\\n  transform: scale(1.15) rotate(5deg);\\n  box-shadow: 0 0 15px rgba(0, 200, 83, 0.6);\\n  border-color: #00e676;\\n}\\n\\n.notification-join-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  right: -2px;\\n  bottom: -2px;\\n  background: linear-gradient(45deg, #00c853, #00e676);\\n  clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);\\n  z-index: -1;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.notification-join-button[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n\\n\\n\\n.notification-details-button[_ngcontent-%COMP%] {\\n  width: 38px;\\n  height: 38px;\\n  background: linear-gradient(135deg, #2196f3, #1976d2);\\n  color: white;\\n  border: 2px solid #2196f3;\\n  border-radius: 8px;\\n  position: relative;\\n}\\n\\n.notification-details-button[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #42a5f5, #2196f3);\\n  transform: scale(1.1) rotateY(15deg);\\n  box-shadow: 0 0 15px rgba(33, 150, 243, 0.6);\\n  border-color: #42a5f5;\\n}\\n\\n.notification-details-button[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 2px;\\n  left: 2px;\\n  right: 2px;\\n  bottom: 2px;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 6px;\\n  transition: all 0.3s ease;\\n}\\n\\n.notification-details-button[_ngcontent-%COMP%]:hover::after {\\n  background: rgba(255, 255, 255, 0.2);\\n}\\n\\n\\n\\n.notification-read-button[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  background: linear-gradient(135deg, #ffc107, #ff9800);\\n  color: white;\\n  border: 2px solid #ffc107;\\n  border-radius: 50%;\\n  transform: rotate(45deg);\\n  position: relative;\\n}\\n\\n.notification-read-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transform: rotate(-45deg);\\n}\\n\\n.notification-read-button[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #ffca28, #ffc107);\\n  transform: rotate(45deg) scale(1.15);\\n  box-shadow: 0 0 15px rgba(255, 193, 7, 0.6);\\n  border-color: #ffca28;\\n}\\n\\n.notification-read-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  width: 8px;\\n  height: 8px;\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 50%;\\n  transform: translate(-50%, -50%) rotate(-45deg);\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n\\n\\n.notification-delete-button[_ngcontent-%COMP%] {\\n  width: 38px;\\n  height: 38px;\\n  background: linear-gradient(135deg, #f44336, #d32f2f);\\n  color: white;\\n  border: 2px solid #f44336;\\n  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);\\n  position: relative;\\n}\\n\\n.notification-delete-button[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #ef5350, #f44336);\\n  transform: scale(1.15) rotate(-5deg);\\n  box-shadow: 0 0 15px rgba(244, 67, 54, 0.6);\\n  border-color: #ef5350;\\n}\\n\\n.notification-delete-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  right: -2px;\\n  bottom: -2px;\\n  background: linear-gradient(45deg, #f44336, #ef5350);\\n  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);\\n  z-index: -1;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.notification-delete-button[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n\\n\\n\\n.notification-attachment-button[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  background: linear-gradient(135deg, #9c27b0, #7b1fa2);\\n  color: white;\\n  border: 2px solid #9c27b0;\\n  clip-path: polygon(\\n    30% 0%,\\n    70% 0%,\\n    100% 30%,\\n    100% 70%,\\n    70% 100%,\\n    30% 100%,\\n    0% 70%,\\n    0% 30%\\n  );\\n  position: relative;\\n}\\n\\n.notification-attachment-button[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #ab47bc, #9c27b0);\\n  transform: scale(1.1) rotate(10deg);\\n  box-shadow: 0 0 15px rgba(156, 39, 176, 0.6);\\n  border-color: #ab47bc;\\n}\\n\\n.notification-attachment-button[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  width: 6px;\\n  height: 6px;\\n  background: rgba(255, 255, 255, 0.4);\\n  border-radius: 50%;\\n  transform: translate(-50%, -50%);\\n  animation: _ngcontent-%COMP%_bounce 1.5s infinite;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #00c853, #00a843);\\n  border-color: #00e676;\\n  box-shadow: 0 0 10px rgba(0, 200, 83, 0.4);\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-details-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-details-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #2196f3, #1976d2);\\n  border-color: #42a5f5;\\n  box-shadow: 0 0 10px rgba(33, 150, 243, 0.4);\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-read-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-read-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffc107, #ff9800);\\n  border-color: #ffca28;\\n  box-shadow: 0 0 10px rgba(255, 193, 7, 0.4);\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-delete-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-delete-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f44336, #d32f2f);\\n  border-color: #ef5350;\\n  box-shadow: 0 0 10px rgba(244, 67, 54, 0.4);\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-attachment-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachment-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #9c27b0, #7b1fa2);\\n  border-color: #ab47bc;\\n  box-shadow: 0 0 10px rgba(156, 39, 176, 0.4);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%,\\n  100% {\\n    opacity: 1;\\n    transform: translate(-50%, -50%) rotate(-45deg) scale(1);\\n  }\\n  50% {\\n    opacity: 0.5;\\n    transform: translate(-50%, -50%) rotate(-45deg) scale(1.2);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_bounce {\\n  0%,\\n  100% {\\n    transform: translate(-50%, -50%) scale(1);\\n  }\\n  50% {\\n    transform: translate(-50%, -50%) scale(1.3);\\n  }\\n}\\n\\n\\n\\n.notification-join-button[_ngcontent-%COMP%]:hover::after {\\n  content: \\\"\\uD83D\\uDCAC\\\";\\n  position: absolute;\\n  top: -8px;\\n  right: -8px;\\n  font-size: 10px;\\n  background: rgba(255, 255, 255, 0.9);\\n  border-radius: 50%;\\n  width: 16px;\\n  height: 16px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  opacity: 1;\\n  transition: opacity 0.3s ease;\\n  z-index: 10;\\n}\\n\\n.notification-details-button[_ngcontent-%COMP%]:hover::after {\\n  content: \\\"\\u2139\\uFE0F\\\";\\n  position: absolute;\\n  top: -8px;\\n  right: -8px;\\n  font-size: 10px;\\n  background: rgba(255, 255, 255, 0.9);\\n  border-radius: 50%;\\n  width: 16px;\\n  height: 16px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  opacity: 1;\\n  transition: opacity 0.3s ease;\\n  z-index: 10;\\n}\\n\\n.notification-delete-button[_ngcontent-%COMP%]:hover::after {\\n  content: \\\"\\uD83D\\uDDD1\\uFE0F\\\";\\n  position: absolute;\\n  top: -8px;\\n  right: -8px;\\n  font-size: 10px;\\n  background: rgba(255, 255, 255, 0.9);\\n  border-radius: 50%;\\n  width: 16px;\\n  height: 16px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  opacity: 1;\\n  transition: opacity 0.3s ease;\\n  z-index: 10;\\n}\\n\\n\\n\\n.futuristic-checkbox[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n  width: 22px; \\n\\n  height: 22px; \\n\\n  cursor: pointer;\\n  transition: all 0.2s ease; \\n\\n}\\n\\n.futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  position: absolute;\\n  opacity: 0;\\n  cursor: pointer;\\n  height: 0;\\n  width: 0;\\n}\\n\\n.checkmark[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  height: 22px; \\n\\n  width: 22px; \\n\\n  background: linear-gradient(\\n    135deg,\\n    rgba(0, 255, 200, 0.1),\\n    rgba(0, 200, 255, 0.1)\\n  ); \\n\\n  border: 2px solid transparent; \\n\\n  border-radius: 50%; \\n\\n  transition: all 0.3s ease;\\n  box-shadow: 0 0 10px rgba(0, 255, 200, 0.4); \\n\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  animation: _ngcontent-%COMP%_glow-pulse 2s infinite alternate; \\n\\n  \\n\\n  position: relative;\\n  z-index: 1;\\n  overflow: hidden;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_glow-pulse {\\n  0% {\\n    box-shadow: 0 0 8px rgba(0, 255, 200, 0.3);\\n  }\\n  100% {\\n    box-shadow: 0 0 15px rgba(0, 200, 255, 0.6);\\n  }\\n}\\n\\n.dark[_nghost-%COMP%]   .checkmark[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .checkmark[_ngcontent-%COMP%] {\\n  background: rgba(\\n    0,\\n    247,\\n    255,\\n    0.1\\n  ); \\n\\n  border: 1px solid rgba(0, 247, 255, 0.3); \\n\\n  box-shadow: 0 0 12px rgba(0, 247, 255, 0.4); \\n\\n  animation: _ngcontent-%COMP%_glow-pulse 2s infinite alternate; \\n\\n}\\n\\n.futuristic-checkbox[_ngcontent-%COMP%]:hover   input[_ngcontent-%COMP%]    ~ .checkmark[_ngcontent-%COMP%] {\\n  background: rgba(\\n    0,\\n    247,\\n    255,\\n    0.2\\n  ); \\n\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n  box-shadow: var(\\n    --glow-effect\\n  ); \\n\\n  transform: scale(\\n    1.05\\n  ); \\n\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:hover   input[_ngcontent-%COMP%]    ~ .checkmark[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:hover   input[_ngcontent-%COMP%]    ~ .checkmark[_ngcontent-%COMP%] {\\n  background: rgba(\\n    0,\\n    247,\\n    255,\\n    0.2\\n  ); \\n\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n  box-shadow: var(\\n    --glow-effect\\n  ); \\n\\n  transform: scale(\\n    1.05\\n  ); \\n\\n}\\n\\n.futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    ~ .checkmark[_ngcontent-%COMP%] {\\n  background: linear-gradient(\\n    135deg,\\n    rgba(0, 255, 200, 0.8),\\n    rgba(0, 200, 255, 0.8)\\n  ); \\n\\n  border: 2px solid transparent; \\n\\n  box-shadow: 0 0 20px rgba(0, 255, 200, 0.8); \\n\\n  animation: _ngcontent-%COMP%_checkbox-glow 1.5s infinite alternate; \\n\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_checkbox-glow {\\n  0% {\\n    box-shadow: 0 0 15px rgba(0, 255, 200, 0.6);\\n    transform: scale(1);\\n  }\\n  100% {\\n    box-shadow: 0 0 25px rgba(0, 200, 255, 0.9);\\n    transform: scale(1.15);\\n  }\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_checkbox-pulse {\\n  0% {\\n    transform: scale(1);\\n    box-shadow: 0 0 15px rgba(0, 247, 255, 0.4);\\n  }\\n  100% {\\n    transform: scale(1.15);\\n    box-shadow: 0 0 20px rgba(0, 247, 255, 0.8);\\n  }\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    ~ .checkmark[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    ~ .checkmark[_ngcontent-%COMP%] {\\n  background: linear-gradient(\\n    135deg,\\n    rgba(0, 255, 200, 0.8),\\n    rgba(0, 200, 255, 0.8)\\n  ); \\n\\n  border: 2px solid transparent; \\n\\n  box-shadow: 0 0 20px rgba(0, 255, 200, 0.8); \\n\\n  animation: _ngcontent-%COMP%_checkbox-glow 1.5s infinite alternate; \\n\\n}\\n\\n.checkmark[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  display: none;\\n}\\n\\n.futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    ~ .checkmark[_ngcontent-%COMP%]:after {\\n  display: block;\\n}\\n\\n.futuristic-checkbox[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%]:after {\\n  left: 7px; \\n\\n  top: 3px; \\n\\n  width: 6px; \\n\\n  height: 12px; \\n\\n  border: solid white;\\n  border-width: 0 2px 2px 0; \\n\\n  transform: rotate(45deg);\\n  box-shadow: 0 0 5px rgba(255, 255, 255, 0.8); \\n\\n  animation: _ngcontent-%COMP%_pulse-check 1.5s infinite alternate; \\n\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_pulse-check {\\n  0% {\\n    opacity: 0.8;\\n    box-shadow: 0 0 5px rgba(255, 255, 255, 0.8);\\n  }\\n  100% {\\n    opacity: 1;\\n    box-shadow: 0 0 10px rgba(255, 255, 255, 1);\\n  }\\n}\\n\\n\\n\\n.select-all-checkbox[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 5px;\\n}\\n\\n\\n\\n.flex.space-x-2[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n}\\n\\n.flex.space-x-2[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%; \\n\\n  background: rgba(\\n    0,\\n    247,\\n    255,\\n    0.1\\n  ); \\n\\n  border: 1px solid rgba(0, 247, 255, 0.3); \\n\\n  box-shadow: 0 0 10px rgba(0, 247, 255, 0.4); \\n\\n}\\n\\n\\n\\n.flex.space-x-2[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:hover   input[_ngcontent-%COMP%]    ~ .checkmark[_ngcontent-%COMP%] {\\n  background: rgba(0, 247, 255, 0.2);\\n  box-shadow: var(--glow-effect);\\n  transform: scale(1.05);\\n}\\n\\n\\n\\n.flex.space-x-2[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%]:after {\\n  left: 13px; \\n\\n  top: 7px; \\n\\n  width: 8px; \\n\\n  height: 16px; \\n\\n}\\n\\n\\n\\n.selection-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: rgba(255, 140, 0, 0.1);\\n  border-radius: 8px;\\n  padding: 8px 12px;\\n  box-shadow: 0 0 15px rgba(255, 140, 0, 0.2);\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n}\\n\\n.dark[_nghost-%COMP%]   .selection-actions[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .selection-actions[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 140, 0, 0.1);\\n  box-shadow: 0 0 15px rgba(255, 140, 0, 0.2);\\n}\\n\\n.selection-count[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-right: 15px;\\n  color: #ff8c00;\\n}\\n\\n.dark[_nghost-%COMP%]   .selection-count[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .selection-count[_ngcontent-%COMP%] {\\n  color: rgba(255, 140, 0, 0.9);\\n}\\n\\n\\n\\n\\n\\n.futuristic-notification-selected[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(255, 140, 0, 0.5) !important;\\n  background-color: rgba(255, 140, 0, 0.05) !important;\\n  box-shadow: 0 5px 15px rgba(255, 140, 0, 0.1) !important;\\n  transform: translateY(-2px);\\n}\\n\\n\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(255, 140, 0, 0.3) !important; \\n\\n  background: linear-gradient(\\n    135deg,\\n    rgba(255, 140, 0, 0.15),\\n    rgba(255, 0, 128, 0.15),\\n    rgba(128, 0, 255, 0.15)\\n  ) !important; \\n\\n  box-shadow: 0 5px 15px rgba(255, 140, 0, 0.2),\\n    inset 0 0 20px rgba(255, 0, 128, 0.1) !important; \\n\\n  transform: translateY(-2px);\\n  padding: 18px 22px !important; \\n\\n  margin-bottom: 18px !important; \\n\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]::before, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -50%;\\n  left: -50%;\\n  width: 200%;\\n  height: 200%;\\n  background: radial-gradient(\\n    circle,\\n    rgba(255, 140, 0, 0.1) 0%,\\n    transparent 70%\\n  );\\n  animation: _ngcontent-%COMP%_rotate-gradient 8s linear infinite;\\n  pointer-events: none;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]::after, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  background-image: radial-gradient(\\n      circle at 10% 10%,\\n      rgba(255, 255, 255, 0.8) 0%,\\n      rgba(255, 255, 255, 0) 2%\\n    ),\\n    radial-gradient(\\n      circle at 20% 30%,\\n      rgba(255, 140, 0, 0.8) 0%,\\n      rgba(255, 140, 0, 0) 2%\\n    ),\\n    radial-gradient(\\n      circle at 30% 70%,\\n      rgba(255, 0, 128, 0.8) 0%,\\n      rgba(255, 0, 128, 0) 2%\\n    ),\\n    radial-gradient(\\n      circle at 70% 40%,\\n      rgba(128, 0, 255, 0.8) 0%,\\n      rgba(128, 0, 255, 0) 2%\\n    ),\\n    radial-gradient(\\n      circle at 80% 80%,\\n      rgba(255, 255, 255, 0.8) 0%,\\n      rgba(255, 255, 255, 0) 2%\\n    ),\\n    radial-gradient(\\n      circle at 90% 10%,\\n      rgba(255, 140, 0, 0.8) 0%,\\n      rgba(255, 140, 0, 0) 2%\\n    ),\\n    radial-gradient(\\n      circle at 50% 50%,\\n      rgba(255, 0, 128, 0.8) 0%,\\n      rgba(255, 0, 128, 0) 2%\\n    );\\n  opacity: 0;\\n  animation: _ngcontent-%COMP%_sparkle-effect 4s ease-in-out infinite;\\n  pointer-events: none;\\n}\\n\\n@keyframes _ngcontent-%COMP%_sparkle-effect {\\n  0% {\\n    opacity: 0;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n  100% {\\n    opacity: 0;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_rotate-gradient {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-text[_ngcontent-%COMP%], .dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-time[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-time[_ngcontent-%COMP%] {\\n  color: rgba(\\n    255,\\n    255,\\n    255,\\n    0.9\\n  ) !important; \\n\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-message-preview[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-message-preview[_ngcontent-%COMP%] {\\n  background-color: rgba(\\n    0,\\n    0,\\n    0,\\n    0.5\\n  ) !important; \\n\\n  color: rgba(255, 255, 255, 0.9) !important; \\n\\n  border-left: 2px solid rgba(255, 140, 0, 0.5) !important; \\n\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-sender[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-sender[_ngcontent-%COMP%] {\\n  color: #ff8c00 !important; \\n\\n  font-weight: 600;\\n}\\n\\n\\n\\n\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(\\n    135deg,\\n    rgba(255, 140, 0, 0.2),\\n    rgba(255, 94, 98, 0.2)\\n  );\\n  color: #ff8c00; \\n\\n  border: 1px solid rgba(255, 140, 0, 0.4);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(\\n    135deg,\\n    rgba(255, 140, 0, 0.3),\\n    rgba(255, 94, 98, 0.3)\\n  );\\n  box-shadow: 0 0 15px rgba(255, 140, 0, 0.5); \\n\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-action-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-action-button[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n  background-color: rgba(\\n    255,\\n    140,\\n    0,\\n    0.1\\n  ); \\n\\n  border: 1px solid rgba(255, 140, 0, 0.3);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(\\n    255,\\n    140,\\n    0,\\n    0.2\\n  ); \\n\\n  color: #ff8c00; \\n\\n  box-shadow: 0 0 15px rgba(255, 140, 0, 0.4); \\n\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-delete-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-delete-button[_ngcontent-%COMP%] {\\n  background-color: rgba(\\n    255,\\n    140,\\n    0,\\n    0.1\\n  ); \\n\\n  color: #ff8c00; \\n\\n  border: 1px solid rgba(255, 140, 0, 0.3);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-delete-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-delete-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(\\n    255,\\n    140,\\n    0,\\n    0.2\\n  ); \\n\\n  color: #ff8c00; \\n\\n  box-shadow: 0 0 8px rgba(255, 140, 0, 0.4); \\n\\n}\\n\\n\\n\\n.notification-separator-dot[_ngcontent-%COMP%] {\\n  width: 4px;\\n  height: 4px;\\n  border-radius: 50%;\\n  background-color: rgba(\\n    0,\\n    247,\\n    255,\\n    0.6\\n  ); \\n\\n  margin: 0 8px;\\n  box-shadow: 0 0 5px rgba(0, 247, 255, 0.4); \\n\\n  animation: _ngcontent-%COMP%_dot-pulse 2s infinite alternate; \\n\\n  transition: all 0.5s ease; \\n\\n}\\n\\n\\n\\n.notification-separator-dot.fade-out[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  transform: scale(0);\\n  width: 0;\\n  margin: 0;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-separator-dot[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-separator-dot[_ngcontent-%COMP%] {\\n  background-color: rgba(\\n    0,\\n    247,\\n    255,\\n    0.8\\n  ); \\n\\n  box-shadow: 0 0 8px rgba(0, 247, 255, 0.6); \\n\\n  animation: _ngcontent-%COMP%_dot-pulse-selected 1.5s infinite alternate; \\n\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_dot-pulse-selected {\\n  0% {\\n    opacity: 0.6;\\n    transform: scale(1);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: scale(1.5);\\n  }\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_dot-pulse {\\n  0% {\\n    opacity: 0.4;\\n    transform: scale(0.8);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: scale(1.2);\\n  }\\n}\\n\\n\\n\\n.notification-checkbox[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 10px; \\n\\n  left: 10px; \\n\\n  z-index: 10; \\n\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n\\n\\n\\n.futuristic-cancel-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0.5rem 1rem;\\n  background-color: rgba(150, 150, 150, 0.2);\\n  color: #6d6870;\\n  border: none;\\n  border-radius: var(--border-radius-md);\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 0 8px rgba(150, 150, 150, 0.2);\\n}\\n\\n.futuristic-cancel-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(150, 150, 150, 0.3);\\n  box-shadow: 0 0 12px rgba(150, 150, 150, 0.3);\\n  transform: translateY(-2px);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-cancel-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-cancel-button[_ngcontent-%COMP%] {\\n  background-color: rgba(100, 100, 100, 0.2);\\n  color: #e0e0e0;\\n  box-shadow: 0 0 8px rgba(100, 100, 100, 0.2);\\n}\\n\\n\\n\\n\\n\\n\\n\\n\\n.notification-detail-section[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n  padding: 1rem;\\n  background: rgba(255, 255, 255, 0.05);\\n  border-radius: 12px;\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-detail-section[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-detail-section[_ngcontent-%COMP%] {\\n  background: rgba(0, 247, 255, 0.05);\\n  border: 1px solid rgba(0, 247, 255, 0.1);\\n}\\n\\n.notification-detail-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #4f5fad;\\n  margin-bottom: 0.75rem;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-detail-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-detail-title[_ngcontent-%COMP%] {\\n  color: #00f7ff;\\n  text-shadow: 0 0 6px rgba(0, 247, 255, 0.3);\\n}\\n\\n\\n\\n.notification-sender-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n}\\n\\n.notification-sender-avatar[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 2px solid rgba(79, 95, 173, 0.3);\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-sender-avatar[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-sender-avatar[_ngcontent-%COMP%] {\\n  border: 2px solid rgba(0, 247, 255, 0.3);\\n}\\n\\n.notification-sender-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.notification-sender-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #4f5fad;\\n  font-size: 1rem;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-sender-name[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-sender-name[_ngcontent-%COMP%] {\\n  color: #00f7ff;\\n}\\n\\n.notification-timestamp[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #a0a0a0;\\n}\\n\\n\\n\\n.notification-content-detail[_ngcontent-%COMP%] {\\n  background: rgba(79, 95, 173, 0.1);\\n  padding: 0.75rem;\\n  border-radius: 8px;\\n  color: #333;\\n  line-height: 1.5;\\n  border-left: 3px solid #4f5fad;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-content-detail[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-content-detail[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.2);\\n  color: #e0e0e0;\\n  border-left: 3px solid #00f7ff;\\n}\\n\\n.notification-message-detail[_ngcontent-%COMP%] {\\n  margin-top: 0.75rem;\\n  padding: 0.75rem;\\n  background: rgba(79, 95, 173, 0.05);\\n  border-radius: 8px;\\n  color: #333;\\n  font-size: 0.9rem;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-message-detail[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-message-detail[_ngcontent-%COMP%] {\\n  background: rgba(0, 247, 255, 0.1);\\n  color: #e0e0e0;\\n}\\n\\n\\n\\n.notification-info-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr;\\n  gap: 0.5rem;\\n}\\n\\n.notification-info-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0.5rem;\\n  background: rgba(79, 95, 173, 0.05);\\n  border-radius: 6px;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-info-item[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-info-item[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.2);\\n}\\n\\n.notification-info-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #666;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-info-label[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-info-label[_ngcontent-%COMP%] {\\n  color: #a0a0a0;\\n}\\n\\n.notification-info-value[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-info-value[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-info-value[_ngcontent-%COMP%] {\\n  color: #e0e0e0;\\n}\\n\\n\\n\\n.notification-attachments-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr;\\n  gap: 0.75rem;\\n}\\n\\n.notification-attachment-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  padding: 0.75rem;\\n  background: rgba(79, 95, 173, 0.05);\\n  border-radius: 8px;\\n  border: 1px solid rgba(79, 95, 173, 0.1);\\n  transition: all 0.3s ease;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-attachment-item[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachment-item[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.2);\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.notification-attachment-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(79, 95, 173, 0.1);\\n  border-color: rgba(79, 95, 173, 0.3);\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-attachment-item[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .notification-attachment-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 247, 255, 0.1);\\n  border-color: rgba(0, 247, 255, 0.3);\\n}\\n\\n.notification-attachment-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  object-fit: cover;\\n  border-radius: 6px;\\n  cursor: pointer;\\n  transition: transform 0.3s ease;\\n}\\n\\n.notification-attachment-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n\\n.notification-attachment-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: rgba(79, 95, 173, 0.2);\\n  border-radius: 6px;\\n  font-size: 1.5rem;\\n  color: #4f5fad;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-attachment-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachment-icon[_ngcontent-%COMP%] {\\n  background: rgba(0, 247, 255, 0.2);\\n  color: #00f7ff;\\n}\\n\\n.notification-attachment-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n\\n.notification-attachment-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 0.25rem;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-attachment-name[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachment-name[_ngcontent-%COMP%] {\\n  color: #e0e0e0;\\n}\\n\\n.notification-attachment-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  font-size: 0.8rem;\\n  color: #666;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-attachment-meta[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachment-meta[_ngcontent-%COMP%] {\\n  color: #a0a0a0;\\n}\\n\\n.notification-attachment-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.25rem;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "of", "BehaviorSubject", "MessageType", "catchError", "map", "takeUntil", "take", "debounceTime", "distinctUntilChanged", "filter", "i0", "ɵɵelementStart", "ɵɵlistener", "NotificationListComponent_div_8_div_3_Template_input_click_2_listener", "$event", "ɵɵrestoreView", "_r14", "ctx_r13", "ɵɵnextContext", "ɵɵresetView", "toggleSelectAll", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ctx_r10", "allSelected", "NotificationListComponent_div_8_button_9_Template_button_click_0_listener", "_r16", "ctx_r15", "markAllAsRead", "ɵɵtext", "NotificationListComponent_div_8_button_11_Template_button_click_0_listener", "_r18", "ctx_r17", "deleteAllNotifications", "NotificationListComponent_div_8_Template_button_click_1_listener", "_r20", "ctx_r19", "loadNotifications", "ɵɵtemplate", "NotificationListComponent_div_8_div_3_Template", "NotificationListComponent_div_8_Template_button_click_5_listener", "ctx_r21", "toggleUn<PERSON><PERSON><PERSON>er", "NotificationListComponent_div_8_Template_button_click_7_listener", "ctx_r22", "toggleSound", "NotificationListComponent_div_8_button_9_Template", "NotificationListComponent_div_8_button_11_Template", "ɵɵpipeBind1", "ctx_r0", "hasNotifications", "ɵɵclassProp", "showOnlyUnread", "isSoundMuted", "ɵɵpropertyInterpolate", "unreadCount$", "NotificationListComponent_div_9_Template_button_click_3_listener", "_r24", "ctx_r23", "markSelectedAsRead", "NotificationListComponent_div_9_Template_button_click_6_listener", "ctx_r25", "deleteSelectedNotifications", "NotificationListComponent_div_9_Template_button_click_9_listener", "ctx_r26", "selectedNotifications", "clear", "showSelectionBar", "ɵɵtextInterpolate1", "ctx_r1", "size", "NotificationListComponent_div_11_Template_button_click_8_listener", "_r28", "ctx_r27", "ɵɵtextInterpolate", "ctx_r3", "getErrorMessage", "NotificationListComponent_div_12_Template_button_click_7_listener", "_r30", "ctx_r29", "notification_r34", "message", "content", "attachments", "length", "NotificationListComponent_div_14_ng_container_2_button_24_Template_button_click_0_listener", "_r45", "$implicit", "ctx_r43", "getNotificationAttachments", "id", "stopPropagation", "NotificationListComponent_div_14_ng_container_2_button_25_Template_button_click_0_listener", "_r50", "ctx_r48", "joinConversation", "NotificationListComponent_div_14_ng_container_2_button_25_i_1_Template", "NotificationListComponent_div_14_ng_container_2_button_25_i_2_Template", "ctx_r39", "loading", "NotificationListComponent_div_14_ng_container_2_button_28_Template_button_click_0_listener", "_r53", "ctx_r51", "mark<PERSON><PERSON><PERSON>", "ɵɵelementContainerStart", "NotificationListComponent_div_14_ng_container_2_Template_input_click_4_listener", "restoredCtx", "_r55", "ctx_r54", "toggleSelection", "NotificationListComponent_div_14_ng_container_2_div_20_Template", "NotificationListComponent_div_14_ng_container_2_div_21_Template", "NotificationListComponent_div_14_ng_container_2_div_22_Template", "NotificationListComponent_div_14_ng_container_2_button_24_Template", "NotificationListComponent_div_14_ng_container_2_button_25_Template", "NotificationListComponent_div_14_ng_container_2_Template_button_click_26_listener", "ctx_r56", "openNotificationDetails", "NotificationListComponent_div_14_ng_container_2_button_28_Template", "NotificationListComponent_div_14_ng_container_2_Template_button_click_29_listener", "ctx_r57", "deleteNotification", "ɵɵelementContainerEnd", "isRead", "ctx_r32", "isSelected", "senderId", "image", "ɵɵsanitizeUrl", "username", "ɵɵpipeBind2", "timestamp", "type", "NotificationListComponent_div_14_Template_div_scroll_0_listener", "_r59", "_r31", "ɵɵreference", "ctx_r58", "onScroll", "NotificationListComponent_div_14_ng_container_2_Template", "NotificationListComponent_div_14_div_4_Template", "ctx_r5", "filteredNotifications$", "loadingMore", "NotificationListComponent_div_27_div_1_div_1_Template_img_click_1_listener", "_r67", "attachment_r61", "ctx_r65", "openAttachment", "url", "ɵɵclassMap", "ctx_r63", "getFileIcon", "ctx_r64", "formatFileSize", "NotificationListComponent_div_27_div_1_div_1_Template", "NotificationListComponent_div_27_div_1_div_2_Template", "NotificationListComponent_div_27_div_1_span_9_Template", "NotificationListComponent_div_27_div_1_Template_button_click_11_listener", "_r72", "ctx_r71", "NotificationListComponent_div_27_div_1_Template_button_click_13_listener", "ctx_r73", "downloadAttachment", "ctx_r60", "isImage", "name", "getFileTypeLabel", "NotificationListComponent_div_27_div_1_Template", "ctx_r8", "currentAttachments", "ctx_r74", "currentNotification", "ctx_r75", "readAt", "NotificationListComponent_div_36_div_37_div_5_div_1_Template_img_click_1_listener", "_r87", "attachment_r81", "ctx_r85", "ctx_r83", "ctx_r84", "NotificationListComponent_div_36_div_37_div_5_div_1_Template", "NotificationListComponent_div_36_div_37_div_5_div_2_Template", "NotificationListComponent_div_36_div_37_div_5_span_9_Template", "NotificationListComponent_div_36_div_37_div_5_Template_button_click_11_listener", "_r92", "ctx_r91", "NotificationListComponent_div_36_div_37_div_5_Template_button_click_13_listener", "ctx_r93", "ctx_r80", "NotificationListComponent_div_36_div_37_div_5_Template", "ctx_r77", "NotificationListComponent_div_36_button_39_Template_button_click_0_listener", "_r97", "ctx_r96", "closeNotificationDetailsModal", "NotificationListComponent_div_36_button_39_i_1_Template", "NotificationListComponent_div_36_button_39_i_2_Template", "ctx_r78", "NotificationListComponent_div_36_button_40_Template_button_click_0_listener", "_r99", "ctx_r98", "NotificationListComponent_div_36_div_19_Template", "NotificationListComponent_div_36_div_35_Template", "NotificationListComponent_div_36_div_36_Template", "NotificationListComponent_div_36_div_37_Template", "NotificationListComponent_div_36_button_39_Template", "NotificationListComponent_div_36_button_40_Template", "NotificationListComponent_div_36_Template_button_click_41_listener", "_r101", "ctx_r100", "ctx_r9", "NotificationListComponent", "constructor", "messageService", "themeService", "router", "hasMoreNotifications", "error", "Set", "showAttachmentsModal", "loadingAttachments", "showNotificationDetailsModal", "destroy$", "scrollPosition$", "notifications$", "notificationCount$", "isDarkMode$", "darkMode$", "isMuted", "notification", "conversationId", "metadata", "relatedEntity", "includes", "groupId", "navigate", "getOrCreateConversation", "subscribe", "next", "conversation", "target", "scrollPosition", "scrollTop", "scrollHeight", "clientHeight", "ngOnInit", "savedMutePreference", "localStorage", "getItem", "setMuted", "setupSubscriptions", "setupInfiniteScroll", "filterDeletedNotifications", "deletedNotificationIds", "getDeletedNotificationIds", "pipe", "notifications", "filteredNotifications", "has", "unreadCount", "n", "notificationCount", "updateNotificationCache", "loadMoreNotifications", "getNotifications", "err", "existingNotifications", "allNotifications", "subscribeToNewNotifications", "console", "subscribeToNotificationsRead", "notificationId", "Error", "find", "updatedNotifications", "Date", "toISOString", "updateUIWithNotifications", "result", "success", "revertedNotifications", "undefined", "revertedUnreadCount", "for<PERSON>ach", "resetSelection", "unreadIds", "validIds", "trim", "hasUnreadNotifications", "count", "getUnreadNotifications", "setTimeout", "playNotificationSound", "setItem", "toString", "attachment", "convertAttachmentTypeToMessageType", "duration", "closeAttachmentsModal", "getNotificationAttachmentsForModal", "IMAGE", "VIDEO", "AUDIO", "FILE", "startsWith", "units", "i", "formattedSize", "toFixed", "window", "open", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "acceptFriendRequest", "add", "saveDeletedNotificationIds", "log", "deletedIdsJson", "JSON", "parse", "deletedIds", "stringify", "Array", "from", "ngOnDestroy", "complete", "event", "delete", "updateSelectionState", "selectedIds", "deleteMultipleNotifications", "ɵɵdirectiveInject", "i1", "MessageService", "i2", "ThemeService", "i3", "Router", "selectors", "viewQuery", "NotificationListComponent_Query", "rf", "ctx", "NotificationListComponent_div_8_Template", "NotificationListComponent_div_9_Template", "NotificationListComponent_div_10_Template", "NotificationListComponent_div_11_Template", "NotificationListComponent_div_12_Template", "NotificationListComponent_div_14_Template", "NotificationListComponent_Template_div_click_16_listener", "NotificationListComponent_Template_div_click_17_listener", "NotificationListComponent_Template_button_click_22_listener", "NotificationListComponent_div_25_Template", "NotificationListComponent_div_26_Template", "NotificationListComponent_div_27_Template", "NotificationListComponent_Template_div_click_28_listener", "NotificationListComponent_Template_div_click_29_listener", "NotificationListComponent_Template_button_click_34_listener", "NotificationListComponent_div_36_Template", "ɵɵstyleProp"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\notifications\\notification-list\\notification-list.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\notifications\\notification-list\\notification-list.component.html"], "sourcesContent": ["import {\n  Component,\n  OnInit,\n  <PERSON><PERSON><PERSON><PERSON>,\n  HostL<PERSON>ener,\n  ElementRef,\n  ViewChild,\n} from '@angular/core';\nimport { Router } from '@angular/router';\nimport { MessageService } from 'src/app/services/message.service';\nimport { Observable, Subject, of, BehaviorSubject } from 'rxjs';\nimport {\n  Notification,\n  Attachment,\n  NotificationAttachment,\n  AttachmentType,\n  MessageType,\n} from 'src/app/models/message.model';\nimport {\n  catchError,\n  map,\n  takeUntil,\n  take,\n  debounceTime,\n  distinctUntilChanged,\n  filter,\n} from 'rxjs/operators';\nimport { ThemeService } from '@app/services/theme.service';\n@Component({\n  selector: 'app-notification-list',\n  templateUrl: './notification-list.component.html',\n  styleUrls: ['./notification-list.component.css'],\n})\nexport class NotificationListComponent implements OnInit, OnDestroy {\n  @ViewChild('notificationContainer', { static: false })\n  notificationContainer!: ElementRef;\n\n  notifications$: Observable<Notification[]>;\n  filteredNotifications$: Observable<Notification[]>;\n  unreadCount$: Observable<number>;\n  isDarkMode$: Observable<boolean>;\n  loading = true;\n  loadingMore = false;\n  hasMoreNotifications = true;\n  error: Error | null = null;\n  showOnlyUnread = false;\n  isSoundMuted = false;\n\n  // Propriétés pour la sélection multiple\n  selectedNotifications: Set<string> = new Set<string>();\n  allSelected = false;\n  showSelectionBar = false;\n\n  // Propriétés pour le modal des pièces jointes\n  showAttachmentsModal = false;\n  loadingAttachments = false;\n  currentAttachments: Attachment[] = [];\n\n  // Propriétés pour le modal des détails de notification\n  showNotificationDetailsModal = false;\n  currentNotification: Notification | null = null;\n\n  private destroy$ = new Subject<void>();\n  private scrollPosition$ = new BehaviorSubject<number>(0);\n\n  constructor(\n    private messageService: MessageService,\n    private themeService: ThemeService,\n    private router: Router\n  ) {\n    this.notifications$ = this.messageService.notifications$;\n    this.filteredNotifications$ = this.notifications$; // Par défaut, afficher toutes les notifications\n    this.unreadCount$ = this.messageService.notificationCount$;\n    this.isDarkMode$ = this.themeService.darkMode$;\n\n    // Vérifier l'état du son\n    this.isSoundMuted = this.messageService.isMuted();\n  }\n\n  /**\n   * Rejoint une conversation ou un groupe à partir d'une notification\n   * @param notification Notification contenant les informations de la conversation ou du groupe\n   */\n  joinConversation(notification: Notification): void {\n    // Marquer la notification comme lue d'abord\n    this.markAsRead(notification.id);\n\n    // Extraire les informations pertinentes de la notification\n    const conversationId =\n      notification.conversationId ||\n      (notification.metadata && notification.metadata['conversationId']) ||\n      (notification.relatedEntity &&\n      notification.relatedEntity.includes('conversation')\n        ? notification.relatedEntity\n        : null);\n\n    const groupId =\n      notification.groupId ||\n      (notification.metadata && notification.metadata['groupId']) ||\n      (notification.relatedEntity &&\n      notification.relatedEntity.includes('group')\n        ? notification.relatedEntity\n        : null);\n\n    // Déterminer où rediriger l'utilisateur\n    if (conversationId) {\n      this.router.navigate(['/messages/conversations/chat', conversationId]);\n    } else if (groupId) {\n      this.router.navigate(['/messages/group', groupId]);\n    } else if (notification.senderId && notification.senderId.id) {\n      this.loading = true;\n\n      this.messageService\n        .getOrCreateConversation(notification.senderId.id)\n        .subscribe({\n          next: (conversation) => {\n            this.loading = false;\n            if (conversation && conversation.id) {\n              this.router.navigate([\n                '/messages/conversations/chat',\n                conversation.id,\n              ]);\n            } else {\n              this.router.navigate(['/messages']);\n            }\n          },\n          error: (error) => {\n            this.loading = false;\n            this.error = error;\n            this.router.navigate(['/messages']);\n          },\n        });\n    } else {\n      this.router.navigate(['/messages']);\n    }\n  }\n\n  @HostListener('scroll', ['$event.target'])\n  onScroll(target: HTMLElement): void {\n    if (!target) return;\n\n    const scrollPosition = target.scrollTop;\n    const scrollHeight = target.scrollHeight;\n    const clientHeight = target.clientHeight;\n\n    // Si on est proche du bas (à 200px du bas)\n    if (scrollHeight - scrollPosition - clientHeight < 200) {\n      this.scrollPosition$.next(scrollPosition);\n    }\n  }\n  ngOnInit(): void {\n    // Charger la préférence de son depuis le localStorage\n    const savedMutePreference = localStorage.getItem('notificationSoundMuted');\n    if (savedMutePreference !== null) {\n      this.isSoundMuted = savedMutePreference === 'true';\n      this.messageService.setMuted(this.isSoundMuted);\n    }\n\n    this.loadNotifications();\n    this.setupSubscriptions();\n    this.setupInfiniteScroll();\n    this.filterDeletedNotifications();\n  }\n\n  /**\n   * Filtre les notifications supprimées lors du chargement initial\n   */\n  private filterDeletedNotifications(): void {\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n\n    if (deletedNotificationIds.size > 0) {\n      this.notifications$.pipe(take(1)).subscribe((notifications) => {\n        const filteredNotifications = notifications.filter(\n          (notification) => !deletedNotificationIds.has(notification.id)\n        );\n\n        (this.messageService as any).notifications.next(filteredNotifications);\n        const unreadCount = filteredNotifications.filter(\n          (n) => !n.isRead\n        ).length;\n        (this.messageService as any).notificationCount.next(unreadCount);\n        this.updateNotificationCache(filteredNotifications);\n      });\n    }\n  }\n\n  setupInfiniteScroll(): void {\n    // Configurer le chargement des anciennes notifications lors du défilement\n    this.scrollPosition$\n      .pipe(\n        takeUntil(this.destroy$),\n        debounceTime(200), // Attendre 200ms après le dernier événement de défilement\n        distinctUntilChanged(), // Ne déclencher que si la position de défilement a changé\n        filter(() => !this.loadingMore && this.hasMoreNotifications) // Ne charger que s'il y a plus de notifications et qu'on n'est pas déjà en train de charger\n      )\n      .subscribe(() => {\n        this.loadMoreNotifications();\n      });\n  }\n  loadNotifications(): void {\n    this.loading = true;\n    this.loadingMore = false;\n    this.error = null;\n    this.hasMoreNotifications = true;\n\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n\n    this.messageService\n      .getNotifications(true)\n      .pipe(\n        takeUntil(this.destroy$),\n        map((notifications) => {\n          if (deletedNotificationIds.size > 0) {\n            return notifications.filter(\n              (notification) => !deletedNotificationIds.has(notification.id)\n            );\n          }\n          return notifications;\n        })\n      )\n      .subscribe({\n        next: (notifications) => {\n          (this.messageService as any).notifications.next(notifications);\n          const unreadCount = notifications.filter((n) => !n.isRead).length;\n          (this.messageService as any).notificationCount.next(unreadCount);\n          this.loading = false;\n          this.hasMoreNotifications =\n            this.messageService.hasMoreNotifications();\n        },\n        error: (err: Error) => {\n          this.error = err;\n          this.loading = false;\n          this.hasMoreNotifications = false;\n        },\n      });\n  }\n\n  loadMoreNotifications(): void {\n    if (this.loadingMore || !this.hasMoreNotifications) return;\n\n    this.loadingMore = true;\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n\n    this.messageService\n      .loadMoreNotifications()\n      .pipe(\n        takeUntil(this.destroy$),\n        map((notifications) => {\n          if (deletedNotificationIds.size > 0) {\n            return notifications.filter(\n              (notification) => !deletedNotificationIds.has(notification.id)\n            );\n          }\n          return notifications;\n        })\n      )\n      .subscribe({\n        next: (notifications) => {\n          this.notifications$\n            .pipe(take(1))\n            .subscribe((existingNotifications) => {\n              const allNotifications = [\n                ...existingNotifications,\n                ...notifications,\n              ];\n              (this.messageService as any).notifications.next(allNotifications);\n              const unreadCount = allNotifications.filter(\n                (n) => !n.isRead\n              ).length;\n              (this.messageService as any).notificationCount.next(unreadCount);\n              this.updateNotificationCache(allNotifications);\n            });\n\n          this.loadingMore = false;\n          this.hasMoreNotifications =\n            this.messageService.hasMoreNotifications();\n        },\n        error: (err: Error) => {\n          this.loadingMore = false;\n          this.hasMoreNotifications = false;\n        },\n      });\n  }\n  setupSubscriptions(): void {\n    this.messageService\n      .subscribeToNewNotifications()\n      .pipe(\n        takeUntil(this.destroy$),\n        catchError((error) => {\n          console.error('Notification stream error:', error);\n          return of(null);\n        })\n      )\n      .subscribe();\n    this.messageService\n      .subscribeToNotificationsRead()\n      .pipe(\n        takeUntil(this.destroy$),\n        catchError((error) => {\n          console.error('Notifications read stream error:', error);\n          return of(null);\n        })\n      )\n      .subscribe();\n  }\n  markAsRead(notificationId: string): void {\n    if (!notificationId) {\n      this.error = new Error('ID de notification invalide');\n      return;\n    }\n\n    this.notifications$.pipe(take(1)).subscribe((notifications) => {\n      const notification = notifications.find((n) => n.id === notificationId);\n      if (notification) {\n        if (notification.isRead) return;\n\n        const updatedNotifications = notifications.map((n) =>\n          n.id === notificationId\n            ? { ...n, isRead: true, readAt: new Date().toISOString() }\n            : n\n        );\n\n        this.updateUIWithNotifications(updatedNotifications);\n\n        this.messageService\n          .markAsRead([notificationId])\n          .pipe(takeUntil(this.destroy$))\n          .subscribe({\n            next: (result) => {\n              if (result && result.success) {\n                if (this.error && this.error.message.includes('mark')) {\n                  this.error = null;\n                }\n              }\n            },\n            error: (err) => {\n              const revertedNotifications = notifications.map((n) =>\n                n.id === notificationId\n                  ? { ...n, isRead: false, readAt: undefined }\n                  : n\n              );\n              (this.messageService as any).notifications.next(\n                revertedNotifications\n              );\n\n              const revertedUnreadCount = revertedNotifications.filter(\n                (n) => !n.isRead\n              ).length;\n              (this.messageService as any).notificationCount.next(\n                revertedUnreadCount\n              );\n            },\n          });\n      } else {\n        this.loadNotifications();\n      }\n    });\n  }\n\n  /**\n   * Met à jour l'interface utilisateur avec les nouvelles notifications\n   * @param notifications Notifications à afficher\n   */\n  private updateUIWithNotifications(notifications: any[]): void {\n    // Mettre à jour l'interface utilisateur immédiatement\n    (this.messageService as any).notifications.next(notifications);\n\n    // Mettre à jour le compteur de notifications non lues\n    const unreadCount = notifications.filter((n) => !n.isRead).length;\n    (this.messageService as any).notificationCount.next(unreadCount);\n\n    // Mettre à jour le cache de notifications dans le service\n    this.updateNotificationCache(notifications);\n  }\n\n  /**\n   * Met à jour le cache de notifications dans le service\n   * @param notifications Notifications à mettre à jour\n   */\n  private updateNotificationCache(notifications: any[]): void {\n    notifications.forEach((notification) => {\n      (this.messageService as any).updateNotificationCache?.(notification);\n    });\n  }\n\n  /**\n   * Réinitialise la sélection des notifications\n   */\n  private resetSelection(): void {\n    this.selectedNotifications.clear();\n    this.allSelected = false;\n    this.showSelectionBar = false;\n  }\n\n  markAllAsRead(): void {\n    this.notifications$.pipe(take(1)).subscribe((notifications) => {\n      const unreadIds = notifications.filter((n) => !n.isRead).map((n) => n.id);\n\n      if (unreadIds.length === 0) return;\n\n      const validIds = unreadIds.filter(\n        (id) => id && typeof id === 'string' && id.trim() !== ''\n      );\n\n      if (validIds.length !== unreadIds.length) {\n        this.error = new Error('Invalid notification IDs');\n        return;\n      }\n\n      const updatedNotifications = notifications.map((n) =>\n        validIds.includes(n.id)\n          ? { ...n, isRead: true, readAt: new Date().toISOString() }\n          : n\n      );\n\n      this.updateUIWithNotifications(updatedNotifications);\n\n      this.messageService\n        .markAsRead(validIds)\n        .pipe(takeUntil(this.destroy$))\n        .subscribe({\n          next: (result) => {\n            if (result && result.success) {\n              if (this.error && this.error.message.includes('mark')) {\n                this.error = null;\n              }\n            }\n          },\n          error: (err) => {\n            // Ne pas définir d'erreur pour éviter de perturber l'interface utilisateur\n          },\n        });\n    });\n  }\n\n  hasNotifications(): Observable<boolean> {\n    return this.notifications$.pipe(\n      map((notifications) => notifications?.length > 0)\n    );\n  }\n  hasUnreadNotifications(): Observable<boolean> {\n    return this.unreadCount$.pipe(map((count) => count > 0));\n  }\n\n  /**\n   * Active/désactive le filtre pour n'afficher que les notifications non lues\n   */\n  toggleUnreadFilter(): void {\n    this.showOnlyUnread = !this.showOnlyUnread;\n\n    if (this.showOnlyUnread) {\n      this.filteredNotifications$ =\n        this.messageService.getUnreadNotifications();\n    } else {\n      this.filteredNotifications$ = this.notifications$;\n    }\n  }\n\n  /**\n   * Active/désactive le son des notifications\n   */\n  toggleSound(): void {\n    this.isSoundMuted = !this.isSoundMuted;\n    this.messageService.setMuted(this.isSoundMuted);\n\n    if (!this.isSoundMuted) {\n      setTimeout(() => {\n        this.messageService.playNotificationSound();\n        setTimeout(() => {\n          this.messageService.playNotificationSound();\n        }, 1000);\n      }, 100);\n    }\n\n    localStorage.setItem(\n      'notificationSoundMuted',\n      this.isSoundMuted.toString()\n    );\n  }\n\n  /**\n   * Récupère les pièces jointes d'une notification et ouvre le modal\n   * @param notificationId ID de la notification\n   */\n  getNotificationAttachments(notificationId: string): void {\n    if (!notificationId) return;\n\n    this.currentAttachments = [];\n    this.loadingAttachments = true;\n    this.showAttachmentsModal = true;\n\n    let notification: Notification | undefined;\n\n    this.notifications$.pipe(take(1)).subscribe((notifications) => {\n      notification = notifications.find(\n        (n: Notification) => n.id === notificationId\n      );\n    });\n\n    if (\n      notification &&\n      notification.message &&\n      notification.message.attachments &&\n      notification.message.attachments.length > 0\n    ) {\n      this.loadingAttachments = false;\n      this.currentAttachments = notification.message.attachments.map(\n        (attachment: NotificationAttachment) =>\n          ({\n            id: '',\n            url: attachment.url || '',\n            type: this.convertAttachmentTypeToMessageType(attachment.type),\n            name: attachment.name || '',\n            size: attachment.size || 0,\n            duration: 0,\n          } as Attachment)\n      );\n      return;\n    }\n\n    this.messageService\n      .getNotificationAttachments(notificationId)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (attachments) => {\n          this.loadingAttachments = false;\n          this.currentAttachments = attachments;\n        },\n        error: (err) => {\n          this.loadingAttachments = false;\n        },\n      });\n  }\n\n  /**\n   * Ferme le modal des pièces jointes\n   */\n  closeAttachmentsModal(): void {\n    this.showAttachmentsModal = false;\n  }\n\n  /**\n   * Ouvre le modal des détails de notification\n   * @param notification Notification à afficher\n   */\n  openNotificationDetails(notification: Notification): void {\n    this.currentNotification = notification;\n    this.showNotificationDetailsModal = true;\n\n    if (notification.message?.attachments?.length) {\n      this.getNotificationAttachmentsForModal(notification.id);\n    }\n  }\n\n  /**\n   * Ferme le modal des détails de notification\n   */\n  closeNotificationDetailsModal(): void {\n    this.showNotificationDetailsModal = false;\n    this.currentNotification = null;\n    this.currentAttachments = [];\n  }\n\n  /**\n   * Récupère les pièces jointes d'une notification pour le modal de détails\n   */\n  private getNotificationAttachmentsForModal(notificationId: string): void {\n    this.currentAttachments = [];\n\n    if (this.currentNotification?.message?.attachments?.length) {\n      this.currentAttachments =\n        this.currentNotification.message.attachments.map(\n          (attachment: NotificationAttachment) =>\n            ({\n              id: '',\n              url: attachment.url || '',\n              type: this.convertAttachmentTypeToMessageType(attachment.type),\n              name: attachment.name || '',\n              size: attachment.size || 0,\n              duration: 0,\n            } as Attachment)\n        );\n    }\n  }\n\n  /**\n   * Convertit AttachmentType en MessageType\n   */\n  private convertAttachmentTypeToMessageType(\n    type: AttachmentType\n  ): MessageType {\n    switch (type) {\n      case 'IMAGE':\n      case 'image':\n        return MessageType.IMAGE;\n      case 'VIDEO':\n      case 'video':\n        return MessageType.VIDEO;\n      case 'AUDIO':\n      case 'audio':\n        return MessageType.AUDIO;\n      case 'FILE':\n      case 'file':\n        return MessageType.FILE;\n      default:\n        return MessageType.FILE;\n    }\n  }\n\n  /**\n   * Vérifie si un type de fichier est une image\n   */\n  isImage(type: string): boolean {\n    return type?.startsWith('image/') || false;\n  }\n\n  /**\n   * Obtient l'icône FontAwesome correspondant au type de fichier\n   * @param type Type MIME du fichier\n   * @returns Classe CSS de l'icône\n   */\n  getFileIcon(type: string): string {\n    if (!type) return 'fas fa-file';\n\n    if (type.startsWith('image/')) return 'fas fa-file-image';\n    if (type.startsWith('video/')) return 'fas fa-file-video';\n    if (type.startsWith('audio/')) return 'fas fa-file-audio';\n    if (type.startsWith('text/')) return 'fas fa-file-alt';\n    if (type.includes('pdf')) return 'fas fa-file-pdf';\n    if (type.includes('word') || type.includes('document'))\n      return 'fas fa-file-word';\n    if (type.includes('excel') || type.includes('sheet'))\n      return 'fas fa-file-excel';\n    if (type.includes('powerpoint') || type.includes('presentation'))\n      return 'fas fa-file-powerpoint';\n    if (type.includes('zip') || type.includes('compressed'))\n      return 'fas fa-file-archive';\n\n    return 'fas fa-file';\n  }\n\n  /**\n   * Obtient le libellé du type de fichier\n   * @param type Type MIME du fichier\n   * @returns Libellé du type de fichier\n   */\n  getFileTypeLabel(type: string): string {\n    if (!type) return 'Fichier';\n\n    if (type.startsWith('image/')) return 'Image';\n    if (type.startsWith('video/')) return 'Vidéo';\n    if (type.startsWith('audio/')) return 'Audio';\n    if (type.startsWith('text/')) return 'Texte';\n    if (type.includes('pdf')) return 'PDF';\n    if (type.includes('word') || type.includes('document')) return 'Document';\n    if (type.includes('excel') || type.includes('sheet'))\n      return 'Feuille de calcul';\n    if (type.includes('powerpoint') || type.includes('presentation'))\n      return 'Présentation';\n    if (type.includes('zip') || type.includes('compressed')) return 'Archive';\n\n    return 'Fichier';\n  }\n\n  /**\n   * Formate la taille du fichier en unités lisibles\n   * @param size Taille en octets\n   * @returns Taille formatée (ex: \"1.5 MB\")\n   */\n  formatFileSize(size: number): string {\n    if (!size) return '';\n\n    const units = ['B', 'KB', 'MB', 'GB', 'TB'];\n    let i = 0;\n    let formattedSize = size;\n\n    while (formattedSize >= 1024 && i < units.length - 1) {\n      formattedSize /= 1024;\n      i++;\n    }\n\n    return `${formattedSize.toFixed(1)} ${units[i]}`;\n  }\n\n  /**\n   * Ouvre une pièce jointe dans un nouvel onglet\n   * @param url URL de la pièce jointe\n   */\n  openAttachment(url: string): void {\n    if (!url) return;\n    window.open(url, '_blank');\n  }\n\n  /**\n   * Télécharge une pièce jointe\n   * @param attachment Pièce jointe à télécharger\n   */\n  downloadAttachment(attachment: Attachment): void {\n    if (!attachment?.url) return;\n\n    const link = document.createElement('a');\n    link.href = attachment.url;\n    link.download = attachment.name || 'attachment';\n    link.target = '_blank';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  }\n\n  acceptFriendRequest(notification: Notification): void {\n    this.markAsRead(notification.id);\n  }\n\n  /**\n   * Supprime une notification et la stocke dans le localStorage\n   * @param notificationId ID de la notification à supprimer\n   */\n  deleteNotification(notificationId: string): void {\n    if (!notificationId) {\n      this.error = new Error('ID de notification invalide');\n      return;\n    }\n\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    deletedNotificationIds.add(notificationId);\n    this.saveDeletedNotificationIds(deletedNotificationIds);\n\n    this.messageService\n      .deleteNotification(notificationId)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (result) => {\n          if (result && result.success) {\n            if (this.error && this.error.message.includes('suppression')) {\n              this.error = null;\n            }\n          }\n        },\n        error: (err) => {\n          this.error = err;\n        },\n      });\n  }\n\n  /**\n   * Supprime toutes les notifications et les stocke dans le localStorage\n   */\n  deleteAllNotifications(): void {\n    console.log('Suppression de toutes les notifications');\n\n    // Récupérer toutes les notifications actuelles\n    this.notifications$.pipe(take(1)).subscribe((notifications) => {\n      // Récupérer les IDs des notifications supprimées du localStorage\n      const deletedNotificationIds = this.getDeletedNotificationIds();\n\n      // Ajouter tous les IDs des notifications actuelles\n      notifications.forEach((notification) => {\n        deletedNotificationIds.add(notification.id);\n      });\n\n      // Sauvegarder les IDs dans le localStorage\n      this.saveDeletedNotificationIds(deletedNotificationIds);\n\n      // Appeler le service pour supprimer toutes les notifications\n      this.messageService\n        .deleteAllNotifications()\n        .pipe(takeUntil(this.destroy$))\n        .subscribe({\n          next: (result) => {\n            console.log(\n              'Résultat de la suppression de toutes les notifications:',\n              result\n            );\n            if (result && result.success) {\n              console.log(\n                `${result.count} notifications supprimées avec succès`\n              );\n              // Si l'erreur était liée à cette opération, la réinitialiser\n              if (this.error && this.error.message.includes('suppression')) {\n                this.error = null;\n              }\n            }\n          },\n          error: (err) => {\n            console.error(\n              'Erreur lors de la suppression de toutes les notifications:',\n              err\n            );\n            // Même en cas d'erreur, conserver les IDs dans le localStorage\n            this.error = err;\n          },\n        });\n    });\n  }\n\n  getErrorMessage(): string {\n    return this.error?.message || 'Unknown error occurred';\n  }\n\n  /**\n   * Récupère les IDs des notifications supprimées du localStorage\n   * @returns Set contenant les IDs des notifications supprimées\n   */\n  private getDeletedNotificationIds(): Set<string> {\n    try {\n      const deletedIdsJson = localStorage.getItem('deletedNotificationIds');\n      if (deletedIdsJson) {\n        return new Set<string>(JSON.parse(deletedIdsJson));\n      }\n      return new Set<string>();\n    } catch (error) {\n      console.error(\n        'Erreur lors de la récupération des IDs de notifications supprimées:',\n        error\n      );\n      return new Set<string>();\n    }\n  }\n\n  /**\n   * Sauvegarde les IDs des notifications supprimées dans le localStorage\n   * @param deletedIds Set contenant les IDs des notifications supprimées\n   */\n  private saveDeletedNotificationIds(deletedIds: Set<string>): void {\n    try {\n      localStorage.setItem(\n        'deletedNotificationIds',\n        JSON.stringify(Array.from(deletedIds))\n      );\n      console.log(\n        `${deletedIds.size} IDs de notifications supprimées sauvegardés dans le localStorage`\n      );\n    } catch (error) {\n      console.error(\n        'Erreur lors de la sauvegarde des IDs de notifications supprimées:',\n        error\n      );\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  /**\n   * Sélectionne ou désélectionne une notification\n   * @param notificationId ID de la notification\n   * @param event Événement de la case à cocher\n   */\n  toggleSelection(notificationId: string, event: Event): void {\n    event.stopPropagation(); // Empêcher la propagation de l'événement\n\n    if (this.selectedNotifications.has(notificationId)) {\n      this.selectedNotifications.delete(notificationId);\n    } else {\n      this.selectedNotifications.add(notificationId);\n    }\n\n    // Mettre à jour l'état de sélection globale\n    this.updateSelectionState();\n\n    // Afficher ou masquer la barre de sélection\n    this.showSelectionBar = this.selectedNotifications.size > 0;\n  }\n\n  /**\n   * Sélectionne ou désélectionne toutes les notifications\n   * @param event Événement de la case à cocher\n   */\n  toggleSelectAll(event: Event): void {\n    event.stopPropagation(); // Empêcher la propagation de l'événement\n\n    this.allSelected = !this.allSelected;\n\n    this.filteredNotifications$.pipe(take(1)).subscribe((notifications) => {\n      if (this.allSelected) {\n        // Sélectionner toutes les notifications\n        notifications.forEach((notification) => {\n          this.selectedNotifications.add(notification.id);\n        });\n      } else {\n        // Désélectionner toutes les notifications\n        this.selectedNotifications.clear();\n      }\n\n      // Afficher ou masquer la barre de sélection\n      this.showSelectionBar = this.selectedNotifications.size > 0;\n    });\n  }\n\n  /**\n   * Met à jour l'état de sélection globale\n   */\n  private updateSelectionState(): void {\n    this.filteredNotifications$.pipe(take(1)).subscribe((notifications) => {\n      this.allSelected =\n        notifications.length > 0 &&\n        this.selectedNotifications.size === notifications.length;\n    });\n  }\n\n  /**\n   * Supprime les notifications sélectionnées\n   */\n  deleteSelectedNotifications(): void {\n    if (this.selectedNotifications.size === 0) {\n      return;\n    }\n\n    const selectedIds = Array.from(this.selectedNotifications);\n    console.log('Suppression des notifications sélectionnées:', selectedIds);\n\n    // Supprimer localement les notifications sélectionnées\n    this.notifications$.pipe(take(1)).subscribe((notifications) => {\n      const updatedNotifications = notifications.filter(\n        (notification) => !this.selectedNotifications.has(notification.id)\n      );\n\n      // Mettre à jour l'interface utilisateur immédiatement\n      this.updateUIWithNotifications(updatedNotifications);\n\n      // Réinitialiser la sélection\n      this.resetSelection();\n    });\n\n    // Appeler le service pour supprimer les notifications sélectionnées\n    this.messageService\n      .deleteMultipleNotifications(selectedIds)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (result) => {\n          console.log('Résultat de la suppression multiple:', result);\n          if (result && result.success) {\n            console.log(`${result.count} notifications supprimées avec succès`);\n          }\n        },\n        error: (err) => {\n          console.error(\n            'Erreur lors de la suppression multiple des notifications:',\n            err\n          );\n        },\n      });\n  }\n\n  /**\n   * Marque les notifications sélectionnées comme lues\n   */\n  markSelectedAsRead(): void {\n    if (this.selectedNotifications.size === 0) {\n      return;\n    }\n\n    const selectedIds = Array.from(this.selectedNotifications);\n    console.log(\n      'Marquage des notifications sélectionnées comme lues:',\n      selectedIds\n    );\n\n    // Marquer localement les notifications sélectionnées comme lues\n    this.notifications$.pipe(take(1)).subscribe((notifications) => {\n      const updatedNotifications = notifications.map((notification) =>\n        this.selectedNotifications.has(notification.id)\n          ? { ...notification, isRead: true, readAt: new Date().toISOString() }\n          : notification\n      );\n\n      // Mettre à jour l'interface utilisateur immédiatement\n      this.updateUIWithNotifications(updatedNotifications);\n\n      // Réinitialiser la sélection\n      this.resetSelection();\n    });\n\n    // Appeler le service pour marquer les notifications comme lues\n    this.messageService\n      .markAsRead(selectedIds)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (result) => {\n          console.log('Résultat du marquage comme lu:', result);\n          if (result && result.success) {\n            console.log('Notifications marquées comme lues avec succès');\n          }\n        },\n        error: (err) => {\n          console.error(\n            'Erreur lors du marquage des notifications comme lues:',\n            err\n          );\n        },\n      });\n  }\n\n  /**\n   * Vérifie si une notification est sélectionnée\n   * @param notificationId ID de la notification\n   * @returns true si la notification est sélectionnée, false sinon\n   */\n  isSelected(notificationId: string): boolean {\n    return this.selectedNotifications.has(notificationId);\n  }\n}\n", "<div\n  class=\"futuristic-notifications-container main-grid-container\"\n  [class.dark]=\"isDarkMode$ | async\"\n>\n  <!-- Background decorative elements -->\n  <div class=\"background-elements background-grid\">\n    <!-- Grid pattern and scan line will be added via CSS -->\n  </div>\n\n  <div class=\"futuristic-notifications-card content-card relative z-10\">\n    <div class=\"futuristic-notifications-header\">\n      <h2 class=\"futuristic-title\">\n        <i class=\"fas fa-bell mr-2\"></i>\n        Notifications\n      </h2>\n\n      <!-- Barre d'actions normale -->\n      <div class=\"flex space-x-2\" *ngIf=\"!showSelectionBar\">\n        <!-- Bouton de rafraîchissement -->\n        <button\n          (click)=\"loadNotifications()\"\n          class=\"futuristic-action-button\"\n          title=\"Rafraîchir\"\n        >\n          <i class=\"fas fa-sync-alt\"></i>\n        </button>\n\n        <!-- Case à cocher \"Tout sélectionner\" (déplacée après le bouton de rafraîchissement) -->\n        <div *ngIf=\"hasNotifications() | async\" class=\"select-all-checkbox\">\n          <label class=\"futuristic-checkbox\">\n            <input\n              type=\"checkbox\"\n              [checked]=\"allSelected\"\n              (click)=\"toggleSelectAll($event)\"\n            />\n            <span class=\"checkmark\"></span>\n          </label>\n        </div>\n\n        <!-- Bouton de filtrage des notifications non lues -->\n        <button\n          (click)=\"toggleUnreadFilter()\"\n          class=\"futuristic-action-button\"\n          [class.active]=\"showOnlyUnread\"\n          title=\"Filtrer les non lues\"\n        >\n          <i class=\"fas fa-filter\"></i>\n        </button>\n\n        <!-- Bouton pour activer/désactiver le son -->\n        <button\n          (click)=\"toggleSound()\"\n          class=\"futuristic-action-button\"\n          [class.active]=\"!isSoundMuted\"\n          title=\"{{ isSoundMuted ? 'Activer le son' : 'Désactiver le son' }}\"\n        >\n          <i\n            class=\"fas\"\n            [ngClass]=\"isSoundMuted ? 'fa-volume-mute' : 'fa-volume-up'\"\n          ></i>\n        </button>\n\n        <!-- Bouton pour marquer toutes les notifications comme lues -->\n        <button\n          *ngIf=\"(unreadCount$ | async) || 0\"\n          (click)=\"markAllAsRead()\"\n          class=\"futuristic-primary-button\"\n        >\n          <i class=\"fas fa-check-double mr-1\"></i> Tout marquer comme lu\n        </button>\n\n        <!-- Bouton pour supprimer toutes les notifications -->\n        <button\n          *ngIf=\"hasNotifications() | async\"\n          (click)=\"deleteAllNotifications()\"\n          class=\"futuristic-danger-button\"\n          title=\"Supprimer toutes les notifications\"\n        >\n          <i class=\"fas fa-trash-alt mr-1\"></i> Tout supprimer\n        </button>\n      </div>\n\n      <!-- Barre d'actions pour les notifications sélectionnées -->\n      <div class=\"flex space-x-2 selection-actions\" *ngIf=\"showSelectionBar\">\n        <span class=\"selection-count\"\n          >{{ selectedNotifications.size }} sélectionné(s)</span\n        >\n\n        <!-- Bouton pour marquer les notifications sélectionnées comme lues -->\n        <button\n          (click)=\"markSelectedAsRead()\"\n          class=\"futuristic-primary-button\"\n        >\n          <i class=\"fas fa-check mr-1\"></i> Marquer comme lu\n        </button>\n\n        <!-- Bouton pour supprimer les notifications sélectionnées -->\n        <button\n          (click)=\"deleteSelectedNotifications()\"\n          class=\"futuristic-danger-button\"\n        >\n          <i class=\"fas fa-trash-alt mr-1\"></i> Supprimer\n        </button>\n\n        <!-- Bouton pour annuler la sélection -->\n        <button\n          (click)=\"\n            selectedNotifications.clear();\n            showSelectionBar = false;\n            allSelected = false\n          \"\n          class=\"futuristic-cancel-button\"\n        >\n          <i class=\"fas fa-times mr-1\"></i> Annuler\n        </button>\n      </div>\n    </div>\n\n    <!-- État de chargement futuriste -->\n    <div *ngIf=\"loading\" class=\"futuristic-loading-container\">\n      <div class=\"futuristic-loading-circle\"></div>\n      <p class=\"futuristic-loading-text\">Chargement des notifications...</p>\n    </div>\n\n    <!-- État d'erreur futuriste -->\n    <div *ngIf=\"error\" class=\"futuristic-error-message\">\n      <div class=\"flex items-center\">\n        <i class=\"fas fa-exclamation-triangle futuristic-error-icon\"></i>\n        <div>\n          <h3 class=\"futuristic-error-title\">Erreur de chargement</h3>\n          <p class=\"futuristic-error-text\">{{ getErrorMessage() }}</p>\n        </div>\n        <button\n          (click)=\"loadNotifications()\"\n          class=\"futuristic-retry-button ml-auto\"\n        >\n          Réessayer\n        </button>\n      </div>\n    </div>\n\n    <!-- État vide futuriste -->\n    <div\n      *ngIf=\"!loading && !(hasNotifications() | async)\"\n      class=\"futuristic-empty-state\"\n    >\n      <div class=\"futuristic-empty-icon\">\n        <i class=\"fas fa-bell-slash\"></i>\n      </div>\n      <h3 class=\"futuristic-empty-title\">Aucune notification</h3>\n      <p class=\"futuristic-empty-text\">Vous êtes à jour !</p>\n      <button (click)=\"loadNotifications()\" class=\"futuristic-check-button\">\n        Vérifier les nouvelles notifications\n      </button>\n    </div>\n\n    <!-- Liste des notifications futuriste -->\n    <div\n      *ngIf=\"!loading && (hasNotifications() | async)\"\n      class=\"futuristic-notifications-list\"\n      #notificationContainer\n      (scroll)=\"onScroll(notificationContainer)\"\n    >\n      <ng-container *ngFor=\"let notification of filteredNotifications$ | async\">\n        <div\n          [class.futuristic-notification-unread]=\"!notification.isRead\"\n          [class.futuristic-notification-read]=\"notification.isRead\"\n          [class.futuristic-notification-selected]=\"isSelected(notification.id)\"\n          class=\"futuristic-notification-card\"\n        >\n          <!-- Case à cocher pour la sélection (déplacée en haut à gauche) -->\n          <div class=\"notification-checkbox\">\n            <label class=\"futuristic-checkbox\">\n              <input\n                type=\"checkbox\"\n                [checked]=\"isSelected(notification.id)\"\n                (click)=\"toggleSelection(notification.id, $event)\"\n              />\n              <span class=\"checkmark\"></span>\n            </label>\n          </div>\n\n          <!-- Avatar de l'expéditeur simplifié -->\n          <div class=\"notification-avatar\">\n            <img\n              [src]=\"\n                notification.senderId?.image ||\n                'assets/images/default-avatar.png'\n              \"\n              alt=\"Avatar\"\n              onerror=\"this.src='assets/images/default-avatar.png'\"\n            />\n          </div>\n\n          <!-- Contenu principal de la notification -->\n          <div class=\"notification-main-content\">\n            <!-- Contenu de notification simplifié -->\n            <div class=\"notification-content\">\n              <div class=\"notification-header\">\n                <div class=\"notification-header-top\">\n                  <span class=\"notification-sender\">{{\n                    notification.senderId?.username || \"Système\"\n                  }}</span>\n\n                  <!-- Heure de la notification (placée à droite du nom d'utilisateur) -->\n                  <div class=\"notification-time\">\n                    {{ notification.timestamp | date : \"shortTime\" }}\n                  </div>\n                </div>\n              </div>\n\n              <!-- Contenu du message (déplacé après l'en-tête) -->\n              <div class=\"notification-text-container\">\n                <span class=\"notification-text\">{{\n                  notification.content\n                }}</span>\n              </div>\n\n              <!-- Aperçu du message simplifié -->\n              <div\n                *ngIf=\"notification.message?.content\"\n                class=\"notification-message-preview\"\n              >\n                {{ notification.message?.content }}\n              </div>\n\n              <!-- Indicateur de pièces jointes -->\n              <div\n                *ngIf=\"notification.message?.attachments?.length\"\n                class=\"notification-attachments-indicator\"\n              >\n                <i class=\"fas fa-paperclip\"></i>\n                {{ notification.message?.attachments?.length }} pièce(s)\n                jointe(s)\n              </div>\n            </div>\n\n            <!-- Indicateur de non-lu (petit point bleu) -->\n            <div *ngIf=\"!notification.isRead\" class=\"unread-indicator\"></div>\n          </div>\n\n          <!-- Actions de notification -->\n          <div class=\"notification-actions\">\n            <!-- Bouton pour afficher les pièces jointes -->\n            <button\n              *ngIf=\"notification.message?.attachments?.length\"\n              (click)=\"\n                getNotificationAttachments(notification.id);\n                $event.stopPropagation()\n              \"\n              class=\"notification-action-button notification-attachment-button\"\n              title=\"Voir les pièces jointes\"\n            >\n              <i class=\"fas fa-paperclip\"></i>\n            </button>\n\n            <!-- Bouton pour rejoindre la conversation -->\n            <button\n              *ngIf=\"\n                notification.type === 'NEW_MESSAGE' ||\n                notification.type === 'GROUP_INVITE' ||\n                notification.type === 'MESSAGE_REACTION'\n              \"\n              (click)=\"joinConversation(notification); $event.stopPropagation()\"\n              class=\"notification-action-button notification-join-button\"\n              title=\"Rejoindre la conversation\"\n              [disabled]=\"loading\"\n            >\n              <i class=\"fas fa-comments\" *ngIf=\"!loading\"></i>\n              <i class=\"fas fa-spinner fa-spin\" *ngIf=\"loading\"></i>\n            </button>\n\n            <!-- Bouton pour voir les détails de la notification -->\n            <button\n              (click)=\"\n                openNotificationDetails(notification); $event.stopPropagation()\n              \"\n              class=\"notification-action-button notification-details-button\"\n              title=\"Voir les détails (ne marque PAS comme lu automatiquement)\"\n            >\n              <i class=\"fas fa-info-circle\"></i>\n            </button>\n\n            <!-- Bouton marquer comme lu -->\n            <button\n              *ngIf=\"!notification.isRead\"\n              (click)=\"markAsRead(notification.id); $event.stopPropagation()\"\n              class=\"notification-action-button notification-read-button\"\n              title=\"Marquer cette notification comme lue\"\n            >\n              <i class=\"fas fa-check\"></i>\n            </button>\n\n            <!-- Bouton pour supprimer la notification -->\n            <button\n              (click)=\"\n                deleteNotification(notification.id); $event.stopPropagation()\n              \"\n              class=\"notification-action-button notification-delete-button\"\n              title=\"Supprimer cette notification\"\n            >\n              <i class=\"fas fa-trash-alt\"></i>\n            </button>\n          </div>\n        </div>\n      </ng-container>\n\n      <!-- Indicateur de chargement des anciennes notifications -->\n      <div *ngIf=\"loadingMore\" class=\"futuristic-loading-more\">\n        <div class=\"futuristic-loading-circle-small\"></div>\n        <p class=\"futuristic-loading-text-small\">\n          Chargement des notifications plus anciennes...\n        </p>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Modal pour afficher les pièces jointes -->\n<div\n  class=\"futuristic-modal-overlay\"\n  [style.display]=\"showAttachmentsModal ? 'flex' : 'none'\"\n  (click)=\"closeAttachmentsModal()\"\n>\n  <div class=\"futuristic-modal-container\" (click)=\"$event.stopPropagation()\">\n    <div class=\"futuristic-modal-header\">\n      <h3 class=\"futuristic-modal-title\">\n        <i class=\"fas fa-paperclip mr-2\"></i>\n        Pièces jointes\n      </h3>\n      <button class=\"futuristic-modal-close\" (click)=\"closeAttachmentsModal()\">\n        <i class=\"fas fa-times\"></i>\n      </button>\n    </div>\n    <div class=\"futuristic-modal-body\">\n      <div *ngIf=\"loadingAttachments\" class=\"futuristic-loading-container\">\n        <div class=\"futuristic-loading-circle\"></div>\n        <p class=\"futuristic-loading-text\">Chargement des pièces jointes...</p>\n      </div>\n\n      <div\n        *ngIf=\"!loadingAttachments && currentAttachments.length === 0\"\n        class=\"futuristic-empty-state\"\n      >\n        <div class=\"futuristic-empty-icon\">\n          <i class=\"fas fa-file-alt\"></i>\n        </div>\n        <h3 class=\"futuristic-empty-title\">Aucune pièce jointe</h3>\n        <p class=\"futuristic-empty-text\">\n          Aucune pièce jointe n'a été trouvée pour cette notification.\n        </p>\n      </div>\n\n      <div\n        *ngIf=\"!loadingAttachments && currentAttachments.length > 0\"\n        class=\"futuristic-attachments-list\"\n      >\n        <div\n          *ngFor=\"let attachment of currentAttachments\"\n          class=\"futuristic-attachment-item\"\n        >\n          <!-- Image -->\n          <div\n            *ngIf=\"isImage(attachment.type)\"\n            class=\"futuristic-attachment-preview\"\n          >\n            <img\n              [src]=\"attachment.url\"\n              alt=\"Image\"\n              (click)=\"openAttachment(attachment.url)\"\n            />\n          </div>\n\n          <!-- Document -->\n          <div\n            *ngIf=\"!isImage(attachment.type)\"\n            class=\"futuristic-attachment-icon\"\n          >\n            <i [class]=\"getFileIcon(attachment.type)\"></i>\n          </div>\n\n          <div class=\"futuristic-attachment-info\">\n            <div class=\"futuristic-attachment-name\">\n              {{ attachment.name || \"Pièce jointe\" }}\n            </div>\n            <div class=\"futuristic-attachment-meta\">\n              <span class=\"futuristic-attachment-type\">{{\n                getFileTypeLabel(attachment.type)\n              }}</span>\n              <span\n                *ngIf=\"attachment.size\"\n                class=\"futuristic-attachment-size\"\n                >{{ formatFileSize(attachment.size) }}</span\n              >\n            </div>\n          </div>\n\n          <div class=\"futuristic-attachment-actions\">\n            <button\n              class=\"futuristic-attachment-button\"\n              (click)=\"openAttachment(attachment.url)\"\n              title=\"Ouvrir\"\n            >\n              <i class=\"fas fa-external-link-alt\"></i>\n            </button>\n            <button\n              class=\"futuristic-attachment-button\"\n              (click)=\"downloadAttachment(attachment)\"\n              title=\"Télécharger\"\n            >\n              <i class=\"fas fa-download\"></i>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Modal pour afficher les détails de notification -->\n<div\n  class=\"futuristic-modal-overlay\"\n  [style.display]=\"showNotificationDetailsModal ? 'flex' : 'none'\"\n  (click)=\"closeNotificationDetailsModal()\"\n>\n  <div class=\"futuristic-modal-container\" (click)=\"$event.stopPropagation()\">\n    <div class=\"futuristic-modal-header\">\n      <h3 class=\"futuristic-modal-title\">\n        <i class=\"fas fa-info-circle mr-2\"></i>\n        Détails de la notification\n      </h3>\n      <button\n        class=\"futuristic-modal-close\"\n        (click)=\"closeNotificationDetailsModal()\"\n      >\n        <i class=\"fas fa-times\"></i>\n      </button>\n    </div>\n    <div class=\"futuristic-modal-body\" *ngIf=\"currentNotification\">\n      <!-- Informations de l'expéditeur -->\n      <div class=\"notification-detail-section\">\n        <h4 class=\"notification-detail-title\">\n          <i class=\"fas fa-user mr-2\"></i>\n          Expéditeur\n        </h4>\n        <div class=\"notification-sender-info\">\n          <img\n            [src]=\"\n              currentNotification.senderId?.image ||\n              'assets/images/default-avatar.png'\n            \"\n            alt=\"Avatar\"\n            class=\"notification-sender-avatar\"\n            onerror=\"this.src='assets/images/default-avatar.png'\"\n          />\n          <div class=\"notification-sender-details\">\n            <span class=\"notification-sender-name\">\n              {{ currentNotification.senderId?.username || \"Système\" }}\n            </span>\n            <span class=\"notification-timestamp\">\n              {{ currentNotification.timestamp | date : \"medium\" }}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Contenu de la notification -->\n      <div class=\"notification-detail-section\">\n        <h4 class=\"notification-detail-title\">\n          <i class=\"fas fa-message mr-2\"></i>\n          Message\n        </h4>\n        <div class=\"notification-content-detail\">\n          {{ currentNotification.content }}\n        </div>\n        <div\n          *ngIf=\"currentNotification.message?.content\"\n          class=\"notification-message-detail\"\n        >\n          <strong>Message original :</strong>\n          {{ currentNotification.message?.content }}\n        </div>\n      </div>\n\n      <!-- Type et statut -->\n      <div class=\"notification-detail-section\">\n        <h4 class=\"notification-detail-title\">\n          <i class=\"fas fa-tag mr-2\"></i>\n          Informations\n        </h4>\n        <div class=\"notification-info-grid\">\n          <div class=\"notification-info-item\">\n            <span class=\"notification-info-label\">Type :</span>\n            <span class=\"notification-info-value\">{{\n              currentNotification.type\n            }}</span>\n          </div>\n          <div class=\"notification-info-item\">\n            <span class=\"notification-info-label\">Statut :</span>\n            <span\n              class=\"notification-info-value\"\n              [class.text-green-500]=\"currentNotification.isRead\"\n              [class.text-orange-500]=\"!currentNotification.isRead\"\n            >\n              {{ currentNotification.isRead ? \"Lu\" : \"Non lu\" }}\n            </span>\n          </div>\n          <div\n            class=\"notification-info-item\"\n            *ngIf=\"currentNotification.readAt\"\n          >\n            <span class=\"notification-info-label\">Lu le :</span>\n            <span class=\"notification-info-value\">{{\n              currentNotification.readAt | date : \"medium\"\n            }}</span>\n          </div>\n          <div\n            class=\"notification-info-item\"\n            *ngIf=\"!currentNotification.isRead\"\n            style=\"\n              background: rgba(255, 140, 0, 0.1);\n              border: 1px solid rgba(255, 140, 0, 0.3);\n            \"\n          >\n            <span class=\"notification-info-label\">\n              <i class=\"fas fa-info-circle mr-1\"></i>\n              Note :\n            </span>\n            <span\n              class=\"notification-info-value\"\n              style=\"color: #ff8c00; font-style: italic\"\n            >\n              Ouvrir les détails ne marque pas automatiquement comme lu\n            </span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Pièces jointes -->\n      <div\n        class=\"notification-detail-section\"\n        *ngIf=\"currentAttachments.length > 0\"\n      >\n        <h4 class=\"notification-detail-title\">\n          <i class=\"fas fa-paperclip mr-2\"></i>\n          Pièces jointes ({{ currentAttachments.length }})\n        </h4>\n        <div class=\"notification-attachments-grid\">\n          <div\n            *ngFor=\"let attachment of currentAttachments\"\n            class=\"notification-attachment-item\"\n          >\n            <!-- Image -->\n            <div\n              *ngIf=\"isImage(attachment.type)\"\n              class=\"notification-attachment-preview\"\n            >\n              <img\n                [src]=\"attachment.url\"\n                alt=\"Image\"\n                (click)=\"openAttachment(attachment.url)\"\n              />\n            </div>\n\n            <!-- Document -->\n            <div\n              *ngIf=\"!isImage(attachment.type)\"\n              class=\"notification-attachment-icon\"\n            >\n              <i [class]=\"getFileIcon(attachment.type)\"></i>\n            </div>\n\n            <div class=\"notification-attachment-info\">\n              <div class=\"notification-attachment-name\">\n                {{ attachment.name || \"Pièce jointe\" }}\n              </div>\n              <div class=\"notification-attachment-meta\">\n                <span class=\"notification-attachment-type\">{{\n                  getFileTypeLabel(attachment.type)\n                }}</span>\n                <span\n                  *ngIf=\"attachment.size\"\n                  class=\"notification-attachment-size\"\n                  >{{ formatFileSize(attachment.size) }}</span\n                >\n              </div>\n            </div>\n\n            <div class=\"notification-attachment-actions\">\n              <button\n                class=\"notification-attachment-button\"\n                (click)=\"openAttachment(attachment.url)\"\n                title=\"Ouvrir\"\n              >\n                <i class=\"fas fa-external-link-alt\"></i>\n              </button>\n              <button\n                class=\"notification-attachment-button\"\n                (click)=\"downloadAttachment(attachment)\"\n                title=\"Télécharger\"\n              >\n                <i class=\"fas fa-download\"></i>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Actions -->\n      <div class=\"notification-detail-actions\">\n        <button\n          *ngIf=\"\n            currentNotification.type === 'NEW_MESSAGE' ||\n            currentNotification.type === 'GROUP_INVITE' ||\n            currentNotification.type === 'MESSAGE_REACTION'\n          \"\n          (click)=\"\n            joinConversation(currentNotification);\n            closeNotificationDetailsModal()\n          \"\n          class=\"futuristic-primary-button\"\n          [disabled]=\"loading\"\n        >\n          <i class=\"fas fa-comments mr-2\" *ngIf=\"!loading\"></i>\n          <i class=\"fas fa-spinner fa-spin mr-2\" *ngIf=\"loading\"></i>\n          Rejoindre la conversation\n        </button>\n\n        <button\n          *ngIf=\"!currentNotification.isRead\"\n          (click)=\"markAsRead(currentNotification.id)\"\n          class=\"futuristic-secondary-button\"\n        >\n          <i class=\"fas fa-check mr-2\"></i>\n          Marquer comme lu\n        </button>\n\n        <button\n          (click)=\"\n            deleteNotification(currentNotification.id);\n            closeNotificationDetailsModal()\n          \"\n          class=\"futuristic-danger-button\"\n        >\n          <i class=\"fas fa-trash-alt mr-2\"></i>\n          Supprimer\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAUA,SAAqBA,OAAO,EAAEC,EAAE,EAAEC,eAAe,QAAQ,MAAM;AAC/D,SAKEC,WAAW,QACN,8BAA8B;AACrC,SACEC,UAAU,EACVC,GAAG,EACHC,SAAS,EACTC,IAAI,EACJC,YAAY,EACZC,oBAAoB,EACpBC,MAAM,QACD,gBAAgB;;;;;;;;;;ICEfC,EAAA,CAAAC,cAAA,cAAoE;IAK9DD,EAAA,CAAAE,UAAA,mBAAAC,sEAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAF,OAAA,CAAAG,eAAA,CAAAN,MAAA,CAAuB;IAAA,EAAC;IAHnCJ,EAAA,CAAAW,YAAA,EAIE;IACFX,EAAA,CAAAY,SAAA,eAA+B;IACjCZ,EAAA,CAAAW,YAAA,EAAQ;;;;IAJJX,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAc,UAAA,YAAAC,OAAA,CAAAC,WAAA,CAAuB;;;;;;IA+B7BhB,EAAA,CAAAC,cAAA,iBAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAAe,0EAAA;MAAAjB,EAAA,CAAAK,aAAA,CAAAa,IAAA;MAAA,MAAAC,OAAA,GAAAnB,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAU,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAGzBpB,EAAA,CAAAY,SAAA,YAAwC;IAACZ,EAAA,CAAAqB,MAAA,8BAC3C;IAAArB,EAAA,CAAAW,YAAA,EAAS;;;;;;IAGTX,EAAA,CAAAC,cAAA,iBAKC;IAHCD,EAAA,CAAAE,UAAA,mBAAAoB,2EAAA;MAAAtB,EAAA,CAAAK,aAAA,CAAAkB,IAAA;MAAA,MAAAC,OAAA,GAAAxB,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAe,OAAA,CAAAC,sBAAA,EAAwB;IAAA,EAAC;IAIlCzB,EAAA,CAAAY,SAAA,YAAqC;IAACZ,EAAA,CAAAqB,MAAA,uBACxC;IAAArB,EAAA,CAAAW,YAAA,EAAS;;;;;;IA9DXX,EAAA,CAAAC,cAAA,cAAsD;IAGlDD,EAAA,CAAAE,UAAA,mBAAAwB,iEAAA;MAAA1B,EAAA,CAAAK,aAAA,CAAAsB,IAAA;MAAA,MAAAC,OAAA,GAAA5B,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAmB,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IAI7B7B,EAAA,CAAAY,SAAA,YAA+B;IACjCZ,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAA8B,UAAA,IAAAC,8CAAA,kBASM;;IAGN/B,EAAA,CAAAC,cAAA,iBAKC;IAJCD,EAAA,CAAAE,UAAA,mBAAA8B,iEAAA;MAAAhC,EAAA,CAAAK,aAAA,CAAAsB,IAAA;MAAA,MAAAM,OAAA,GAAAjC,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAwB,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAK9BlC,EAAA,CAAAY,SAAA,YAA6B;IAC/BZ,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,iBAKC;IAJCD,EAAA,CAAAE,UAAA,mBAAAiC,iEAAA;MAAAnC,EAAA,CAAAK,aAAA,CAAAsB,IAAA;MAAA,MAAAS,OAAA,GAAApC,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA2B,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAKvBrC,EAAA,CAAAY,SAAA,YAGK;IACPZ,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAA8B,UAAA,IAAAQ,iDAAA,qBAMS;;IAGTtC,EAAA,CAAA8B,UAAA,KAAAS,kDAAA,qBAOS;;IACXvC,EAAA,CAAAW,YAAA,EAAM;;;;IApDEX,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAc,UAAA,SAAAd,EAAA,CAAAwC,WAAA,OAAAC,MAAA,CAAAC,gBAAA,IAAgC;IAepC1C,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAA2C,WAAA,WAAAF,MAAA,CAAAG,cAAA,CAA+B;IAU/B5C,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAA2C,WAAA,YAAAF,MAAA,CAAAI,YAAA,CAA8B;IAC9B7C,EAAA,CAAA8C,qBAAA,UAAAL,MAAA,CAAAI,YAAA,+CAAmE;IAIjE7C,EAAA,CAAAa,SAAA,GAA4D;IAA5Db,EAAA,CAAAc,UAAA,YAAA2B,MAAA,CAAAI,YAAA,qCAA4D;IAM7D7C,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAAc,UAAA,SAAAd,EAAA,CAAAwC,WAAA,SAAAC,MAAA,CAAAM,YAAA,OAAiC;IASjC/C,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAc,UAAA,SAAAd,EAAA,CAAAwC,WAAA,SAAAC,MAAA,CAAAC,gBAAA,IAAgC;;;;;;IAUrC1C,EAAA,CAAAC,cAAA,cAAuE;IAElED,EAAA,CAAAqB,MAAA,GAA+C;IAAArB,EAAA,CAAAW,YAAA,EACjD;IAGDX,EAAA,CAAAC,cAAA,iBAGC;IAFCD,EAAA,CAAAE,UAAA,mBAAA8C,iEAAA;MAAAhD,EAAA,CAAAK,aAAA,CAAA4C,IAAA;MAAA,MAAAC,OAAA,GAAAlD,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAyC,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAG9BnD,EAAA,CAAAY,SAAA,YAAiC;IAACZ,EAAA,CAAAqB,MAAA,yBACpC;IAAArB,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,iBAGC;IAFCD,EAAA,CAAAE,UAAA,mBAAAkD,iEAAA;MAAApD,EAAA,CAAAK,aAAA,CAAA4C,IAAA;MAAA,MAAAI,OAAA,GAAArD,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA4C,OAAA,CAAAC,2BAAA,EAA6B;IAAA,EAAC;IAGvCtD,EAAA,CAAAY,SAAA,YAAqC;IAACZ,EAAA,CAAAqB,MAAA,kBACxC;IAAArB,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,iBAOC;IANCD,EAAA,CAAAE,UAAA,mBAAAqD,iEAAA;MAAAvD,EAAA,CAAAK,aAAA,CAAA4C,IAAA;MAAA,MAAAO,OAAA,GAAAxD,EAAA,CAAAQ,aAAA;MACegD,OAAA,CAAAC,qBAAA,CAAAC,KAAA,EACd;MAAAF,OAAA,CAAAG,gBAAA,GACL,KAAK;MAAA,OAAA3D,EAAA,CAAAS,WAAA,CAAA+C,OAAA,CAAAxC,WAAA,GACJ,KACP;IAAA,EADW;IAGDhB,EAAA,CAAAY,SAAA,aAAiC;IAACZ,EAAA,CAAAqB,MAAA,iBACpC;IAAArB,EAAA,CAAAW,YAAA,EAAS;;;;IA7BNX,EAAA,CAAAa,SAAA,GAA+C;IAA/Cb,EAAA,CAAA4D,kBAAA,KAAAC,MAAA,CAAAJ,qBAAA,CAAAK,IAAA,8BAA+C;;;;;IAkCtD9D,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAAY,SAAA,cAA6C;IAC7CZ,EAAA,CAAAC,cAAA,YAAmC;IAAAD,EAAA,CAAAqB,MAAA,sCAA+B;IAAArB,EAAA,CAAAW,YAAA,EAAI;;;;;;IAIxEX,EAAA,CAAAC,cAAA,cAAoD;IAEhDD,EAAA,CAAAY,SAAA,YAAiE;IACjEZ,EAAA,CAAAC,cAAA,UAAK;IACgCD,EAAA,CAAAqB,MAAA,2BAAoB;IAAArB,EAAA,CAAAW,YAAA,EAAK;IAC5DX,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAqB,MAAA,GAAuB;IAAArB,EAAA,CAAAW,YAAA,EAAI;IAE9DX,EAAA,CAAAC,cAAA,iBAGC;IAFCD,EAAA,CAAAE,UAAA,mBAAA6D,kEAAA;MAAA/D,EAAA,CAAAK,aAAA,CAAA2D,IAAA;MAAA,MAAAC,OAAA,GAAAjE,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAwD,OAAA,CAAApC,iBAAA,EAAmB;IAAA,EAAC;IAG7B7B,EAAA,CAAAqB,MAAA,uBACF;IAAArB,EAAA,CAAAW,YAAA,EAAS;;;;IAP0BX,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAkE,iBAAA,CAAAC,MAAA,CAAAC,eAAA,GAAuB;;;;;;IAY9DpE,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAY,SAAA,YAAiC;IACnCZ,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAqB,MAAA,0BAAmB;IAAArB,EAAA,CAAAW,YAAA,EAAK;IAC3DX,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAqB,MAAA,mCAAkB;IAAArB,EAAA,CAAAW,YAAA,EAAI;IACvDX,EAAA,CAAAC,cAAA,iBAAsE;IAA9DD,EAAA,CAAAE,UAAA,mBAAAmE,kEAAA;MAAArE,EAAA,CAAAK,aAAA,CAAAiE,IAAA;MAAA,MAAAC,OAAA,GAAAvE,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA8D,OAAA,CAAA1C,iBAAA,EAAmB;IAAA,EAAC;IACnC7B,EAAA,CAAAqB,MAAA,kDACF;IAAArB,EAAA,CAAAW,YAAA,EAAS;;;;;IAkEDX,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4D,kBAAA,MAAAY,gBAAA,CAAAC,OAAA,kBAAAD,gBAAA,CAAAC,OAAA,CAAAC,OAAA,MACF;;;;;IAGA1E,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAY,SAAA,YAAgC;IAChCZ,EAAA,CAAAqB,MAAA,GAEF;IAAArB,EAAA,CAAAW,YAAA,EAAM;;;;IAFJX,EAAA,CAAAa,SAAA,GAEF;IAFEb,EAAA,CAAA4D,kBAAA,MAAAY,gBAAA,CAAAC,OAAA,kBAAAD,gBAAA,CAAAC,OAAA,CAAAE,WAAA,kBAAAH,gBAAA,CAAAC,OAAA,CAAAE,WAAA,CAAAC,MAAA,8BAEF;;;;;IAIF5E,EAAA,CAAAY,SAAA,cAAiE;;;;;;IAMjEZ,EAAA,CAAAC,cAAA,iBAQC;IANCD,EAAA,CAAAE,UAAA,mBAAA2E,2FAAAzE,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAyE,IAAA;MAAA,MAAAN,gBAAA,GAAAxE,EAAA,CAAAQ,aAAA,GAAAuE,SAAA;MAAA,MAAAC,OAAA,GAAAhF,EAAA,CAAAQ,aAAA;MACmBwE,OAAA,CAAAC,0BAAA,CAAAT,gBAAA,CAAAU,EAAA,CAClB;MAAA,OAAkBlF,EAAA,CAAAS,WAAA,CAAAL,MAAA,CAAA+E,eAAA,EAEjC;IAAA,EADe;IAIDnF,EAAA,CAAAY,SAAA,YAAgC;IAClCZ,EAAA,CAAAW,YAAA,EAAS;;;;;IAcPX,EAAA,CAAAY,SAAA,YAAgD;;;;;IAChDZ,EAAA,CAAAY,SAAA,YAAsD;;;;;;IAZxDZ,EAAA,CAAAC,cAAA,iBAUC;IAJCD,EAAA,CAAAE,UAAA,mBAAAkF,2FAAAhF,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAgF,IAAA;MAAA,MAAAb,gBAAA,GAAAxE,EAAA,CAAAQ,aAAA,GAAAuE,SAAA;MAAA,MAAAO,OAAA,GAAAtF,EAAA,CAAAQ,aAAA;MAAS8E,OAAA,CAAAC,gBAAA,CAAAf,gBAAA,CAA8B;MAAA,OAAExE,EAAA,CAAAS,WAAA,CAAAL,MAAA,CAAA+E,eAAA,EAAwB;IAAA,EAAC;IAKlEnF,EAAA,CAAA8B,UAAA,IAAA0D,sEAAA,gBAAgD;IAChDxF,EAAA,CAAA8B,UAAA,IAAA2D,sEAAA,gBAAsD;IACxDzF,EAAA,CAAAW,YAAA,EAAS;;;;IAJPX,EAAA,CAAAc,UAAA,aAAA4E,OAAA,CAAAC,OAAA,CAAoB;IAEQ3F,EAAA,CAAAa,SAAA,GAAc;IAAdb,EAAA,CAAAc,UAAA,UAAA4E,OAAA,CAAAC,OAAA,CAAc;IACP3F,EAAA,CAAAa,SAAA,GAAa;IAAbb,EAAA,CAAAc,UAAA,SAAA4E,OAAA,CAAAC,OAAA,CAAa;;;;;;IAelD3F,EAAA,CAAAC,cAAA,iBAKC;IAHCD,EAAA,CAAAE,UAAA,mBAAA0F,2FAAAxF,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAwF,IAAA;MAAA,MAAArB,gBAAA,GAAAxE,EAAA,CAAAQ,aAAA,GAAAuE,SAAA;MAAA,MAAAe,OAAA,GAAA9F,EAAA,CAAAQ,aAAA;MAASsF,OAAA,CAAAC,UAAA,CAAAvB,gBAAA,CAAAU,EAAA,CAA2B;MAAA,OAAElF,EAAA,CAAAS,WAAA,CAAAL,MAAA,CAAA+E,eAAA,EAAwB;IAAA,EAAC;IAI/DnF,EAAA,CAAAY,SAAA,aAA4B;IAC9BZ,EAAA,CAAAW,YAAA,EAAS;;;;;;IAhIfX,EAAA,CAAAgG,uBAAA,GAA0E;IACxEhG,EAAA,CAAAC,cAAA,cAKC;IAOOD,EAAA,CAAAE,UAAA,mBAAA+F,gFAAA7F,MAAA;MAAA,MAAA8F,WAAA,GAAAlG,EAAA,CAAAK,aAAA,CAAA8F,IAAA;MAAA,MAAA3B,gBAAA,GAAA0B,WAAA,CAAAnB,SAAA;MAAA,MAAAqB,OAAA,GAAApG,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA2F,OAAA,CAAAC,eAAA,CAAA7B,gBAAA,CAAAU,EAAA,EAAA9E,MAAA,CAAwC;IAAA,EAAC;IAHpDJ,EAAA,CAAAW,YAAA,EAIE;IACFX,EAAA,CAAAY,SAAA,eAA+B;IACjCZ,EAAA,CAAAW,YAAA,EAAQ;IAIVX,EAAA,CAAAC,cAAA,cAAiC;IAC/BD,EAAA,CAAAY,SAAA,cAOE;IACJZ,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAC,cAAA,cAAuC;IAKGD,EAAA,CAAAqB,MAAA,IAEhC;IAAArB,EAAA,CAAAW,YAAA,EAAO;IAGTX,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAAqB,MAAA,IACF;;IAAArB,EAAA,CAAAW,YAAA,EAAM;IAKVX,EAAA,CAAAC,cAAA,eAAyC;IACPD,EAAA,CAAAqB,MAAA,IAE9B;IAAArB,EAAA,CAAAW,YAAA,EAAO;IAIXX,EAAA,CAAA8B,UAAA,KAAAwE,+DAAA,kBAKM;IAGNtG,EAAA,CAAA8B,UAAA,KAAAyE,+DAAA,kBAOM;IACRvG,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAA8B,UAAA,KAAA0E,+DAAA,kBAAiE;IACnExG,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAC,cAAA,eAAkC;IAEhCD,EAAA,CAAA8B,UAAA,KAAA2E,kEAAA,qBAUS;IAGTzG,EAAA,CAAA8B,UAAA,KAAA4E,kEAAA,qBAaS;IAGT1G,EAAA,CAAAC,cAAA,kBAMC;IALCD,EAAA,CAAAE,UAAA,mBAAAyG,kFAAAvG,MAAA;MAAA,MAAA8F,WAAA,GAAAlG,EAAA,CAAAK,aAAA,CAAA8F,IAAA;MAAA,MAAA3B,gBAAA,GAAA0B,WAAA,CAAAnB,SAAA;MAAA,MAAA6B,OAAA,GAAA5G,EAAA,CAAAQ,aAAA;MACmBoG,OAAA,CAAAC,uBAAA,CAAArC,gBAAA,CAAqC;MAAA,OAAExE,EAAA,CAAAS,WAAA,CAAAL,MAAA,CAAA+E,eAAA,EAExE;IAAA,EADe;IAIDnF,EAAA,CAAAY,SAAA,aAAkC;IACpCZ,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAA8B,UAAA,KAAAgF,kEAAA,qBAOS;IAGT9G,EAAA,CAAAC,cAAA,kBAMC;IALCD,EAAA,CAAAE,UAAA,mBAAA6G,kFAAA3G,MAAA;MAAA,MAAA8F,WAAA,GAAAlG,EAAA,CAAAK,aAAA,CAAA8F,IAAA;MAAA,MAAA3B,gBAAA,GAAA0B,WAAA,CAAAnB,SAAA;MAAA,MAAAiC,OAAA,GAAAhH,EAAA,CAAAQ,aAAA;MACmBwG,OAAA,CAAAC,kBAAA,CAAAzC,gBAAA,CAAAU,EAAA,CAAmC;MAAA,OAAElF,EAAA,CAAAS,WAAA,CAAAL,MAAA,CAAA+E,eAAA,EAEtE;IAAA,EADe;IAIDnF,EAAA,CAAAY,SAAA,aAAgC;IAClCZ,EAAA,CAAAW,YAAA,EAAS;IAGfX,EAAA,CAAAkH,qBAAA,EAAe;;;;;IA5IXlH,EAAA,CAAAa,SAAA,GAA6D;IAA7Db,EAAA,CAAA2C,WAAA,oCAAA6B,gBAAA,CAAA2C,MAAA,CAA6D,iCAAA3C,gBAAA,CAAA2C,MAAA,sCAAAC,OAAA,CAAAC,UAAA,CAAA7C,gBAAA,CAAAU,EAAA;IAUvDlF,EAAA,CAAAa,SAAA,GAAuC;IAAvCb,EAAA,CAAAc,UAAA,YAAAsG,OAAA,CAAAC,UAAA,CAAA7C,gBAAA,CAAAU,EAAA,EAAuC;IAUzClF,EAAA,CAAAa,SAAA,GAGC;IAHDb,EAAA,CAAAc,UAAA,SAAA0D,gBAAA,CAAA8C,QAAA,kBAAA9C,gBAAA,CAAA8C,QAAA,CAAAC,KAAA,yCAAAvH,EAAA,CAAAwH,aAAA,CAGC;IAYqCxH,EAAA,CAAAa,SAAA,GAEhC;IAFgCb,EAAA,CAAAkE,iBAAA,EAAAM,gBAAA,CAAA8C,QAAA,kBAAA9C,gBAAA,CAAA8C,QAAA,CAAAG,QAAA,oBAEhC;IAIAzH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4D,kBAAA,MAAA5D,EAAA,CAAA0H,WAAA,SAAAlD,gBAAA,CAAAmD,SAAA,oBACF;IAM8B3H,EAAA,CAAAa,SAAA,GAE9B;IAF8Bb,EAAA,CAAAkE,iBAAA,CAAAM,gBAAA,CAAAE,OAAA,CAE9B;IAKD1E,EAAA,CAAAa,SAAA,GAAmC;IAAnCb,EAAA,CAAAc,UAAA,SAAA0D,gBAAA,CAAAC,OAAA,kBAAAD,gBAAA,CAAAC,OAAA,CAAAC,OAAA,CAAmC;IAQnC1E,EAAA,CAAAa,SAAA,GAA+C;IAA/Cb,EAAA,CAAAc,UAAA,SAAA0D,gBAAA,CAAAC,OAAA,kBAAAD,gBAAA,CAAAC,OAAA,CAAAE,WAAA,kBAAAH,gBAAA,CAAAC,OAAA,CAAAE,WAAA,CAAAC,MAAA,CAA+C;IAU9C5E,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAc,UAAA,UAAA0D,gBAAA,CAAA2C,MAAA,CAA0B;IAO7BnH,EAAA,CAAAa,SAAA,GAA+C;IAA/Cb,EAAA,CAAAc,UAAA,SAAA0D,gBAAA,CAAAC,OAAA,kBAAAD,gBAAA,CAAAC,OAAA,CAAAE,WAAA,kBAAAH,gBAAA,CAAAC,OAAA,CAAAE,WAAA,CAAAC,MAAA,CAA+C;IAa/C5E,EAAA,CAAAa,SAAA,GAKf;IALeb,EAAA,CAAAc,UAAA,SAAA0D,gBAAA,CAAAoD,IAAA,sBAAApD,gBAAA,CAAAoD,IAAA,uBAAApD,gBAAA,CAAAoD,IAAA,wBAKf;IAsBe5H,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAc,UAAA,UAAA0D,gBAAA,CAAA2C,MAAA,CAA0B;;;;;IAuBnCnH,EAAA,CAAAC,cAAA,eAAyD;IACvDD,EAAA,CAAAY,SAAA,eAAmD;IACnDZ,EAAA,CAAAC,cAAA,aAAyC;IACvCD,EAAA,CAAAqB,MAAA,uDACF;IAAArB,EAAA,CAAAW,YAAA,EAAI;;;;;;IA3JRX,EAAA,CAAAC,cAAA,kBAKC;IADCD,EAAA,CAAAE,UAAA,oBAAA2H,gEAAA;MAAA7H,EAAA,CAAAK,aAAA,CAAAyH,IAAA;MAAA,MAAAC,IAAA,GAAA/H,EAAA,CAAAgI,WAAA;MAAA,MAAAC,OAAA,GAAAjI,EAAA,CAAAQ,aAAA;MAAA,OAAUR,EAAA,CAAAS,WAAA,CAAAwH,OAAA,CAAAC,QAAA,CAAAH,IAAA,CAA+B;IAAA,EAAC;IAE1C/H,EAAA,CAAA8B,UAAA,IAAAqG,wDAAA,6BA8Ie;;IAGfnI,EAAA,CAAA8B,UAAA,IAAAsG,+CAAA,kBAKM;IACRpI,EAAA,CAAAW,YAAA,EAAM;;;;IAvJmCX,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAwC,WAAA,OAAA6F,MAAA,CAAAC,sBAAA,EAAiC;IAiJlEtI,EAAA,CAAAa,SAAA,GAAiB;IAAjBb,EAAA,CAAAc,UAAA,SAAAuH,MAAA,CAAAE,WAAA,CAAiB;;;;;IA2BvBvI,EAAA,CAAAC,cAAA,cAAqE;IACnED,EAAA,CAAAY,SAAA,cAA6C;IAC7CZ,EAAA,CAAAC,cAAA,YAAmC;IAAAD,EAAA,CAAAqB,MAAA,4CAAgC;IAAArB,EAAA,CAAAW,YAAA,EAAI;;;;;IAGzEX,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAY,SAAA,aAA+B;IACjCZ,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAqB,MAAA,+BAAmB;IAAArB,EAAA,CAAAW,YAAA,EAAK;IAC3DX,EAAA,CAAAC,cAAA,YAAiC;IAC/BD,EAAA,CAAAqB,MAAA,yFACF;IAAArB,EAAA,CAAAW,YAAA,EAAI;;;;;;IAYFX,EAAA,CAAAC,cAAA,eAGC;IAIGD,EAAA,CAAAE,UAAA,mBAAAsI,2EAAA;MAAAxI,EAAA,CAAAK,aAAA,CAAAoI,IAAA;MAAA,MAAAC,cAAA,GAAA1I,EAAA,CAAAQ,aAAA,GAAAuE,SAAA;MAAA,MAAA4D,OAAA,GAAA3I,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAkI,OAAA,CAAAC,cAAA,CAAAF,cAAA,CAAAG,GAAA,CAA8B;IAAA,EAAC;IAH1C7I,EAAA,CAAAW,YAAA,EAIE;;;;IAHAX,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAAc,UAAA,QAAA4H,cAAA,CAAAG,GAAA,EAAA7I,EAAA,CAAAwH,aAAA,CAAsB;;;;;IAO1BxH,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAY,SAAA,QAA8C;IAChDZ,EAAA,CAAAW,YAAA,EAAM;;;;;IADDX,EAAA,CAAAa,SAAA,GAAsC;IAAtCb,EAAA,CAAA8I,UAAA,CAAAC,OAAA,CAAAC,WAAA,CAAAN,cAAA,CAAAd,IAAA,EAAsC;;;;;IAWvC5H,EAAA,CAAAC,cAAA,gBAGG;IAAAD,EAAA,CAAAqB,MAAA,GAAqC;IAAArB,EAAA,CAAAW,YAAA,EACvC;;;;;IADEX,EAAA,CAAAa,SAAA,GAAqC;IAArCb,EAAA,CAAAkE,iBAAA,CAAA+E,OAAA,CAAAC,cAAA,CAAAR,cAAA,CAAA5E,IAAA,EAAqC;;;;;;IAnC9C9D,EAAA,CAAAC,cAAA,eAGC;IAECD,EAAA,CAAA8B,UAAA,IAAAqH,qDAAA,mBASM;IAGNnJ,EAAA,CAAA8B,UAAA,IAAAsH,qDAAA,mBAKM;IAENpJ,EAAA,CAAAC,cAAA,eAAwC;IAEpCD,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAAwC;IACGD,EAAA,CAAAqB,MAAA,GAEvC;IAAArB,EAAA,CAAAW,YAAA,EAAO;IACTX,EAAA,CAAA8B,UAAA,IAAAuH,sDAAA,oBAIC;IACHrJ,EAAA,CAAAW,YAAA,EAAM;IAGRX,EAAA,CAAAC,cAAA,gBAA2C;IAGvCD,EAAA,CAAAE,UAAA,mBAAAoJ,yEAAA;MAAA,MAAApD,WAAA,GAAAlG,EAAA,CAAAK,aAAA,CAAAkJ,IAAA;MAAA,MAAAb,cAAA,GAAAxC,WAAA,CAAAnB,SAAA;MAAA,MAAAyE,OAAA,GAAAxJ,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA+I,OAAA,CAAAZ,cAAA,CAAAF,cAAA,CAAAG,GAAA,CAA8B;IAAA,EAAC;IAGxC7I,EAAA,CAAAY,SAAA,cAAwC;IAC1CZ,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,mBAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAAuJ,yEAAA;MAAA,MAAAvD,WAAA,GAAAlG,EAAA,CAAAK,aAAA,CAAAkJ,IAAA;MAAA,MAAAb,cAAA,GAAAxC,WAAA,CAAAnB,SAAA;MAAA,MAAA2E,OAAA,GAAA1J,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAiJ,OAAA,CAAAC,kBAAA,CAAAjB,cAAA,CAA8B;IAAA,EAAC;IAGxC1I,EAAA,CAAAY,SAAA,cAA+B;IACjCZ,EAAA,CAAAW,YAAA,EAAS;;;;;IAhDRX,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAAc,UAAA,SAAA8I,OAAA,CAAAC,OAAA,CAAAnB,cAAA,CAAAd,IAAA,EAA8B;IAY9B5H,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAc,UAAA,UAAA8I,OAAA,CAAAC,OAAA,CAAAnB,cAAA,CAAAd,IAAA,EAA+B;IAQ9B5H,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4D,kBAAA,MAAA8E,cAAA,CAAAoB,IAAA,6BACF;IAE2C9J,EAAA,CAAAa,SAAA,GAEvC;IAFuCb,EAAA,CAAAkE,iBAAA,CAAA0F,OAAA,CAAAG,gBAAA,CAAArB,cAAA,CAAAd,IAAA,EAEvC;IAEC5H,EAAA,CAAAa,SAAA,GAAqB;IAArBb,EAAA,CAAAc,UAAA,SAAA4H,cAAA,CAAA5E,IAAA,CAAqB;;;;;IArChC9D,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAA8B,UAAA,IAAAkI,+CAAA,oBAwDM;IACRhK,EAAA,CAAAW,YAAA,EAAM;;;;IAxDqBX,EAAA,CAAAa,SAAA,GAAqB;IAArBb,EAAA,CAAAc,UAAA,YAAAmJ,MAAA,CAAAC,kBAAA,CAAqB;;;;;IAqH9ClK,EAAA,CAAAC,cAAA,eAGC;IACSD,EAAA,CAAAqB,MAAA,yBAAkB;IAAArB,EAAA,CAAAW,YAAA,EAAS;IACnCX,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4D,kBAAA,MAAAuG,OAAA,CAAAC,mBAAA,CAAA3F,OAAA,kBAAA0F,OAAA,CAAAC,mBAAA,CAAA3F,OAAA,CAAAC,OAAA,MACF;;;;;IA0BE1E,EAAA,CAAAC,cAAA,eAGC;IACuCD,EAAA,CAAAqB,MAAA,cAAO;IAAArB,EAAA,CAAAW,YAAA,EAAO;IACpDX,EAAA,CAAAC,cAAA,gBAAsC;IAAAD,EAAA,CAAAqB,MAAA,GAEpC;;IAAArB,EAAA,CAAAW,YAAA,EAAO;;;;IAF6BX,EAAA,CAAAa,SAAA,GAEpC;IAFoCb,EAAA,CAAAkE,iBAAA,CAAAlE,EAAA,CAAA0H,WAAA,OAAA2C,OAAA,CAAAD,mBAAA,CAAAE,MAAA,YAEpC;;;;;IAEJtK,EAAA,CAAAC,cAAA,eAOC;IAEGD,EAAA,CAAAY,SAAA,aAAuC;IACvCZ,EAAA,CAAAqB,MAAA,eACF;IAAArB,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAqB,MAAA,uEACF;IAAArB,EAAA,CAAAW,YAAA,EAAO;;;;;;IAoBPX,EAAA,CAAAC,cAAA,eAGC;IAIGD,EAAA,CAAAE,UAAA,mBAAAqK,kFAAA;MAAAvK,EAAA,CAAAK,aAAA,CAAAmK,IAAA;MAAA,MAAAC,cAAA,GAAAzK,EAAA,CAAAQ,aAAA,GAAAuE,SAAA;MAAA,MAAA2F,OAAA,GAAA1K,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAiK,OAAA,CAAA9B,cAAA,CAAA6B,cAAA,CAAA5B,GAAA,CAA8B;IAAA,EAAC;IAH1C7I,EAAA,CAAAW,YAAA,EAIE;;;;IAHAX,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAAc,UAAA,QAAA2J,cAAA,CAAA5B,GAAA,EAAA7I,EAAA,CAAAwH,aAAA,CAAsB;;;;;IAO1BxH,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAY,SAAA,QAA8C;IAChDZ,EAAA,CAAAW,YAAA,EAAM;;;;;IADDX,EAAA,CAAAa,SAAA,GAAsC;IAAtCb,EAAA,CAAA8I,UAAA,CAAA6B,OAAA,CAAA3B,WAAA,CAAAyB,cAAA,CAAA7C,IAAA,EAAsC;;;;;IAWvC5H,EAAA,CAAAC,cAAA,gBAGG;IAAAD,EAAA,CAAAqB,MAAA,GAAqC;IAAArB,EAAA,CAAAW,YAAA,EACvC;;;;;IADEX,EAAA,CAAAa,SAAA,GAAqC;IAArCb,EAAA,CAAAkE,iBAAA,CAAA0G,OAAA,CAAA1B,cAAA,CAAAuB,cAAA,CAAA3G,IAAA,EAAqC;;;;;;IAnC9C9D,EAAA,CAAAC,cAAA,eAGC;IAECD,EAAA,CAAA8B,UAAA,IAAA+I,4DAAA,mBASM;IAGN7K,EAAA,CAAA8B,UAAA,IAAAgJ,4DAAA,mBAKM;IAEN9K,EAAA,CAAAC,cAAA,eAA0C;IAEtCD,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAA0C;IACGD,EAAA,CAAAqB,MAAA,GAEzC;IAAArB,EAAA,CAAAW,YAAA,EAAO;IACTX,EAAA,CAAA8B,UAAA,IAAAiJ,6DAAA,oBAIC;IACH/K,EAAA,CAAAW,YAAA,EAAM;IAGRX,EAAA,CAAAC,cAAA,gBAA6C;IAGzCD,EAAA,CAAAE,UAAA,mBAAA8K,gFAAA;MAAA,MAAA9E,WAAA,GAAAlG,EAAA,CAAAK,aAAA,CAAA4K,IAAA;MAAA,MAAAR,cAAA,GAAAvE,WAAA,CAAAnB,SAAA;MAAA,MAAAmG,OAAA,GAAAlL,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAyK,OAAA,CAAAtC,cAAA,CAAA6B,cAAA,CAAA5B,GAAA,CAA8B;IAAA,EAAC;IAGxC7I,EAAA,CAAAY,SAAA,cAAwC;IAC1CZ,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,mBAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAAiL,gFAAA;MAAA,MAAAjF,WAAA,GAAAlG,EAAA,CAAAK,aAAA,CAAA4K,IAAA;MAAA,MAAAR,cAAA,GAAAvE,WAAA,CAAAnB,SAAA;MAAA,MAAAqG,OAAA,GAAApL,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA2K,OAAA,CAAAzB,kBAAA,CAAAc,cAAA,CAA8B;IAAA,EAAC;IAGxCzK,EAAA,CAAAY,SAAA,cAA+B;IACjCZ,EAAA,CAAAW,YAAA,EAAS;;;;;IAhDRX,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAAc,UAAA,SAAAuK,OAAA,CAAAxB,OAAA,CAAAY,cAAA,CAAA7C,IAAA,EAA8B;IAY9B5H,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAc,UAAA,UAAAuK,OAAA,CAAAxB,OAAA,CAAAY,cAAA,CAAA7C,IAAA,EAA+B;IAQ9B5H,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4D,kBAAA,MAAA6G,cAAA,CAAAX,IAAA,6BACF;IAE6C9J,EAAA,CAAAa,SAAA,GAEzC;IAFyCb,EAAA,CAAAkE,iBAAA,CAAAmH,OAAA,CAAAtB,gBAAA,CAAAU,cAAA,CAAA7C,IAAA,EAEzC;IAEC5H,EAAA,CAAAa,SAAA,GAAqB;IAArBb,EAAA,CAAAc,UAAA,SAAA2J,cAAA,CAAA3G,IAAA,CAAqB;;;;;IA1ClC9D,EAAA,CAAAC,cAAA,eAGC;IAEGD,EAAA,CAAAY,SAAA,YAAqC;IACrCZ,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,eAA2C;IACzCD,EAAA,CAAA8B,UAAA,IAAAwJ,sDAAA,oBAwDM;IACRtL,EAAA,CAAAW,YAAA,EAAM;;;;IA5DJX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4D,kBAAA,2BAAA2H,OAAA,CAAArB,kBAAA,CAAAtF,MAAA,OACF;IAG2B5E,EAAA,CAAAa,SAAA,GAAqB;IAArBb,EAAA,CAAAc,UAAA,YAAAyK,OAAA,CAAArB,kBAAA,CAAqB;;;;;IA0E9ClK,EAAA,CAAAY,SAAA,aAAqD;;;;;IACrDZ,EAAA,CAAAY,SAAA,aAA2D;;;;;;IAd7DZ,EAAA,CAAAC,cAAA,kBAYC;IANCD,EAAA,CAAAE,UAAA,mBAAAsL,4EAAA;MAAAxL,EAAA,CAAAK,aAAA,CAAAoL,IAAA;MAAA,MAAAC,OAAA,GAAA1L,EAAA,CAAAQ,aAAA;MACekL,OAAA,CAAAnG,gBAAA,CAAAmG,OAAA,CAAAtB,mBAAA,CACd;MAAA,OAAcpK,EAAA,CAAAS,WAAA,CAAAiL,OAAA,CAAAC,6BAAA,EAEzB;IAAA,EADW;IAID3L,EAAA,CAAA8B,UAAA,IAAA8J,uDAAA,iBAAqD;IACrD5L,EAAA,CAAA8B,UAAA,IAAA+J,uDAAA,iBAA2D;IAC3D7L,EAAA,CAAAqB,MAAA,kCACF;IAAArB,EAAA,CAAAW,YAAA,EAAS;;;;IALPX,EAAA,CAAAc,UAAA,aAAAgL,OAAA,CAAAnG,OAAA,CAAoB;IAEa3F,EAAA,CAAAa,SAAA,GAAc;IAAdb,EAAA,CAAAc,UAAA,UAAAgL,OAAA,CAAAnG,OAAA,CAAc;IACP3F,EAAA,CAAAa,SAAA,GAAa;IAAbb,EAAA,CAAAc,UAAA,SAAAgL,OAAA,CAAAnG,OAAA,CAAa;;;;;;IAIvD3F,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAA6L,4EAAA;MAAA/L,EAAA,CAAAK,aAAA,CAAA2L,IAAA;MAAA,MAAAC,OAAA,GAAAjM,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAwL,OAAA,CAAAlG,UAAA,CAAAkG,OAAA,CAAA7B,mBAAA,CAAAlF,EAAA,CAAkC;IAAA,EAAC;IAG5ClF,EAAA,CAAAY,SAAA,aAAiC;IACjCZ,EAAA,CAAAqB,MAAA,yBACF;IAAArB,EAAA,CAAAW,YAAA,EAAS;;;;;;IArMbX,EAAA,CAAAC,cAAA,cAA+D;IAIzDD,EAAA,CAAAY,SAAA,aAAgC;IAChCZ,EAAA,CAAAqB,MAAA,wBACF;IAAArB,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,eAAsC;IACpCD,EAAA,CAAAY,SAAA,eAQE;IACFZ,EAAA,CAAAC,cAAA,eAAyC;IAErCD,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAC,cAAA,iBAAqC;IACnCD,EAAA,CAAAqB,MAAA,IACF;;IAAArB,EAAA,CAAAW,YAAA,EAAO;IAMbX,EAAA,CAAAC,cAAA,gBAAyC;IAErCD,EAAA,CAAAY,SAAA,cAAmC;IACnCZ,EAAA,CAAAqB,MAAA,iBACF;IAAArB,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,gBAAyC;IACvCD,EAAA,CAAAqB,MAAA,IACF;IAAArB,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAA8B,UAAA,KAAAoK,gDAAA,mBAMM;IACRlM,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAC,cAAA,gBAAyC;IAErCD,EAAA,CAAAY,SAAA,cAA+B;IAC/BZ,EAAA,CAAAqB,MAAA,sBACF;IAAArB,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,gBAAoC;IAEMD,EAAA,CAAAqB,MAAA,cAAM;IAAArB,EAAA,CAAAW,YAAA,EAAO;IACnDX,EAAA,CAAAC,cAAA,iBAAsC;IAAAD,EAAA,CAAAqB,MAAA,IAEpC;IAAArB,EAAA,CAAAW,YAAA,EAAO;IAEXX,EAAA,CAAAC,cAAA,gBAAoC;IACID,EAAA,CAAAqB,MAAA,gBAAQ;IAAArB,EAAA,CAAAW,YAAA,EAAO;IACrDX,EAAA,CAAAC,cAAA,iBAIC;IACCD,EAAA,CAAAqB,MAAA,IACF;IAAArB,EAAA,CAAAW,YAAA,EAAO;IAETX,EAAA,CAAA8B,UAAA,KAAAqK,gDAAA,mBAQM;IACNnM,EAAA,CAAA8B,UAAA,KAAAsK,gDAAA,mBAkBM;IACRpM,EAAA,CAAAW,YAAA,EAAM;IAIRX,EAAA,CAAA8B,UAAA,KAAAuK,gDAAA,mBAmEM;IAGNrM,EAAA,CAAAC,cAAA,gBAAyC;IACvCD,EAAA,CAAA8B,UAAA,KAAAwK,mDAAA,sBAgBS;IAETtM,EAAA,CAAA8B,UAAA,KAAAyK,mDAAA,sBAOS;IAETvM,EAAA,CAAAC,cAAA,kBAMC;IALCD,EAAA,CAAAE,UAAA,mBAAAsM,mEAAA;MAAAxM,EAAA,CAAAK,aAAA,CAAAoM,KAAA;MAAA,MAAAC,QAAA,GAAA1M,EAAA,CAAAQ,aAAA;MACekM,QAAA,CAAAzF,kBAAA,CAAAyF,QAAA,CAAAtC,mBAAA,CAAAlF,EAAA,CACd;MAAA,OAAclF,EAAA,CAAAS,WAAA,CAAAiM,QAAA,CAAAf,6BAAA,EAEzB;IAAA,EADW;IAGD3L,EAAA,CAAAY,SAAA,cAAqC;IACrCZ,EAAA,CAAAqB,MAAA,mBACF;IAAArB,EAAA,CAAAW,YAAA,EAAS;;;;IAvMLX,EAAA,CAAAa,SAAA,GAGC;IAHDb,EAAA,CAAAc,UAAA,SAAA6L,MAAA,CAAAvC,mBAAA,CAAA9C,QAAA,kBAAAqF,MAAA,CAAAvC,mBAAA,CAAA9C,QAAA,CAAAC,KAAA,yCAAAvH,EAAA,CAAAwH,aAAA,CAGC;IAOCxH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4D,kBAAA,OAAA+I,MAAA,CAAAvC,mBAAA,CAAA9C,QAAA,kBAAAqF,MAAA,CAAAvC,mBAAA,CAAA9C,QAAA,CAAAG,QAAA,yBACF;IAEEzH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4D,kBAAA,MAAA5D,EAAA,CAAA0H,WAAA,SAAAiF,MAAA,CAAAvC,mBAAA,CAAAzC,SAAA,iBACF;IAYF3H,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4D,kBAAA,MAAA+I,MAAA,CAAAvC,mBAAA,CAAA1F,OAAA,MACF;IAEG1E,EAAA,CAAAa,SAAA,GAA0C;IAA1Cb,EAAA,CAAAc,UAAA,SAAA6L,MAAA,CAAAvC,mBAAA,CAAA3F,OAAA,kBAAAkI,MAAA,CAAAvC,mBAAA,CAAA3F,OAAA,CAAAC,OAAA,CAA0C;IAiBH1E,EAAA,CAAAa,SAAA,IAEpC;IAFoCb,EAAA,CAAAkE,iBAAA,CAAAyI,MAAA,CAAAvC,mBAAA,CAAAxC,IAAA,CAEpC;IAMA5H,EAAA,CAAAa,SAAA,GAAmD;IAAnDb,EAAA,CAAA2C,WAAA,mBAAAgK,MAAA,CAAAvC,mBAAA,CAAAjD,MAAA,CAAmD,qBAAAwF,MAAA,CAAAvC,mBAAA,CAAAjD,MAAA;IAGnDnH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4D,kBAAA,MAAA+I,MAAA,CAAAvC,mBAAA,CAAAjD,MAAA,wBACF;IAICnH,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAc,UAAA,SAAA6L,MAAA,CAAAvC,mBAAA,CAAAE,MAAA,CAAgC;IAShCtK,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAAc,UAAA,UAAA6L,MAAA,CAAAvC,mBAAA,CAAAjD,MAAA,CAAiC;IAuBrCnH,EAAA,CAAAa,SAAA,GAAmC;IAAnCb,EAAA,CAAAc,UAAA,SAAA6L,MAAA,CAAAzC,kBAAA,CAAAtF,MAAA,KAAmC;IAsEjC5E,EAAA,CAAAa,SAAA,GAKX;IALWb,EAAA,CAAAc,UAAA,SAAA6L,MAAA,CAAAvC,mBAAA,CAAAxC,IAAA,sBAAA+E,MAAA,CAAAvC,mBAAA,CAAAxC,IAAA,uBAAA+E,MAAA,CAAAvC,mBAAA,CAAAxC,IAAA,wBAKX;IAaW5H,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAAc,UAAA,UAAA6L,MAAA,CAAAvC,mBAAA,CAAAjD,MAAA,CAAiC;;;ADplB5C,OAAM,MAAOyF,yBAAyB;EAgCpCC,YACUC,cAA8B,EAC9BC,YAA0B,EAC1BC,MAAc;IAFd,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IA3BhB,KAAArH,OAAO,GAAG,IAAI;IACd,KAAA4C,WAAW,GAAG,KAAK;IACnB,KAAA0E,oBAAoB,GAAG,IAAI;IAC3B,KAAAC,KAAK,GAAiB,IAAI;IAC1B,KAAAtK,cAAc,GAAG,KAAK;IACtB,KAAAC,YAAY,GAAG,KAAK;IAEpB;IACA,KAAAY,qBAAqB,GAAgB,IAAI0J,GAAG,EAAU;IACtD,KAAAnM,WAAW,GAAG,KAAK;IACnB,KAAA2C,gBAAgB,GAAG,KAAK;IAExB;IACA,KAAAyJ,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAnD,kBAAkB,GAAiB,EAAE;IAErC;IACA,KAAAoD,4BAA4B,GAAG,KAAK;IACpC,KAAAlD,mBAAmB,GAAwB,IAAI;IAEvC,KAAAmD,QAAQ,GAAG,IAAIlO,OAAO,EAAQ;IAC9B,KAAAmO,eAAe,GAAG,IAAIjO,eAAe,CAAS,CAAC,CAAC;IAOtD,IAAI,CAACkO,cAAc,GAAG,IAAI,CAACX,cAAc,CAACW,cAAc;IACxD,IAAI,CAACnF,sBAAsB,GAAG,IAAI,CAACmF,cAAc,CAAC,CAAC;IACnD,IAAI,CAAC1K,YAAY,GAAG,IAAI,CAAC+J,cAAc,CAACY,kBAAkB;IAC1D,IAAI,CAACC,WAAW,GAAG,IAAI,CAACZ,YAAY,CAACa,SAAS;IAE9C;IACA,IAAI,CAAC/K,YAAY,GAAG,IAAI,CAACiK,cAAc,CAACe,OAAO,EAAE;EACnD;EAEA;;;;EAIAtI,gBAAgBA,CAACuI,YAA0B;IACzC;IACA,IAAI,CAAC/H,UAAU,CAAC+H,YAAY,CAAC5I,EAAE,CAAC;IAEhC;IACA,MAAM6I,cAAc,GAClBD,YAAY,CAACC,cAAc,IAC1BD,YAAY,CAACE,QAAQ,IAAIF,YAAY,CAACE,QAAQ,CAAC,gBAAgB,CAAE,KACjEF,YAAY,CAACG,aAAa,IAC3BH,YAAY,CAACG,aAAa,CAACC,QAAQ,CAAC,cAAc,CAAC,GAC/CJ,YAAY,CAACG,aAAa,GAC1B,IAAI,CAAC;IAEX,MAAME,OAAO,GACXL,YAAY,CAACK,OAAO,IACnBL,YAAY,CAACE,QAAQ,IAAIF,YAAY,CAACE,QAAQ,CAAC,SAAS,CAAE,KAC1DF,YAAY,CAACG,aAAa,IAC3BH,YAAY,CAACG,aAAa,CAACC,QAAQ,CAAC,OAAO,CAAC,GACxCJ,YAAY,CAACG,aAAa,GAC1B,IAAI,CAAC;IAEX;IACA,IAAIF,cAAc,EAAE;MAClB,IAAI,CAACf,MAAM,CAACoB,QAAQ,CAAC,CAAC,8BAA8B,EAAEL,cAAc,CAAC,CAAC;KACvE,MAAM,IAAII,OAAO,EAAE;MAClB,IAAI,CAACnB,MAAM,CAACoB,QAAQ,CAAC,CAAC,iBAAiB,EAAED,OAAO,CAAC,CAAC;KACnD,MAAM,IAAIL,YAAY,CAACxG,QAAQ,IAAIwG,YAAY,CAACxG,QAAQ,CAACpC,EAAE,EAAE;MAC5D,IAAI,CAACS,OAAO,GAAG,IAAI;MAEnB,IAAI,CAACmH,cAAc,CAChBuB,uBAAuB,CAACP,YAAY,CAACxG,QAAQ,CAACpC,EAAE,CAAC,CACjDoJ,SAAS,CAAC;QACTC,IAAI,EAAGC,YAAY,IAAI;UACrB,IAAI,CAAC7I,OAAO,GAAG,KAAK;UACpB,IAAI6I,YAAY,IAAIA,YAAY,CAACtJ,EAAE,EAAE;YACnC,IAAI,CAAC8H,MAAM,CAACoB,QAAQ,CAAC,CACnB,8BAA8B,EAC9BI,YAAY,CAACtJ,EAAE,CAChB,CAAC;WACH,MAAM;YACL,IAAI,CAAC8H,MAAM,CAACoB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;;QAEvC,CAAC;QACDlB,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACvH,OAAO,GAAG,KAAK;UACpB,IAAI,CAACuH,KAAK,GAAGA,KAAK;UAClB,IAAI,CAACF,MAAM,CAACoB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;QACrC;OACD,CAAC;KACL,MAAM;MACL,IAAI,CAACpB,MAAM,CAACoB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;;EAEvC;EAGAlG,QAAQA,CAACuG,MAAmB;IAC1B,IAAI,CAACA,MAAM,EAAE;IAEb,MAAMC,cAAc,GAAGD,MAAM,CAACE,SAAS;IACvC,MAAMC,YAAY,GAAGH,MAAM,CAACG,YAAY;IACxC,MAAMC,YAAY,GAAGJ,MAAM,CAACI,YAAY;IAExC;IACA,IAAID,YAAY,GAAGF,cAAc,GAAGG,YAAY,GAAG,GAAG,EAAE;MACtD,IAAI,CAACrB,eAAe,CAACe,IAAI,CAACG,cAAc,CAAC;;EAE7C;EACAI,QAAQA,CAAA;IACN;IACA,MAAMC,mBAAmB,GAAGC,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;IAC1E,IAAIF,mBAAmB,KAAK,IAAI,EAAE;MAChC,IAAI,CAAClM,YAAY,GAAGkM,mBAAmB,KAAK,MAAM;MAClD,IAAI,CAACjC,cAAc,CAACoC,QAAQ,CAAC,IAAI,CAACrM,YAAY,CAAC;;IAGjD,IAAI,CAAChB,iBAAiB,EAAE;IACxB,IAAI,CAACsN,kBAAkB,EAAE;IACzB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,0BAA0B,EAAE;EACnC;EAEA;;;EAGQA,0BAA0BA,CAAA;IAChC,MAAMC,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAE/D,IAAID,sBAAsB,CAACxL,IAAI,GAAG,CAAC,EAAE;MACnC,IAAI,CAAC2J,cAAc,CAAC+B,IAAI,CAAC5P,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0O,SAAS,CAAEmB,aAAa,IAAI;QAC5D,MAAMC,qBAAqB,GAAGD,aAAa,CAAC1P,MAAM,CAC/C+N,YAAY,IAAK,CAACwB,sBAAsB,CAACK,GAAG,CAAC7B,YAAY,CAAC5I,EAAE,CAAC,CAC/D;QAEA,IAAI,CAAC4H,cAAsB,CAAC2C,aAAa,CAAClB,IAAI,CAACmB,qBAAqB,CAAC;QACtE,MAAME,WAAW,GAAGF,qBAAqB,CAAC3P,MAAM,CAC7C8P,CAAC,IAAK,CAACA,CAAC,CAAC1I,MAAM,CACjB,CAACvC,MAAM;QACP,IAAI,CAACkI,cAAsB,CAACgD,iBAAiB,CAACvB,IAAI,CAACqB,WAAW,CAAC;QAChE,IAAI,CAACG,uBAAuB,CAACL,qBAAqB,CAAC;MACrD,CAAC,CAAC;;EAEN;EAEAN,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAAC5B,eAAe,CACjBgC,IAAI,CACH7P,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,EACxB1N,YAAY,CAAC,GAAG,CAAC;IAAE;IACnBC,oBAAoB,EAAE;IAAE;IACxBC,MAAM,CAAC,MAAM,CAAC,IAAI,CAACwI,WAAW,IAAI,IAAI,CAAC0E,oBAAoB,CAAC,CAAC;KAC9D,CACAqB,SAAS,CAAC,MAAK;MACd,IAAI,CAAC0B,qBAAqB,EAAE;IAC9B,CAAC,CAAC;EACN;EACAnO,iBAAiBA,CAAA;IACf,IAAI,CAAC8D,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC4C,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC2E,KAAK,GAAG,IAAI;IACjB,IAAI,CAACD,oBAAoB,GAAG,IAAI;IAEhC,MAAMqC,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAE/D,IAAI,CAACzC,cAAc,CAChBmD,gBAAgB,CAAC,IAAI,CAAC,CACtBT,IAAI,CACH7P,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,EACxB7N,GAAG,CAAE+P,aAAa,IAAI;MACpB,IAAIH,sBAAsB,CAACxL,IAAI,GAAG,CAAC,EAAE;QACnC,OAAO2L,aAAa,CAAC1P,MAAM,CACxB+N,YAAY,IAAK,CAACwB,sBAAsB,CAACK,GAAG,CAAC7B,YAAY,CAAC5I,EAAE,CAAC,CAC/D;;MAEH,OAAOuK,aAAa;IACtB,CAAC,CAAC,CACH,CACAnB,SAAS,CAAC;MACTC,IAAI,EAAGkB,aAAa,IAAI;QACrB,IAAI,CAAC3C,cAAsB,CAAC2C,aAAa,CAAClB,IAAI,CAACkB,aAAa,CAAC;QAC9D,MAAMG,WAAW,GAAGH,aAAa,CAAC1P,MAAM,CAAE8P,CAAC,IAAK,CAACA,CAAC,CAAC1I,MAAM,CAAC,CAACvC,MAAM;QAChE,IAAI,CAACkI,cAAsB,CAACgD,iBAAiB,CAACvB,IAAI,CAACqB,WAAW,CAAC;QAChE,IAAI,CAACjK,OAAO,GAAG,KAAK;QACpB,IAAI,CAACsH,oBAAoB,GACvB,IAAI,CAACH,cAAc,CAACG,oBAAoB,EAAE;MAC9C,CAAC;MACDC,KAAK,EAAGgD,GAAU,IAAI;QACpB,IAAI,CAAChD,KAAK,GAAGgD,GAAG;QAChB,IAAI,CAACvK,OAAO,GAAG,KAAK;QACpB,IAAI,CAACsH,oBAAoB,GAAG,KAAK;MACnC;KACD,CAAC;EACN;EAEA+C,qBAAqBA,CAAA;IACnB,IAAI,IAAI,CAACzH,WAAW,IAAI,CAAC,IAAI,CAAC0E,oBAAoB,EAAE;IAEpD,IAAI,CAAC1E,WAAW,GAAG,IAAI;IACvB,MAAM+G,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAE/D,IAAI,CAACzC,cAAc,CAChBkD,qBAAqB,EAAE,CACvBR,IAAI,CACH7P,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,EACxB7N,GAAG,CAAE+P,aAAa,IAAI;MACpB,IAAIH,sBAAsB,CAACxL,IAAI,GAAG,CAAC,EAAE;QACnC,OAAO2L,aAAa,CAAC1P,MAAM,CACxB+N,YAAY,IAAK,CAACwB,sBAAsB,CAACK,GAAG,CAAC7B,YAAY,CAAC5I,EAAE,CAAC,CAC/D;;MAEH,OAAOuK,aAAa;IACtB,CAAC,CAAC,CACH,CACAnB,SAAS,CAAC;MACTC,IAAI,EAAGkB,aAAa,IAAI;QACtB,IAAI,CAAChC,cAAc,CAChB+B,IAAI,CAAC5P,IAAI,CAAC,CAAC,CAAC,CAAC,CACb0O,SAAS,CAAE6B,qBAAqB,IAAI;UACnC,MAAMC,gBAAgB,GAAG,CACvB,GAAGD,qBAAqB,EACxB,GAAGV,aAAa,CACjB;UACA,IAAI,CAAC3C,cAAsB,CAAC2C,aAAa,CAAClB,IAAI,CAAC6B,gBAAgB,CAAC;UACjE,MAAMR,WAAW,GAAGQ,gBAAgB,CAACrQ,MAAM,CACxC8P,CAAC,IAAK,CAACA,CAAC,CAAC1I,MAAM,CACjB,CAACvC,MAAM;UACP,IAAI,CAACkI,cAAsB,CAACgD,iBAAiB,CAACvB,IAAI,CAACqB,WAAW,CAAC;UAChE,IAAI,CAACG,uBAAuB,CAACK,gBAAgB,CAAC;QAChD,CAAC,CAAC;QAEJ,IAAI,CAAC7H,WAAW,GAAG,KAAK;QACxB,IAAI,CAAC0E,oBAAoB,GACvB,IAAI,CAACH,cAAc,CAACG,oBAAoB,EAAE;MAC9C,CAAC;MACDC,KAAK,EAAGgD,GAAU,IAAI;QACpB,IAAI,CAAC3H,WAAW,GAAG,KAAK;QACxB,IAAI,CAAC0E,oBAAoB,GAAG,KAAK;MACnC;KACD,CAAC;EACN;EACAkC,kBAAkBA,CAAA;IAChB,IAAI,CAACrC,cAAc,CAChBuD,2BAA2B,EAAE,CAC7Bb,IAAI,CACH7P,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,EACxB9N,UAAU,CAAEyN,KAAK,IAAI;MACnBoD,OAAO,CAACpD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAO5N,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH,CACAgP,SAAS,EAAE;IACd,IAAI,CAACxB,cAAc,CAChByD,4BAA4B,EAAE,CAC9Bf,IAAI,CACH7P,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,EACxB9N,UAAU,CAAEyN,KAAK,IAAI;MACnBoD,OAAO,CAACpD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,OAAO5N,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH,CACAgP,SAAS,EAAE;EAChB;EACAvI,UAAUA,CAACyK,cAAsB;IAC/B,IAAI,CAACA,cAAc,EAAE;MACnB,IAAI,CAACtD,KAAK,GAAG,IAAIuD,KAAK,CAAC,6BAA6B,CAAC;MACrD;;IAGF,IAAI,CAAChD,cAAc,CAAC+B,IAAI,CAAC5P,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0O,SAAS,CAAEmB,aAAa,IAAI;MAC5D,MAAM3B,YAAY,GAAG2B,aAAa,CAACiB,IAAI,CAAEb,CAAC,IAAKA,CAAC,CAAC3K,EAAE,KAAKsL,cAAc,CAAC;MACvE,IAAI1C,YAAY,EAAE;QAChB,IAAIA,YAAY,CAAC3G,MAAM,EAAE;QAEzB,MAAMwJ,oBAAoB,GAAGlB,aAAa,CAAC/P,GAAG,CAAEmQ,CAAC,IAC/CA,CAAC,CAAC3K,EAAE,KAAKsL,cAAc,GACnB;UAAE,GAAGX,CAAC;UAAE1I,MAAM,EAAE,IAAI;UAAEmD,MAAM,EAAE,IAAIsG,IAAI,EAAE,CAACC,WAAW;QAAE,CAAE,GACxDhB,CAAC,CACN;QAED,IAAI,CAACiB,yBAAyB,CAACH,oBAAoB,CAAC;QAEpD,IAAI,CAAC7D,cAAc,CAChB/G,UAAU,CAAC,CAACyK,cAAc,CAAC,CAAC,CAC5BhB,IAAI,CAAC7P,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAC;UACTC,IAAI,EAAGwC,MAAM,IAAI;YACf,IAAIA,MAAM,IAAIA,MAAM,CAACC,OAAO,EAAE;cAC5B,IAAI,IAAI,CAAC9D,KAAK,IAAI,IAAI,CAACA,KAAK,CAACzI,OAAO,CAACyJ,QAAQ,CAAC,MAAM,CAAC,EAAE;gBACrD,IAAI,CAAChB,KAAK,GAAG,IAAI;;;UAGvB,CAAC;UACDA,KAAK,EAAGgD,GAAG,IAAI;YACb,MAAMe,qBAAqB,GAAGxB,aAAa,CAAC/P,GAAG,CAAEmQ,CAAC,IAChDA,CAAC,CAAC3K,EAAE,KAAKsL,cAAc,GACnB;cAAE,GAAGX,CAAC;cAAE1I,MAAM,EAAE,KAAK;cAAEmD,MAAM,EAAE4G;YAAS,CAAE,GAC1CrB,CAAC,CACN;YACA,IAAI,CAAC/C,cAAsB,CAAC2C,aAAa,CAAClB,IAAI,CAC7C0C,qBAAqB,CACtB;YAED,MAAME,mBAAmB,GAAGF,qBAAqB,CAAClR,MAAM,CACrD8P,CAAC,IAAK,CAACA,CAAC,CAAC1I,MAAM,CACjB,CAACvC,MAAM;YACP,IAAI,CAACkI,cAAsB,CAACgD,iBAAiB,CAACvB,IAAI,CACjD4C,mBAAmB,CACpB;UACH;SACD,CAAC;OACL,MAAM;QACL,IAAI,CAACtP,iBAAiB,EAAE;;IAE5B,CAAC,CAAC;EACJ;EAEA;;;;EAIQiP,yBAAyBA,CAACrB,aAAoB;IACpD;IACC,IAAI,CAAC3C,cAAsB,CAAC2C,aAAa,CAAClB,IAAI,CAACkB,aAAa,CAAC;IAE9D;IACA,MAAMG,WAAW,GAAGH,aAAa,CAAC1P,MAAM,CAAE8P,CAAC,IAAK,CAACA,CAAC,CAAC1I,MAAM,CAAC,CAACvC,MAAM;IAChE,IAAI,CAACkI,cAAsB,CAACgD,iBAAiB,CAACvB,IAAI,CAACqB,WAAW,CAAC;IAEhE;IACA,IAAI,CAACG,uBAAuB,CAACN,aAAa,CAAC;EAC7C;EAEA;;;;EAIQM,uBAAuBA,CAACN,aAAoB;IAClDA,aAAa,CAAC2B,OAAO,CAAEtD,YAAY,IAAI;MACpC,IAAI,CAAChB,cAAsB,CAACiD,uBAAuB,GAAGjC,YAAY,CAAC;IACtE,CAAC,CAAC;EACJ;EAEA;;;EAGQuD,cAAcA,CAAA;IACpB,IAAI,CAAC5N,qBAAqB,CAACC,KAAK,EAAE;IAClC,IAAI,CAAC1C,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC2C,gBAAgB,GAAG,KAAK;EAC/B;EAEAvC,aAAaA,CAAA;IACX,IAAI,CAACqM,cAAc,CAAC+B,IAAI,CAAC5P,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0O,SAAS,CAAEmB,aAAa,IAAI;MAC5D,MAAM6B,SAAS,GAAG7B,aAAa,CAAC1P,MAAM,CAAE8P,CAAC,IAAK,CAACA,CAAC,CAAC1I,MAAM,CAAC,CAACzH,GAAG,CAAEmQ,CAAC,IAAKA,CAAC,CAAC3K,EAAE,CAAC;MAEzE,IAAIoM,SAAS,CAAC1M,MAAM,KAAK,CAAC,EAAE;MAE5B,MAAM2M,QAAQ,GAAGD,SAAS,CAACvR,MAAM,CAC9BmF,EAAE,IAAKA,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,IAAIA,EAAE,CAACsM,IAAI,EAAE,KAAK,EAAE,CACzD;MAED,IAAID,QAAQ,CAAC3M,MAAM,KAAK0M,SAAS,CAAC1M,MAAM,EAAE;QACxC,IAAI,CAACsI,KAAK,GAAG,IAAIuD,KAAK,CAAC,0BAA0B,CAAC;QAClD;;MAGF,MAAME,oBAAoB,GAAGlB,aAAa,CAAC/P,GAAG,CAAEmQ,CAAC,IAC/C0B,QAAQ,CAACrD,QAAQ,CAAC2B,CAAC,CAAC3K,EAAE,CAAC,GACnB;QAAE,GAAG2K,CAAC;QAAE1I,MAAM,EAAE,IAAI;QAAEmD,MAAM,EAAE,IAAIsG,IAAI,EAAE,CAACC,WAAW;MAAE,CAAE,GACxDhB,CAAC,CACN;MAED,IAAI,CAACiB,yBAAyB,CAACH,oBAAoB,CAAC;MAEpD,IAAI,CAAC7D,cAAc,CAChB/G,UAAU,CAACwL,QAAQ,CAAC,CACpB/B,IAAI,CAAC7P,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAC;QACTC,IAAI,EAAGwC,MAAM,IAAI;UACf,IAAIA,MAAM,IAAIA,MAAM,CAACC,OAAO,EAAE;YAC5B,IAAI,IAAI,CAAC9D,KAAK,IAAI,IAAI,CAACA,KAAK,CAACzI,OAAO,CAACyJ,QAAQ,CAAC,MAAM,CAAC,EAAE;cACrD,IAAI,CAAChB,KAAK,GAAG,IAAI;;;QAGvB,CAAC;QACDA,KAAK,EAAGgD,GAAG,IAAI;UACb;QAAA;OAEH,CAAC;IACN,CAAC,CAAC;EACJ;EAEAxN,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC+K,cAAc,CAAC+B,IAAI,CAC7B9P,GAAG,CAAE+P,aAAa,IAAKA,aAAa,EAAE7K,MAAM,GAAG,CAAC,CAAC,CAClD;EACH;EACA6M,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAAC1O,YAAY,CAACyM,IAAI,CAAC9P,GAAG,CAAEgS,KAAK,IAAKA,KAAK,GAAG,CAAC,CAAC,CAAC;EAC1D;EAEA;;;EAGAxP,kBAAkBA,CAAA;IAChB,IAAI,CAACU,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAE1C,IAAI,IAAI,CAACA,cAAc,EAAE;MACvB,IAAI,CAAC0F,sBAAsB,GACzB,IAAI,CAACwE,cAAc,CAAC6E,sBAAsB,EAAE;KAC/C,MAAM;MACL,IAAI,CAACrJ,sBAAsB,GAAG,IAAI,CAACmF,cAAc;;EAErD;EAEA;;;EAGApL,WAAWA,CAAA;IACT,IAAI,CAACQ,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACtC,IAAI,CAACiK,cAAc,CAACoC,QAAQ,CAAC,IAAI,CAACrM,YAAY,CAAC;IAE/C,IAAI,CAAC,IAAI,CAACA,YAAY,EAAE;MACtB+O,UAAU,CAAC,MAAK;QACd,IAAI,CAAC9E,cAAc,CAAC+E,qBAAqB,EAAE;QAC3CD,UAAU,CAAC,MAAK;UACd,IAAI,CAAC9E,cAAc,CAAC+E,qBAAqB,EAAE;QAC7C,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,EAAE,GAAG,CAAC;;IAGT7C,YAAY,CAAC8C,OAAO,CAClB,wBAAwB,EACxB,IAAI,CAACjP,YAAY,CAACkP,QAAQ,EAAE,CAC7B;EACH;EAEA;;;;EAIA9M,0BAA0BA,CAACuL,cAAsB;IAC/C,IAAI,CAACA,cAAc,EAAE;IAErB,IAAI,CAACtG,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACmD,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACD,oBAAoB,GAAG,IAAI;IAEhC,IAAIU,YAAsC;IAE1C,IAAI,CAACL,cAAc,CAAC+B,IAAI,CAAC5P,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0O,SAAS,CAAEmB,aAAa,IAAI;MAC5D3B,YAAY,GAAG2B,aAAa,CAACiB,IAAI,CAC9Bb,CAAe,IAAKA,CAAC,CAAC3K,EAAE,KAAKsL,cAAc,CAC7C;IACH,CAAC,CAAC;IAEF,IACE1C,YAAY,IACZA,YAAY,CAACrJ,OAAO,IACpBqJ,YAAY,CAACrJ,OAAO,CAACE,WAAW,IAChCmJ,YAAY,CAACrJ,OAAO,CAACE,WAAW,CAACC,MAAM,GAAG,CAAC,EAC3C;MACA,IAAI,CAACyI,kBAAkB,GAAG,KAAK;MAC/B,IAAI,CAACnD,kBAAkB,GAAG4D,YAAY,CAACrJ,OAAO,CAACE,WAAW,CAACjF,GAAG,CAC3DsS,UAAkC,KAChC;QACC9M,EAAE,EAAE,EAAE;QACN2D,GAAG,EAAEmJ,UAAU,CAACnJ,GAAG,IAAI,EAAE;QACzBjB,IAAI,EAAE,IAAI,CAACqK,kCAAkC,CAACD,UAAU,CAACpK,IAAI,CAAC;QAC9DkC,IAAI,EAAEkI,UAAU,CAAClI,IAAI,IAAI,EAAE;QAC3BhG,IAAI,EAAEkO,UAAU,CAAClO,IAAI,IAAI,CAAC;QAC1BoO,QAAQ,EAAE;OACI,EACnB;MACD;;IAGF,IAAI,CAACpF,cAAc,CAChB7H,0BAA0B,CAACuL,cAAc,CAAC,CAC1ChB,IAAI,CAAC7P,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAC;MACTC,IAAI,EAAG5J,WAAW,IAAI;QACpB,IAAI,CAAC0I,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACnD,kBAAkB,GAAGvF,WAAW;MACvC,CAAC;MACDuI,KAAK,EAAGgD,GAAG,IAAI;QACb,IAAI,CAAC7C,kBAAkB,GAAG,KAAK;MACjC;KACD,CAAC;EACN;EAEA;;;EAGA8E,qBAAqBA,CAAA;IACnB,IAAI,CAAC/E,oBAAoB,GAAG,KAAK;EACnC;EAEA;;;;EAIAvG,uBAAuBA,CAACiH,YAA0B;IAChD,IAAI,CAAC1D,mBAAmB,GAAG0D,YAAY;IACvC,IAAI,CAACR,4BAA4B,GAAG,IAAI;IAExC,IAAIQ,YAAY,CAACrJ,OAAO,EAAEE,WAAW,EAAEC,MAAM,EAAE;MAC7C,IAAI,CAACwN,kCAAkC,CAACtE,YAAY,CAAC5I,EAAE,CAAC;;EAE5D;EAEA;;;EAGAyG,6BAA6BA,CAAA;IAC3B,IAAI,CAAC2B,4BAA4B,GAAG,KAAK;IACzC,IAAI,CAAClD,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACF,kBAAkB,GAAG,EAAE;EAC9B;EAEA;;;EAGQkI,kCAAkCA,CAAC5B,cAAsB;IAC/D,IAAI,CAACtG,kBAAkB,GAAG,EAAE;IAE5B,IAAI,IAAI,CAACE,mBAAmB,EAAE3F,OAAO,EAAEE,WAAW,EAAEC,MAAM,EAAE;MAC1D,IAAI,CAACsF,kBAAkB,GACrB,IAAI,CAACE,mBAAmB,CAAC3F,OAAO,CAACE,WAAW,CAACjF,GAAG,CAC7CsS,UAAkC,KAChC;QACC9M,EAAE,EAAE,EAAE;QACN2D,GAAG,EAAEmJ,UAAU,CAACnJ,GAAG,IAAI,EAAE;QACzBjB,IAAI,EAAE,IAAI,CAACqK,kCAAkC,CAACD,UAAU,CAACpK,IAAI,CAAC;QAC9DkC,IAAI,EAAEkI,UAAU,CAAClI,IAAI,IAAI,EAAE;QAC3BhG,IAAI,EAAEkO,UAAU,CAAClO,IAAI,IAAI,CAAC;QAC1BoO,QAAQ,EAAE;OACI,EACnB;;EAEP;EAEA;;;EAGQD,kCAAkCA,CACxCrK,IAAoB;IAEpB,QAAQA,IAAI;MACV,KAAK,OAAO;MACZ,KAAK,OAAO;QACV,OAAOpI,WAAW,CAAC6S,KAAK;MAC1B,KAAK,OAAO;MACZ,KAAK,OAAO;QACV,OAAO7S,WAAW,CAAC8S,KAAK;MAC1B,KAAK,OAAO;MACZ,KAAK,OAAO;QACV,OAAO9S,WAAW,CAAC+S,KAAK;MAC1B,KAAK,MAAM;MACX,KAAK,MAAM;QACT,OAAO/S,WAAW,CAACgT,IAAI;MACzB;QACE,OAAOhT,WAAW,CAACgT,IAAI;;EAE7B;EAEA;;;EAGA3I,OAAOA,CAACjC,IAAY;IAClB,OAAOA,IAAI,EAAE6K,UAAU,CAAC,QAAQ,CAAC,IAAI,KAAK;EAC5C;EAEA;;;;;EAKAzJ,WAAWA,CAACpB,IAAY;IACtB,IAAI,CAACA,IAAI,EAAE,OAAO,aAAa;IAE/B,IAAIA,IAAI,CAAC6K,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACzD,IAAI7K,IAAI,CAAC6K,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACzD,IAAI7K,IAAI,CAAC6K,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACzD,IAAI7K,IAAI,CAAC6K,UAAU,CAAC,OAAO,CAAC,EAAE,OAAO,iBAAiB;IACtD,IAAI7K,IAAI,CAACsG,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,iBAAiB;IAClD,IAAItG,IAAI,CAACsG,QAAQ,CAAC,MAAM,CAAC,IAAItG,IAAI,CAACsG,QAAQ,CAAC,UAAU,CAAC,EACpD,OAAO,kBAAkB;IAC3B,IAAItG,IAAI,CAACsG,QAAQ,CAAC,OAAO,CAAC,IAAItG,IAAI,CAACsG,QAAQ,CAAC,OAAO,CAAC,EAClD,OAAO,mBAAmB;IAC5B,IAAItG,IAAI,CAACsG,QAAQ,CAAC,YAAY,CAAC,IAAItG,IAAI,CAACsG,QAAQ,CAAC,cAAc,CAAC,EAC9D,OAAO,wBAAwB;IACjC,IAAItG,IAAI,CAACsG,QAAQ,CAAC,KAAK,CAAC,IAAItG,IAAI,CAACsG,QAAQ,CAAC,YAAY,CAAC,EACrD,OAAO,qBAAqB;IAE9B,OAAO,aAAa;EACtB;EAEA;;;;;EAKAnE,gBAAgBA,CAACnC,IAAY;IAC3B,IAAI,CAACA,IAAI,EAAE,OAAO,SAAS;IAE3B,IAAIA,IAAI,CAAC6K,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;IAC7C,IAAI7K,IAAI,CAAC6K,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;IAC7C,IAAI7K,IAAI,CAAC6K,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;IAC7C,IAAI7K,IAAI,CAAC6K,UAAU,CAAC,OAAO,CAAC,EAAE,OAAO,OAAO;IAC5C,IAAI7K,IAAI,CAACsG,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;IACtC,IAAItG,IAAI,CAACsG,QAAQ,CAAC,MAAM,CAAC,IAAItG,IAAI,CAACsG,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,UAAU;IACzE,IAAItG,IAAI,CAACsG,QAAQ,CAAC,OAAO,CAAC,IAAItG,IAAI,CAACsG,QAAQ,CAAC,OAAO,CAAC,EAClD,OAAO,mBAAmB;IAC5B,IAAItG,IAAI,CAACsG,QAAQ,CAAC,YAAY,CAAC,IAAItG,IAAI,CAACsG,QAAQ,CAAC,cAAc,CAAC,EAC9D,OAAO,cAAc;IACvB,IAAItG,IAAI,CAACsG,QAAQ,CAAC,KAAK,CAAC,IAAItG,IAAI,CAACsG,QAAQ,CAAC,YAAY,CAAC,EAAE,OAAO,SAAS;IAEzE,OAAO,SAAS;EAClB;EAEA;;;;;EAKAhF,cAAcA,CAACpF,IAAY;IACzB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IAEpB,MAAM4O,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3C,IAAIC,CAAC,GAAG,CAAC;IACT,IAAIC,aAAa,GAAG9O,IAAI;IAExB,OAAO8O,aAAa,IAAI,IAAI,IAAID,CAAC,GAAGD,KAAK,CAAC9N,MAAM,GAAG,CAAC,EAAE;MACpDgO,aAAa,IAAI,IAAI;MACrBD,CAAC,EAAE;;IAGL,OAAO,GAAGC,aAAa,CAACC,OAAO,CAAC,CAAC,CAAC,IAAIH,KAAK,CAACC,CAAC,CAAC,EAAE;EAClD;EAEA;;;;EAIA/J,cAAcA,CAACC,GAAW;IACxB,IAAI,CAACA,GAAG,EAAE;IACViK,MAAM,CAACC,IAAI,CAAClK,GAAG,EAAE,QAAQ,CAAC;EAC5B;EAEA;;;;EAIAc,kBAAkBA,CAACqI,UAAsB;IACvC,IAAI,CAACA,UAAU,EAAEnJ,GAAG,EAAE;IAEtB,MAAMmK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGnB,UAAU,CAACnJ,GAAG;IAC1BmK,IAAI,CAACI,QAAQ,GAAGpB,UAAU,CAAClI,IAAI,IAAI,YAAY;IAC/CkJ,IAAI,CAACvE,MAAM,GAAG,QAAQ;IACtBwE,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;IAC/BA,IAAI,CAACO,KAAK,EAAE;IACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;EACjC;EAEAS,mBAAmBA,CAAC3F,YAA0B;IAC5C,IAAI,CAAC/H,UAAU,CAAC+H,YAAY,CAAC5I,EAAE,CAAC;EAClC;EAEA;;;;EAIA+B,kBAAkBA,CAACuJ,cAAsB;IACvC,IAAI,CAACA,cAAc,EAAE;MACnB,IAAI,CAACtD,KAAK,GAAG,IAAIuD,KAAK,CAAC,6BAA6B,CAAC;MACrD;;IAGF,MAAMnB,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAC/DD,sBAAsB,CAACoE,GAAG,CAAClD,cAAc,CAAC;IAC1C,IAAI,CAACmD,0BAA0B,CAACrE,sBAAsB,CAAC;IAEvD,IAAI,CAACxC,cAAc,CAChB7F,kBAAkB,CAACuJ,cAAc,CAAC,CAClChB,IAAI,CAAC7P,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAC;MACTC,IAAI,EAAGwC,MAAM,IAAI;QACf,IAAIA,MAAM,IAAIA,MAAM,CAACC,OAAO,EAAE;UAC5B,IAAI,IAAI,CAAC9D,KAAK,IAAI,IAAI,CAACA,KAAK,CAACzI,OAAO,CAACyJ,QAAQ,CAAC,aAAa,CAAC,EAAE;YAC5D,IAAI,CAAChB,KAAK,GAAG,IAAI;;;MAGvB,CAAC;MACDA,KAAK,EAAGgD,GAAG,IAAI;QACb,IAAI,CAAChD,KAAK,GAAGgD,GAAG;MAClB;KACD,CAAC;EACN;EAEA;;;EAGAzO,sBAAsBA,CAAA;IACpB6O,OAAO,CAACsD,GAAG,CAAC,yCAAyC,CAAC;IAEtD;IACA,IAAI,CAACnG,cAAc,CAAC+B,IAAI,CAAC5P,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0O,SAAS,CAAEmB,aAAa,IAAI;MAC5D;MACA,MAAMH,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;MAE/D;MACAE,aAAa,CAAC2B,OAAO,CAAEtD,YAAY,IAAI;QACrCwB,sBAAsB,CAACoE,GAAG,CAAC5F,YAAY,CAAC5I,EAAE,CAAC;MAC7C,CAAC,CAAC;MAEF;MACA,IAAI,CAACyO,0BAA0B,CAACrE,sBAAsB,CAAC;MAEvD;MACA,IAAI,CAACxC,cAAc,CAChBrL,sBAAsB,EAAE,CACxB+N,IAAI,CAAC7P,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAC;QACTC,IAAI,EAAGwC,MAAM,IAAI;UACfT,OAAO,CAACsD,GAAG,CACT,yDAAyD,EACzD7C,MAAM,CACP;UACD,IAAIA,MAAM,IAAIA,MAAM,CAACC,OAAO,EAAE;YAC5BV,OAAO,CAACsD,GAAG,CACT,GAAG7C,MAAM,CAACW,KAAK,uCAAuC,CACvD;YACD;YACA,IAAI,IAAI,CAACxE,KAAK,IAAI,IAAI,CAACA,KAAK,CAACzI,OAAO,CAACyJ,QAAQ,CAAC,aAAa,CAAC,EAAE;cAC5D,IAAI,CAAChB,KAAK,GAAG,IAAI;;;QAGvB,CAAC;QACDA,KAAK,EAAGgD,GAAG,IAAI;UACbI,OAAO,CAACpD,KAAK,CACX,4DAA4D,EAC5DgD,GAAG,CACJ;UACD;UACA,IAAI,CAAChD,KAAK,GAAGgD,GAAG;QAClB;OACD,CAAC;IACN,CAAC,CAAC;EACJ;EAEA9L,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC8I,KAAK,EAAEzI,OAAO,IAAI,wBAAwB;EACxD;EAEA;;;;EAIQ8K,yBAAyBA,CAAA;IAC/B,IAAI;MACF,MAAMsE,cAAc,GAAG7E,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;MACrE,IAAI4E,cAAc,EAAE;QAClB,OAAO,IAAI1G,GAAG,CAAS2G,IAAI,CAACC,KAAK,CAACF,cAAc,CAAC,CAAC;;MAEpD,OAAO,IAAI1G,GAAG,EAAU;KACzB,CAAC,OAAOD,KAAK,EAAE;MACdoD,OAAO,CAACpD,KAAK,CACX,qEAAqE,EACrEA,KAAK,CACN;MACD,OAAO,IAAIC,GAAG,EAAU;;EAE5B;EAEA;;;;EAIQwG,0BAA0BA,CAACK,UAAuB;IACxD,IAAI;MACFhF,YAAY,CAAC8C,OAAO,CAClB,wBAAwB,EACxBgC,IAAI,CAACG,SAAS,CAACC,KAAK,CAACC,IAAI,CAACH,UAAU,CAAC,CAAC,CACvC;MACD1D,OAAO,CAACsD,GAAG,CACT,GAAGI,UAAU,CAAClQ,IAAI,mEAAmE,CACtF;KACF,CAAC,OAAOoJ,KAAK,EAAE;MACdoD,OAAO,CAACpD,KAAK,CACX,mEAAmE,EACnEA,KAAK,CACN;;EAEL;EAEAkH,WAAWA,CAAA;IACT,IAAI,CAAC7G,QAAQ,CAACgB,IAAI,EAAE;IACpB,IAAI,CAAChB,QAAQ,CAAC8G,QAAQ,EAAE;EAC1B;EAEA;;;;;EAKAhO,eAAeA,CAACmK,cAAsB,EAAE8D,KAAY;IAClDA,KAAK,CAACnP,eAAe,EAAE,CAAC,CAAC;IAEzB,IAAI,IAAI,CAAC1B,qBAAqB,CAACkM,GAAG,CAACa,cAAc,CAAC,EAAE;MAClD,IAAI,CAAC/M,qBAAqB,CAAC8Q,MAAM,CAAC/D,cAAc,CAAC;KAClD,MAAM;MACL,IAAI,CAAC/M,qBAAqB,CAACiQ,GAAG,CAAClD,cAAc,CAAC;;IAGhD;IACA,IAAI,CAACgE,oBAAoB,EAAE;IAE3B;IACA,IAAI,CAAC7Q,gBAAgB,GAAG,IAAI,CAACF,qBAAqB,CAACK,IAAI,GAAG,CAAC;EAC7D;EAEA;;;;EAIApD,eAAeA,CAAC4T,KAAY;IAC1BA,KAAK,CAACnP,eAAe,EAAE,CAAC,CAAC;IAEzB,IAAI,CAACnE,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IAEpC,IAAI,CAACsH,sBAAsB,CAACkH,IAAI,CAAC5P,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0O,SAAS,CAAEmB,aAAa,IAAI;MACpE,IAAI,IAAI,CAACzO,WAAW,EAAE;QACpB;QACAyO,aAAa,CAAC2B,OAAO,CAAEtD,YAAY,IAAI;UACrC,IAAI,CAACrK,qBAAqB,CAACiQ,GAAG,CAAC5F,YAAY,CAAC5I,EAAE,CAAC;QACjD,CAAC,CAAC;OACH,MAAM;QACL;QACA,IAAI,CAACzB,qBAAqB,CAACC,KAAK,EAAE;;MAGpC;MACA,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACF,qBAAqB,CAACK,IAAI,GAAG,CAAC;IAC7D,CAAC,CAAC;EACJ;EAEA;;;EAGQ0Q,oBAAoBA,CAAA;IAC1B,IAAI,CAAClM,sBAAsB,CAACkH,IAAI,CAAC5P,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0O,SAAS,CAAEmB,aAAa,IAAI;MACpE,IAAI,CAACzO,WAAW,GACdyO,aAAa,CAAC7K,MAAM,GAAG,CAAC,IACxB,IAAI,CAACnB,qBAAqB,CAACK,IAAI,KAAK2L,aAAa,CAAC7K,MAAM;IAC5D,CAAC,CAAC;EACJ;EAEA;;;EAGAtB,2BAA2BA,CAAA;IACzB,IAAI,IAAI,CAACG,qBAAqB,CAACK,IAAI,KAAK,CAAC,EAAE;MACzC;;IAGF,MAAM2Q,WAAW,GAAGP,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC1Q,qBAAqB,CAAC;IAC1D6M,OAAO,CAACsD,GAAG,CAAC,8CAA8C,EAAEa,WAAW,CAAC;IAExE;IACA,IAAI,CAAChH,cAAc,CAAC+B,IAAI,CAAC5P,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0O,SAAS,CAAEmB,aAAa,IAAI;MAC5D,MAAMkB,oBAAoB,GAAGlB,aAAa,CAAC1P,MAAM,CAC9C+N,YAAY,IAAK,CAAC,IAAI,CAACrK,qBAAqB,CAACkM,GAAG,CAAC7B,YAAY,CAAC5I,EAAE,CAAC,CACnE;MAED;MACA,IAAI,CAAC4L,yBAAyB,CAACH,oBAAoB,CAAC;MAEpD;MACA,IAAI,CAACU,cAAc,EAAE;IACvB,CAAC,CAAC;IAEF;IACA,IAAI,CAACvE,cAAc,CAChB4H,2BAA2B,CAACD,WAAW,CAAC,CACxCjF,IAAI,CAAC7P,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAC;MACTC,IAAI,EAAGwC,MAAM,IAAI;QACfT,OAAO,CAACsD,GAAG,CAAC,sCAAsC,EAAE7C,MAAM,CAAC;QAC3D,IAAIA,MAAM,IAAIA,MAAM,CAACC,OAAO,EAAE;UAC5BV,OAAO,CAACsD,GAAG,CAAC,GAAG7C,MAAM,CAACW,KAAK,uCAAuC,CAAC;;MAEvE,CAAC;MACDxE,KAAK,EAAGgD,GAAG,IAAI;QACbI,OAAO,CAACpD,KAAK,CACX,2DAA2D,EAC3DgD,GAAG,CACJ;MACH;KACD,CAAC;EACN;EAEA;;;EAGA/M,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACM,qBAAqB,CAACK,IAAI,KAAK,CAAC,EAAE;MACzC;;IAGF,MAAM2Q,WAAW,GAAGP,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC1Q,qBAAqB,CAAC;IAC1D6M,OAAO,CAACsD,GAAG,CACT,sDAAsD,EACtDa,WAAW,CACZ;IAED;IACA,IAAI,CAAChH,cAAc,CAAC+B,IAAI,CAAC5P,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0O,SAAS,CAAEmB,aAAa,IAAI;MAC5D,MAAMkB,oBAAoB,GAAGlB,aAAa,CAAC/P,GAAG,CAAEoO,YAAY,IAC1D,IAAI,CAACrK,qBAAqB,CAACkM,GAAG,CAAC7B,YAAY,CAAC5I,EAAE,CAAC,GAC3C;QAAE,GAAG4I,YAAY;QAAE3G,MAAM,EAAE,IAAI;QAAEmD,MAAM,EAAE,IAAIsG,IAAI,EAAE,CAACC,WAAW;MAAE,CAAE,GACnE/C,YAAY,CACjB;MAED;MACA,IAAI,CAACgD,yBAAyB,CAACH,oBAAoB,CAAC;MAEpD;MACA,IAAI,CAACU,cAAc,EAAE;IACvB,CAAC,CAAC;IAEF;IACA,IAAI,CAACvE,cAAc,CAChB/G,UAAU,CAAC0O,WAAW,CAAC,CACvBjF,IAAI,CAAC7P,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAC;MACTC,IAAI,EAAGwC,MAAM,IAAI;QACfT,OAAO,CAACsD,GAAG,CAAC,gCAAgC,EAAE7C,MAAM,CAAC;QACrD,IAAIA,MAAM,IAAIA,MAAM,CAACC,OAAO,EAAE;UAC5BV,OAAO,CAACsD,GAAG,CAAC,+CAA+C,CAAC;;MAEhE,CAAC;MACD1G,KAAK,EAAGgD,GAAG,IAAI;QACbI,OAAO,CAACpD,KAAK,CACX,uDAAuD,EACvDgD,GAAG,CACJ;MACH;KACD,CAAC;EACN;EAEA;;;;;EAKA7I,UAAUA,CAACmJ,cAAsB;IAC/B,OAAO,IAAI,CAAC/M,qBAAqB,CAACkM,GAAG,CAACa,cAAc,CAAC;EACvD;;;uBAz8BW5D,yBAAyB,EAAA5M,EAAA,CAAA2U,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA7U,EAAA,CAAA2U,iBAAA,CAAAG,EAAA,CAAAC,YAAA,GAAA/U,EAAA,CAAA2U,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAzBrI,yBAAyB;MAAAsI,SAAA;MAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;mBAAzBC,GAAA,CAAApN,QAAA,CAAA9H,MAAA,CAAAqO,MAAA,CAAuB;UAAA;;;;;;;;UCjCpCzO,EAAA,CAAAC,cAAA,aAGC;;UAECD,EAAA,CAAAY,SAAA,aAEM;UAENZ,EAAA,CAAAC,cAAA,aAAsE;UAGhED,EAAA,CAAAY,SAAA,WAAgC;UAChCZ,EAAA,CAAAqB,MAAA,sBACF;UAAArB,EAAA,CAAAW,YAAA,EAAK;UAGLX,EAAA,CAAA8B,UAAA,IAAAyT,wCAAA,mBA+DM;UAGNvV,EAAA,CAAA8B,UAAA,IAAA0T,wCAAA,kBAgCM;UACRxV,EAAA,CAAAW,YAAA,EAAM;UAGNX,EAAA,CAAA8B,UAAA,KAAA2T,yCAAA,iBAGM;UAGNzV,EAAA,CAAA8B,UAAA,KAAA4T,yCAAA,kBAcM;UAGN1V,EAAA,CAAA8B,UAAA,KAAA6T,yCAAA,kBAYM;;UAGN3V,EAAA,CAAA8B,UAAA,KAAA8T,yCAAA,kBA6JM;;UACR5V,EAAA,CAAAW,YAAA,EAAM;UAIRX,EAAA,CAAAC,cAAA,eAIC;UADCD,EAAA,CAAAE,UAAA,mBAAA2V,yDAAA;YAAA,OAASP,GAAA,CAAAnD,qBAAA,EAAuB;UAAA,EAAC;UAEjCnS,EAAA,CAAAC,cAAA,eAA2E;UAAnCD,EAAA,CAAAE,UAAA,mBAAA4V,yDAAA1V,MAAA;YAAA,OAASA,MAAA,CAAA+E,eAAA,EAAwB;UAAA,EAAC;UACxEnF,EAAA,CAAAC,cAAA,eAAqC;UAEjCD,EAAA,CAAAY,SAAA,aAAqC;UACrCZ,EAAA,CAAAqB,MAAA,6BACF;UAAArB,EAAA,CAAAW,YAAA,EAAK;UACLX,EAAA,CAAAC,cAAA,kBAAyE;UAAlCD,EAAA,CAAAE,UAAA,mBAAA6V,4DAAA;YAAA,OAAST,GAAA,CAAAnD,qBAAA,EAAuB;UAAA,EAAC;UACtEnS,EAAA,CAAAY,SAAA,aAA4B;UAC9BZ,EAAA,CAAAW,YAAA,EAAS;UAEXX,EAAA,CAAAC,cAAA,eAAmC;UACjCD,EAAA,CAAA8B,UAAA,KAAAkU,yCAAA,iBAGM;UAENhW,EAAA,CAAA8B,UAAA,KAAAmU,yCAAA,kBAWM;UAENjW,EAAA,CAAA8B,UAAA,KAAAoU,yCAAA,kBA6DM;UACRlW,EAAA,CAAAW,YAAA,EAAM;UAKVX,EAAA,CAAAC,cAAA,eAIC;UADCD,EAAA,CAAAE,UAAA,mBAAAiW,yDAAA;YAAA,OAASb,GAAA,CAAA3J,6BAAA,EAA+B;UAAA,EAAC;UAEzC3L,EAAA,CAAAC,cAAA,eAA2E;UAAnCD,EAAA,CAAAE,UAAA,mBAAAkW,yDAAAhW,MAAA;YAAA,OAASA,MAAA,CAAA+E,eAAA,EAAwB;UAAA,EAAC;UACxEnF,EAAA,CAAAC,cAAA,eAAqC;UAEjCD,EAAA,CAAAY,SAAA,aAAuC;UACvCZ,EAAA,CAAAqB,MAAA,yCACF;UAAArB,EAAA,CAAAW,YAAA,EAAK;UACLX,EAAA,CAAAC,cAAA,kBAGC;UADCD,EAAA,CAAAE,UAAA,mBAAAmW,4DAAA;YAAA,OAASf,GAAA,CAAA3J,6BAAA,EAA+B;UAAA,EAAC;UAEzC3L,EAAA,CAAAY,SAAA,aAA4B;UAC9BZ,EAAA,CAAAW,YAAA,EAAS;UAEXX,EAAA,CAAA8B,UAAA,KAAAwU,yCAAA,oBAkNM;UACRtW,EAAA,CAAAW,YAAA,EAAM;;;UAvoBNX,EAAA,CAAA2C,WAAA,SAAA3C,EAAA,CAAAwC,WAAA,QAAA8S,GAAA,CAAA3H,WAAA,EAAkC;UAeD3N,EAAA,CAAAa,SAAA,GAAuB;UAAvBb,EAAA,CAAAc,UAAA,UAAAwU,GAAA,CAAA3R,gBAAA,CAAuB;UAkEL3D,EAAA,CAAAa,SAAA,GAAsB;UAAtBb,EAAA,CAAAc,UAAA,SAAAwU,GAAA,CAAA3R,gBAAA,CAAsB;UAoCjE3D,EAAA,CAAAa,SAAA,GAAa;UAAbb,EAAA,CAAAc,UAAA,SAAAwU,GAAA,CAAA3P,OAAA,CAAa;UAMb3F,EAAA,CAAAa,SAAA,GAAW;UAAXb,EAAA,CAAAc,UAAA,SAAAwU,GAAA,CAAApI,KAAA,CAAW;UAkBdlN,EAAA,CAAAa,SAAA,GAA+C;UAA/Cb,EAAA,CAAAc,UAAA,UAAAwU,GAAA,CAAA3P,OAAA,KAAA3F,EAAA,CAAAwC,WAAA,SAAA8S,GAAA,CAAA5S,gBAAA,IAA+C;UAe/C1C,EAAA,CAAAa,SAAA,GAA8C;UAA9Cb,EAAA,CAAAc,UAAA,UAAAwU,GAAA,CAAA3P,OAAA,IAAA3F,EAAA,CAAAwC,WAAA,SAAA8S,GAAA,CAAA5S,gBAAA,IAA8C;UAmKnD1C,EAAA,CAAAa,SAAA,GAAwD;UAAxDb,EAAA,CAAAuW,WAAA,YAAAjB,GAAA,CAAAlI,oBAAA,mBAAwD;UAc9CpN,EAAA,CAAAa,SAAA,GAAwB;UAAxBb,EAAA,CAAAc,UAAA,SAAAwU,GAAA,CAAAjI,kBAAA,CAAwB;UAM3BrN,EAAA,CAAAa,SAAA,GAA4D;UAA5Db,EAAA,CAAAc,UAAA,UAAAwU,GAAA,CAAAjI,kBAAA,IAAAiI,GAAA,CAAApL,kBAAA,CAAAtF,MAAA,OAA4D;UAa5D5E,EAAA,CAAAa,SAAA,GAA0D;UAA1Db,EAAA,CAAAc,UAAA,UAAAwU,GAAA,CAAAjI,kBAAA,IAAAiI,GAAA,CAAApL,kBAAA,CAAAtF,MAAA,KAA0D;UAoEjE5E,EAAA,CAAAa,SAAA,GAAgE;UAAhEb,EAAA,CAAAuW,WAAA,YAAAjB,GAAA,CAAAhI,4BAAA,mBAAgE;UAgB1BtN,EAAA,CAAAa,SAAA,GAAyB;UAAzBb,EAAA,CAAAc,UAAA,SAAAwU,GAAA,CAAAlL,mBAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}