{"ast": null, "code": "import { BehaviorSubject, Observable, of, throwError, retry, EMP<PERSON> } from 'rxjs';\nimport { map, catchError, tap, filter, switchMap } from 'rxjs/operators';\nimport { from } from 'rxjs';\nimport { MessageType, CallType, CallStatus } from '../models/message.model';\nimport { GET_CONVERSATIONS_QUERY, GET_NOTIFICATIONS_QUERY, NOTIFICATION_SUBSCRIPTION, GET_CONVERSATION_QUERY, SEND_MESSAGE_MUTATION, MARK_AS_READ_MUTATION, MESSAGE_SENT_SUBSCRIPTION, USER_STATUS_SUBSCRIPTION, GET_USER_QUERY, GET_ALL_USER_QUERY, CONVERSATION_UPDATED_SUBSCRIPTION, SEARCH_MESSAGES_QUERY, GET_UNREAD_MESSAGES_QUERY, SET_USER_ONLINE_MUTATION, SET_USER_OFFLINE_MUTATION, START_TYPING_MUTATION, STOP_TYPING_MUTATION, TYPING_INDICATOR_SUBSCRIPTION, GET_CURRENT_USER_QUERY, REACT_TO_MESSAGE_MUTATION, FORWARD_MESSAGE_MUTATION, PIN_MESSAGE_MUTATION, CREATE_GROUP_MUTATION, UPDATE_GROUP_MUTATION, DELETE_GROUP_MUTATION, LEAVE_GROUP_MUTATION, GET_GROUP_QUERY, GET_USER_GROUPS_QUERY, EDIT_MESSAGE_MUTATION, DELETE_MESSAGE_MUTATION, GET_MESSAGES_QUERY, GET_NOTIFICATIONS_ATTACHAMENTS, MARK_NOTIFICATION_READ_MUTATION, NOTIFICATIONS_READ_SUBSCRIPTION, CREATE_CONVERSATION_MUTATION, DELETE_NOTIFICATION_MUTATION, DELETE_MULTIPLE_NOTIFICATIONS_MUTATION, DELETE_ALL_NOTIFICATIONS_MUTATION,\n// Requêtes et mutations pour les appels\nCALL_HISTORY_QUERY, CALL_DETAILS_QUERY, CALL_STATS_QUERY, INITIATE_CALL_MUTATION, SEND_CALL_SIGNAL_MUTATION, ACCEPT_CALL_MUTATION, REJECT_CALL_MUTATION, END_CALL_MUTATION, TOGGLE_CALL_MEDIA_MUTATION, CALL_SIGNAL_SUBSCRIPTION, INCOMING_CALL_SUBSCRIPTION, GET_VOICE_MESSAGES_QUERY } from '../graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"./logger.service\";\nexport class MessageService {\n  constructor(apollo, logger, zone) {\n    this.apollo = apollo;\n    this.logger = logger;\n    this.zone = zone;\n    // État partagé\n    this.activeConversation = new BehaviorSubject(null);\n    this.notifications = new BehaviorSubject([]);\n    this.notificationCache = new Map();\n    this.notificationCount = new BehaviorSubject(0);\n    this.onlineUsers = new Map();\n    this.subscriptions = [];\n    this.CACHE_DURATION = 300000;\n    this.lastFetchTime = 0;\n    // Propriétés pour les appels\n    this.activeCall = new BehaviorSubject(null);\n    this.incomingCall = new BehaviorSubject(null);\n    this.callSignals = new BehaviorSubject(null);\n    this.localStream = null;\n    this.remoteStream = null;\n    this.peerConnection = null;\n    // Observables publics pour les appels\n    this.activeCall$ = this.activeCall.asObservable();\n    this.incomingCall$ = this.incomingCall.asObservable();\n    this.callSignals$ = this.callSignals.asObservable();\n    this.localStream$ = new BehaviorSubject(null);\n    this.remoteStream$ = new BehaviorSubject(null);\n    // Configuration WebRTC\n    this.rtcConfig = {\n      iceServers: [{\n        urls: 'stun:stun.l.google.com:19302'\n      }, {\n        urls: 'stun:stun1.l.google.com:19302'\n      }]\n    };\n    this.usersCache = [];\n    // Pagination metadata for user list\n    this.currentUserPagination = {\n      totalCount: 0,\n      totalPages: 0,\n      currentPage: 1,\n      hasNextPage: false,\n      hasPreviousPage: false\n    };\n    // Observables publics\n    this.activeConversation$ = this.activeConversation.asObservable();\n    this.notifications$ = this.notifications.asObservable();\n    this.notificationCount$ = this.notificationCount.asObservable();\n    // Propriétés pour la gestion des sons\n    this.sounds = {};\n    this.isPlaying = {};\n    this.muted = false;\n    // --------------------------------------------------------------------------\n    // Section 2: Méthodes pour les Notifications\n    // --------------------------------------------------------------------------\n    // Propriétés pour la pagination des notifications\n    this.notificationPagination = {\n      currentPage: 1,\n      limit: 10,\n      hasMoreNotifications: true\n    };\n    this.toSafeISOString = date => {\n      if (!date) return undefined;\n      return typeof date === 'string' ? date : date.toISOString();\n    };\n    this.loadNotificationsFromLocalStorage();\n    this.initSubscriptions();\n    this.startCleanupInterval();\n    this.preloadSounds();\n  }\n  /**\n   * Charge les notifications depuis le localStorage\n   * @private\n   */\n  loadNotificationsFromLocalStorage() {\n    try {\n      const savedNotifications = localStorage.getItem('notifications');\n      if (savedNotifications) {\n        const notifications = JSON.parse(savedNotifications);\n        this.logger.debug('MessageService', `Chargement de ${notifications.length} notifications depuis le localStorage`);\n        // Vider le cache avant de charger les notifications pour éviter les doublons\n        this.notificationCache.clear();\n        // Mettre à jour le cache avec les notifications sauvegardées\n        notifications.forEach(notification => {\n          // Vérifier que la notification a un ID valide\n          if (notification && notification.id) {\n            this.notificationCache.set(notification.id, notification);\n          }\n        });\n        // Mettre à jour le BehaviorSubject avec les notifications chargées\n        this.notifications.next(Array.from(this.notificationCache.values()));\n        this.updateUnreadCount();\n        this.logger.debug('MessageService', `${this.notificationCache.size} notifications chargées dans le cache`);\n      }\n    } catch (error) {\n      this.logger.error('MessageService', 'Erreur lors du chargement des notifications depuis le localStorage:', error);\n    }\n  }\n  initSubscriptions() {\n    this.zone.runOutsideAngular(() => {\n      this.subscribeToNewNotifications().subscribe();\n      this.subscribeToNotificationsRead().subscribe();\n      this.subscribeToIncomingCalls().subscribe();\n    });\n    this.subscribeToUserStatus();\n  }\n  /**\n   * S'abonne aux appels entrants\n   */\n  subscribeToIncomingCalls() {\n    return this.apollo.subscribe({\n      query: INCOMING_CALL_SUBSCRIPTION\n    }).pipe(map(({\n      data\n    }) => {\n      if (!data?.incomingCall) {\n        return null;\n      }\n      // Gérer l'appel entrant\n      this.handleIncomingCall(data.incomingCall);\n      return data.incomingCall;\n    }), catchError(error => {\n      this.logger.error('Error in incoming call subscription', error);\n      return of(null);\n    }));\n  }\n  /**\n   * Gère un appel entrant\n   */\n  handleIncomingCall(call) {\n    this.logger.debug('Incoming call received', call);\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n  }\n  // --------------------------------------------------------------------------\n  // Section: Gestion des sons (intégré depuis SoundService)\n  // --------------------------------------------------------------------------\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  preloadSounds() {\n    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n    this.loadSound('notification', 'assets/sounds/notification.mp3');\n  }\n  /**\n   * Charge un fichier audio\n   * @param name Nom du son\n   * @param path Chemin du fichier\n   */\n  loadSound(name, path) {\n    try {\n      const audio = new Audio(path);\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n      // Gérer la fin de la lecture\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n      });\n      this.logger.debug('MessageService', `Son chargé: ${name} (${path})`);\n    } catch (error) {\n      this.logger.error('MessageService', `Erreur lors du chargement du son ${name}:`, error);\n    }\n  }\n  /**\n   * Joue un son\n   * @param name Nom du son\n   * @param loop Lecture en boucle\n   */\n  play(name, loop = false) {\n    if (this.muted) {\n      this.logger.debug('MessageService', `Son ${name} non joué (muet)`);\n      return;\n    }\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        this.logger.warn('MessageService', `Son ${name} non trouvé`);\n        return;\n      }\n      // Configurer la lecture en boucle\n      sound.loop = loop;\n      // Jouer le son s'il n'est pas déjà en cours de lecture\n      if (!this.isPlaying[name]) {\n        sound.currentTime = 0;\n        sound.play().catch(error => {\n          this.logger.error('MessageService', `Erreur lors de la lecture du son ${name}:`, error);\n        });\n        this.isPlaying[name] = true;\n        this.logger.debug('MessageService', `Lecture du son: ${name}, boucle: ${loop}`);\n      }\n    } catch (error) {\n      this.logger.error('MessageService', `Erreur lors de la lecture du son ${name}:`, error);\n    }\n  }\n  /**\n   * Arrête un son\n   * @param name Nom du son\n   */\n  stop(name) {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        this.logger.warn('MessageService', `Son ${name} non trouvé`);\n        return;\n      }\n      // Arrêter le son s'il est en cours de lecture\n      if (this.isPlaying[name]) {\n        sound.pause();\n        sound.currentTime = 0;\n        this.isPlaying[name] = false;\n        this.logger.debug('MessageService', `Son arrêté: ${name}`);\n      }\n    } catch (error) {\n      this.logger.error('MessageService', `Erreur lors de l'arrêt du son ${name}:`, error);\n    }\n  }\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds() {\n    Object.keys(this.sounds).forEach(name => {\n      this.stop(name);\n    });\n    this.logger.debug('MessageService', 'Tous les sons ont été arrêtés');\n  }\n  /**\n   * Active ou désactive le son\n   * @param muted True pour désactiver le son, false pour l'activer\n   */\n  setMuted(muted) {\n    this.muted = muted;\n    this.logger.info('MessageService', `Son ${muted ? 'désactivé' : 'activé'}`);\n    if (muted) {\n      this.stopAllSounds();\n    }\n  }\n  /**\n   * Vérifie si le son est désactivé\n   * @returns True si le son est désactivé, false sinon\n   */\n  isMuted() {\n    return this.muted;\n  }\n  /**\n   * Joue le son de notification\n   */\n  playNotificationSound() {\n    console.log('MessageService: Tentative de lecture du son de notification');\n    if (this.muted) {\n      console.log('MessageService: Son désactivé, notification ignorée');\n      return;\n    }\n    // Utiliser l'API Web Audio pour générer un son de notification simple\n    try {\n      // Créer un contexte audio\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      // Créer un oscillateur pour générer un son\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n      // Configurer l'oscillateur\n      oscillator.type = 'sine';\n      oscillator.frequency.setValueAtTime(880, audioContext.currentTime); // La note A5\n      // Configurer le volume\n      gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n      gainNode.gain.linearRampToValueAtTime(0.5, audioContext.currentTime + 0.01);\n      gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.3);\n      // Connecter les nœuds\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n      // Démarrer et arrêter l'oscillateur\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.3);\n      console.log('MessageService: Son de notification généré avec succès');\n    } catch (error) {\n      console.error('MessageService: Erreur lors de la génération du son:', error);\n      // Fallback à la méthode originale en cas d'erreur\n      try {\n        const audio = new Audio('assets/sounds/notification.mp3');\n        audio.volume = 1.0; // Volume maximum\n        audio.play().catch(err => {\n          console.error('MessageService: Erreur lors de la lecture du fichier son:', err);\n        });\n      } catch (audioError) {\n        console.error('MessageService: Exception lors de la lecture du fichier son:', audioError);\n      }\n    }\n  }\n  // --------------------------------------------------------------------------\n  // Section 1: Méthodes pour les Messages\n  // --------------------------------------------------------------------------\n  /**\n   * Envoie un message vocal à un utilisateur\n   * @param receiverId ID de l'utilisateur destinataire\n   * @param audioBlob Blob audio à envoyer\n   * @param conversationId ID de la conversation (optionnel)\n   * @param duration Durée de l'enregistrement en secondes (optionnel)\n   * @returns Observable avec le message envoyé\n   */\n  sendVoiceMessage(receiverId, audioBlob, conversationId, duration) {\n    this.logger.debug(`[MessageService] Sending voice message to user ${receiverId}, duration: ${duration}s`);\n    // Vérifier que le blob audio est valide\n    if (!audioBlob || audioBlob.size === 0) {\n      this.logger.error('[MessageService] Invalid audio blob');\n      return throwError(() => new Error('Invalid audio blob'));\n    }\n    // Créer un fichier à partir du blob audio avec un nom unique\n    const timestamp = Date.now();\n    const audioFile = new File([audioBlob], `voice-message-${timestamp}.webm`, {\n      type: 'audio/webm',\n      lastModified: timestamp\n    });\n    // Vérifier que le fichier a été créé correctement\n    if (!audioFile || audioFile.size === 0) {\n      this.logger.error('[MessageService] Failed to create audio file');\n      return throwError(() => new Error('Failed to create audio file'));\n    }\n    this.logger.debug(`[MessageService] Created audio file: ${audioFile.name}, size: ${audioFile.size} bytes`);\n    // Créer des métadonnées pour le message vocal\n    const metadata = {\n      duration: duration || 0,\n      isVoiceMessage: true,\n      timestamp: timestamp\n    };\n    // Utiliser la méthode sendMessage avec le type VOICE_MESSAGE\n    // Utiliser une chaîne vide comme contenu pour éviter les problèmes de validation\n    return this.sendMessage(receiverId, ' ',\n    // Espace comme contenu minimal pour passer la validation\n    audioFile, MessageType.VOICE_MESSAGE, conversationId, undefined, metadata);\n  }\n  /**\n   * Joue un fichier audio\n   * @param audioUrl URL du fichier audio à jouer\n   * @returns Promise qui se résout lorsque la lecture est terminée\n   */\n  playAudio(audioUrl) {\n    return new Promise((resolve, reject) => {\n      const audio = new Audio(audioUrl);\n      audio.onended = () => {\n        resolve();\n      };\n      audio.onerror = error => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      };\n      audio.play().catch(error => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      });\n    });\n  }\n  /**\n   * Récupère tous les messages vocaux de l'utilisateur\n   * @returns Observable avec la liste des messages vocaux\n   */\n  getVoiceMessages() {\n    this.logger.debug('[MessageService] Getting voice messages');\n    return this.apollo.watchQuery({\n      query: GET_VOICE_MESSAGES_QUERY,\n      fetchPolicy: 'network-only' // Ne pas utiliser le cache pour cette requête\n    }).valueChanges.pipe(map(result => {\n      const voiceMessages = result.data?.getVoiceMessages || [];\n      this.logger.debug(`[MessageService] Retrieved ${voiceMessages.length} voice messages`);\n      return voiceMessages;\n    }), catchError(error => {\n      this.logger.error('[MessageService] Error fetching voice messages:', error);\n      return throwError(() => new Error('Failed to fetch voice messages'));\n    }));\n  }\n  // Message methods\n  getMessages(senderId, receiverId, conversationId, page = 1, limit = 10) {\n    return this.apollo.watchQuery({\n      query: GET_MESSAGES_QUERY,\n      variables: {\n        senderId,\n        receiverId,\n        conversationId,\n        limit,\n        page\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const messages = result.data?.getMessages || [];\n      return messages.map(msg => this.normalizeMessage(msg));\n    }), catchError(error => {\n      this.logger.error('Error fetching messages:', error);\n      return throwError(() => new Error('Failed to fetch messages'));\n    }));\n  }\n  editMessage(messageId, newContent) {\n    return this.apollo.mutate({\n      mutation: EDIT_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        newContent\n      }\n    }).pipe(map(result => {\n      if (!result.data?.editMessage) {\n        throw new Error('Failed to edit message');\n      }\n      return this.normalizeMessage(result.data.editMessage);\n    }), catchError(error => {\n      this.logger.error('Error editing message:', error);\n      return throwError(() => new Error('Failed to edit message'));\n    }));\n  }\n  deleteMessage(messageId) {\n    return this.apollo.mutate({\n      mutation: DELETE_MESSAGE_MUTATION,\n      variables: {\n        messageId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.deleteMessage) {\n        throw new Error('Failed to delete message');\n      }\n      return this.normalizeMessage(result.data.deleteMessage);\n    }), catchError(error => {\n      this.logger.error('Error deleting message:', error);\n      return throwError(() => new Error('Failed to delete message'));\n    }));\n  }\n  sendMessage(receiverId, content, file, messageType = MessageType.TEXT, conversationId, replyTo, metadata) {\n    this.logger.info(`[MessageService] Sending message to: ${receiverId}, hasFile: ${!!file}`);\n    this.logger.debug(`[MessageService] Message content: \"${content?.substring(0, 50)}${content?.length > 50 ? '...' : ''}\"`);\n    // Vérifier l'authentification\n    const token = localStorage.getItem('token');\n    this.logger.debug(`[MessageService] Authentication check before sending message: token=${!!token}`);\n    // Utiliser le type de message fourni ou le déterminer automatiquement\n    let finalMessageType = messageType;\n    // Si le type n'est pas explicitement fourni et qu'il y a un fichier, déterminer le type\n    if (file) {\n      // Si le type est déjà VOICE_MESSAGE, le conserver\n      if (messageType === MessageType.VOICE_MESSAGE) {\n        finalMessageType = MessageType.VOICE_MESSAGE;\n        this.logger.debug(`[MessageService] Using explicit VOICE_MESSAGE type`);\n      }\n      // Sinon, déterminer le type en fonction du type de fichier\n      else if (messageType === MessageType.TEXT) {\n        if (file.type.startsWith('image/')) {\n          finalMessageType = MessageType.IMAGE;\n        } else if (file.type.startsWith('video/')) {\n          finalMessageType = MessageType.VIDEO;\n        } else if (file.type.startsWith('audio/')) {\n          // Vérifier si c'est un message vocal basé sur les métadonnées\n          if (metadata && metadata.isVoiceMessage) {\n            finalMessageType = MessageType.VOICE_MESSAGE;\n          } else {\n            finalMessageType = MessageType.AUDIO;\n          }\n        } else {\n          finalMessageType = MessageType.FILE;\n        }\n      }\n    }\n    this.logger.debug(`[MessageService] Message type determined: ${finalMessageType}`);\n    // Ajouter le type de message aux variables\n    // Utiliser directement la valeur de l'énumération sans conversion\n    const variables = {\n      receiverId,\n      content,\n      type: finalMessageType // Ajouter explicitement le type de message\n    };\n    // Forcer le type à être une valeur d'énumération GraphQL\n    // Cela empêche Apollo de convertir la valeur en minuscules\n    if (variables.type) {\n      Object.defineProperty(variables, 'type', {\n        value: finalMessageType,\n        enumerable: true,\n        writable: false\n      });\n    }\n    // Ajouter les métadonnées si elles sont fournies\n    if (metadata) {\n      variables.metadata = metadata;\n      this.logger.debug(`[MessageService] Metadata attached:`, metadata);\n    }\n    if (file) {\n      variables.file = file;\n      this.logger.debug(`[MessageService] File attached: ${file.name}, size: ${file.size}, type: ${file.type}, messageType: ${finalMessageType}`);\n    }\n    if (conversationId) {\n      variables.conversationId = conversationId;\n      this.logger.debug(`[MessageService] Using existing conversation: ${conversationId}`);\n    }\n    if (replyTo) {\n      variables.replyTo = replyTo;\n      this.logger.debug(`[MessageService] Replying to message: ${replyTo}`);\n    }\n    const context = file ? {\n      useMultipart: true,\n      file\n    } : undefined;\n    this.logger.debug(`[MessageService] Sending GraphQL mutation with variables:`, variables);\n    return this.apollo.mutate({\n      mutation: SEND_MESSAGE_MUTATION,\n      variables,\n      context\n    }).pipe(map(result => {\n      this.logger.debug(`[MessageService] Message send response:`, result);\n      if (!result.data?.sendMessage) {\n        this.logger.error(`[MessageService] Failed to send message: No data returned`);\n        throw new Error('Failed to send message');\n      }\n      try {\n        this.logger.debug(`[MessageService] Normalizing sent message`, result.data.sendMessage);\n        const normalizedMessage = this.normalizeMessage(result.data.sendMessage);\n        this.logger.info(`[MessageService] Message sent successfully: ${normalizedMessage.id}`);\n        return normalizedMessage;\n      } catch (normalizationError) {\n        this.logger.error(`[MessageService] Error normalizing message:`, normalizationError);\n        // Retourner un message minimal mais valide plutôt que de lancer une erreur\n        const minimalMessage = {\n          id: result.data.sendMessage.id || 'temp-' + Date.now(),\n          content: result.data.sendMessage.content || '',\n          type: result.data.sendMessage.type || MessageType.TEXT,\n          timestamp: new Date(),\n          isRead: false,\n          sender: {\n            id: this.getCurrentUserId(),\n            username: 'You'\n          }\n        };\n        this.logger.info(`[MessageService] Returning minimal message: ${minimalMessage.id}`);\n        return minimalMessage;\n      }\n    }), catchError(error => {\n      this.logger.error(`[MessageService] Error sending message:`, error);\n      return throwError(() => new Error('Failed to send message'));\n    }));\n  }\n  markMessageAsRead(messageId) {\n    return this.apollo.mutate({\n      mutation: MARK_AS_READ_MUTATION,\n      variables: {\n        messageId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.markMessageAsRead) throw new Error('Failed to mark message as read');\n      return {\n        ...result.data.markMessageAsRead,\n        readAt: new Date()\n      };\n    }), catchError(error => {\n      console.error('Error marking message as read:', error);\n      return throwError(() => new Error('Failed to mark message as read'));\n    }));\n  }\n  reactToMessage(messageId, emoji) {\n    return this.apollo.mutate({\n      mutation: REACT_TO_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        emoji\n      }\n    }).pipe(map(result => {\n      if (!result.data?.reactToMessage) throw new Error('Failed to react to message');\n      return result.data.reactToMessage;\n    }), catchError(error => {\n      console.error('Error reacting to message:', error);\n      return throwError(() => new Error('Failed to react to message'));\n    }));\n  }\n  forwardMessage(messageId, conversationIds) {\n    return this.apollo.mutate({\n      mutation: FORWARD_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        conversationIds\n      }\n    }).pipe(map(result => {\n      if (!result.data?.forwardMessage) throw new Error('Failed to forward message');\n      return result.data.forwardMessage.map(msg => ({\n        ...msg,\n        timestamp: msg.timestamp ? this.normalizeDate(msg.timestamp) : new Date()\n      }));\n    }), catchError(error => {\n      console.error('Error forwarding message:', error);\n      return throwError(() => new Error('Failed to forward message'));\n    }));\n  }\n  pinMessage(messageId, conversationId) {\n    return this.apollo.mutate({\n      mutation: PIN_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        conversationId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.pinMessage) throw new Error('Failed to pin message');\n      return {\n        ...result.data.pinMessage,\n        pinnedAt: new Date()\n      };\n    }), catchError(error => {\n      console.error('Error pinning message:', error);\n      return throwError(() => new Error('Failed to pin message'));\n    }));\n  }\n  searchMessages(query, conversationId, filters = {}) {\n    return this.apollo.watchQuery({\n      query: SEARCH_MESSAGES_QUERY,\n      variables: {\n        query,\n        conversationId,\n        ...filters,\n        dateFrom: this.toSafeISOString(filters.dateFrom),\n        dateTo: this.toSafeISOString(filters.dateTo)\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => result.data?.searchMessages?.map(msg => ({\n      ...msg,\n      timestamp: this.safeDate(msg.timestamp),\n      sender: this.normalizeUser(msg.sender)\n    })) || []), catchError(error => {\n      console.error('Error searching messages:', error);\n      return throwError(() => new Error('Failed to search messages'));\n    }));\n  }\n  getUnreadMessages(userId) {\n    return this.apollo.watchQuery({\n      query: GET_UNREAD_MESSAGES_QUERY,\n      variables: {\n        userId\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => result.data?.getUnreadMessages?.map(msg => ({\n      ...msg,\n      timestamp: this.safeDate(msg.timestamp),\n      sender: this.normalizeUser(msg.sender)\n    })) || []), catchError(error => {\n      console.error('Error fetching unread messages:', error);\n      return throwError(() => new Error('Failed to fetch unread messages'));\n    }));\n  }\n  setActiveConversation(conversationId) {\n    this.activeConversation.next(conversationId);\n  }\n  getConversations() {\n    return this.apollo.watchQuery({\n      query: GET_CONVERSATIONS_QUERY,\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const conversations = result.data?.getConversations || [];\n      return conversations.map(conv => this.normalizeConversation(conv));\n    }), catchError(error => {\n      console.error('Error fetching conversations:', error);\n      return throwError(() => new Error('Failed to load conversations'));\n    }));\n  }\n  getConversation(conversationId, limit, page) {\n    this.logger.info(`[MessageService] Getting conversation: ${conversationId}, limit: ${limit}, page: ${page}`);\n    const variables = {\n      conversationId\n    };\n    // Ajouter les paramètres de pagination s'ils sont fournis\n    if (limit !== undefined) {\n      variables.limit = limit;\n    } else {\n      variables.limit = 10; // Valeur par défaut\n    }\n    // Calculer l'offset à partir de la page si elle est fournie\n    if (page !== undefined) {\n      // La requête GraphQL utilise offset, donc nous devons convertir la page en offset\n      const offset = (page - 1) * variables.limit;\n      variables.offset = offset;\n      this.logger.debug(`[MessageService] Calculated offset: ${offset} from page: ${page} and limit: ${variables.limit}`);\n    } else {\n      variables.offset = 0; // Valeur par défaut\n    }\n\n    this.logger.debug(`[MessageService] Final pagination parameters: limit=${variables.limit}, offset=${variables.offset}`);\n    return this.apollo.watchQuery({\n      query: GET_CONVERSATION_QUERY,\n      variables: variables,\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      this.logger.debug(`[MessageService] Conversation response received:`, result);\n      const conv = result.data?.getConversation;\n      if (!conv) {\n        this.logger.error(`[MessageService] Conversation not found: ${conversationId}`);\n        throw new Error('Conversation not found');\n      }\n      this.logger.debug(`[MessageService] Normalizing conversation: ${conversationId}`);\n      const normalizedConversation = this.normalizeConversation(conv);\n      this.logger.info(`[MessageService] Conversation loaded successfully: ${conversationId}, participants: ${normalizedConversation.participants?.length || 0}, messages: ${normalizedConversation.messages?.length || 0}`);\n      return normalizedConversation;\n    }), catchError(error => {\n      this.logger.error(`[MessageService] Error fetching conversation:`, error);\n      return throwError(() => new Error('Failed to load conversation'));\n    }));\n  }\n  createConversation(userId) {\n    this.logger.info(`[MessageService] Creating conversation with user: ${userId}`);\n    if (!userId) {\n      this.logger.error(`[MessageService] Cannot create conversation: userId is undefined`);\n      return throwError(() => new Error('User ID is required to create a conversation'));\n    }\n    return this.apollo.mutate({\n      mutation: CREATE_CONVERSATION_MUTATION,\n      variables: {\n        userId\n      }\n    }).pipe(map(result => {\n      this.logger.debug(`[MessageService] Conversation creation response:`, result);\n      const conversation = result.data?.createConversation;\n      if (!conversation) {\n        this.logger.error(`[MessageService] Failed to create conversation with user: ${userId}`);\n        throw new Error('Failed to create conversation');\n      }\n      try {\n        const normalizedConversation = this.normalizeConversation(conversation);\n        this.logger.info(`[MessageService] Conversation created successfully: ${normalizedConversation.id}`);\n        return normalizedConversation;\n      } catch (error) {\n        this.logger.error(`[MessageService] Error normalizing created conversation:`, error);\n        throw new Error('Error processing created conversation');\n      }\n    }), catchError(error => {\n      this.logger.error(`[MessageService] Error creating conversation with user ${userId}:`, error);\n      return throwError(() => new Error(`Failed to create conversation: ${error.message}`));\n    }));\n  }\n  /**\n   * Récupère une conversation existante ou en crée une nouvelle si elle n'existe pas\n   * @param userId ID de l'utilisateur avec qui créer/récupérer une conversation\n   * @returns Observable avec la conversation\n   */\n  getOrCreateConversation(userId) {\n    this.logger.info(`[MessageService] Getting or creating conversation with user: ${userId}`);\n    if (!userId) {\n      this.logger.error(`[MessageService] Cannot get/create conversation: userId is undefined`);\n      return throwError(() => new Error('User ID is required to get/create a conversation'));\n    }\n    // D'abord, essayons de trouver une conversation existante entre les deux utilisateurs\n    return this.getConversations().pipe(map(conversations => {\n      // Récupérer l'ID de l'utilisateur actuel\n      const currentUserId = this.getCurrentUserId();\n      // Chercher une conversation directe (non groupe) entre les deux utilisateurs\n      const existingConversation = conversations.find(conv => {\n        if (conv.isGroup) return false;\n        // Vérifier si la conversation contient les deux utilisateurs\n        const participantIds = conv.participants?.map(p => p.id || p._id) || [];\n        return participantIds.includes(userId) && participantIds.includes(currentUserId);\n      });\n      if (existingConversation) {\n        this.logger.info(`[MessageService] Found existing conversation: ${existingConversation.id}`);\n        return existingConversation;\n      }\n      // Si aucune conversation n'est trouvée, en créer une nouvelle\n      throw new Error('No existing conversation found');\n    }), catchError(error => {\n      this.logger.info(`[MessageService] No existing conversation found, creating new one: ${error.message}`);\n      return this.createConversation(userId);\n    }));\n  }\n  getNotifications(refresh = false, page = 1, limit = 10) {\n    this.logger.info('MessageService', `Fetching notifications, refresh: ${refresh}, page: ${page}, limit: ${limit}`);\n    this.logger.debug('MessageService', 'Using query', {\n      query: GET_NOTIFICATIONS_QUERY\n    });\n    // Si refresh est true, réinitialiser la pagination mais ne pas vider le cache\n    // pour conserver les suppressions locales\n    if (refresh) {\n      this.logger.debug('MessageService', 'Resetting pagination due to refresh');\n      this.notificationPagination.currentPage = 1;\n      this.notificationPagination.hasMoreNotifications = true;\n    }\n    // Mettre à jour les paramètres de pagination\n    this.notificationPagination.currentPage = page;\n    this.notificationPagination.limit = limit;\n    // Récupérer les IDs des notifications supprimées du localStorage\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    this.logger.debug('MessageService', `Found ${deletedNotificationIds.size} deleted notification IDs in localStorage`);\n    return this.apollo.watchQuery({\n      query: GET_NOTIFICATIONS_QUERY,\n      variables: {\n        page: page,\n        limit: limit\n      },\n      fetchPolicy: refresh ? 'network-only' : 'cache-first'\n    }).valueChanges.pipe(map(result => {\n      this.logger.debug('MessageService', 'Notifications response received');\n      if (result.errors) {\n        this.logger.error('MessageService', 'GraphQL errors:', result.errors);\n        throw new Error(result.errors.map(e => e.message).join(', '));\n      }\n      const notifications = result.data?.getUserNotifications || [];\n      this.logger.debug('MessageService', `Received ${notifications.length} notifications from server for page ${page}`);\n      // Vérifier s'il y a plus de notifications à charger\n      this.notificationPagination.hasMoreNotifications = notifications.length >= limit;\n      if (notifications.length === 0) {\n        this.logger.info('MessageService', 'No notifications received from server');\n        this.notificationPagination.hasMoreNotifications = false;\n      }\n      // Filtrer les notifications supprimées\n      const filteredNotifications = notifications.filter(notif => !deletedNotificationIds.has(notif.id));\n      this.logger.debug('MessageService', `Filtered out ${notifications.length - filteredNotifications.length} deleted notifications`);\n      // Afficher les notifications reçues pour le débogage\n      filteredNotifications.forEach((notif, index) => {\n        console.log(`Notification ${index + 1} (page ${page}):`, {\n          id: notif.id || notif._id,\n          type: notif.type,\n          content: notif.content,\n          isRead: notif.isRead\n        });\n      });\n      // Vérifier si les notifications existent déjà dans le cache avant de les ajouter\n      // Mettre à jour le cache avec les nouvelles notifications\n      this.updateCache(filteredNotifications);\n      // Récupérer toutes les notifications du cache\n      const cachedNotifications = Array.from(this.notificationCache.values());\n      console.log(`Total notifications in cache after update: ${cachedNotifications.length}`);\n      // Mettre à jour le BehaviorSubject avec toutes les notifications\n      this.notifications.next(cachedNotifications);\n      // Mettre à jour le compteur de notifications non lues\n      this.updateUnreadCount();\n      // Sauvegarder les notifications dans le localStorage\n      this.saveNotificationsToLocalStorage();\n      return cachedNotifications;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error loading notifications:', error);\n      if (error.graphQLErrors) {\n        this.logger.error('MessageService', 'GraphQL errors:', error.graphQLErrors);\n      }\n      if (error.networkError) {\n        this.logger.error('MessageService', 'Network error:', error.networkError);\n      }\n      return throwError(() => new Error('Failed to load notifications'));\n    }));\n  }\n  /**\n   * Récupère les IDs des notifications supprimées du localStorage\n   * @private\n   * @returns Set contenant les IDs des notifications supprimées\n   */\n  getDeletedNotificationIds() {\n    try {\n      const deletedIds = new Set();\n      const savedNotifications = localStorage.getItem('notifications');\n      // Si aucune notification n'est sauvegardée, retourner un ensemble vide\n      if (!savedNotifications) {\n        return deletedIds;\n      }\n      // Récupérer les IDs des notifications sauvegardées\n      const savedNotificationIds = new Set(JSON.parse(savedNotifications).map(n => n.id));\n      // Récupérer les notifications du serveur (si disponibles dans le cache Apollo)\n      const serverNotifications = this.apollo.client.readQuery({\n        query: GET_NOTIFICATIONS_QUERY\n      })?.getUserNotifications || [];\n      // Pour chaque notification du serveur, vérifier si elle est dans les notifications sauvegardées\n      serverNotifications.forEach(notification => {\n        if (!savedNotificationIds.has(notification.id)) {\n          deletedIds.add(notification.id);\n        }\n      });\n      return deletedIds;\n    } catch (error) {\n      this.logger.error('MessageService', 'Erreur lors de la récupération des IDs de notifications supprimées:', error);\n      return new Set();\n    }\n  }\n  // Méthode pour vérifier s'il y a plus de notifications à charger\n  hasMoreNotifications() {\n    return this.notificationPagination.hasMoreNotifications;\n  }\n  // Méthode pour charger la page suivante de notifications\n  loadMoreNotifications() {\n    const nextPage = this.notificationPagination.currentPage + 1;\n    return this.getNotifications(false, nextPage, this.notificationPagination.limit);\n  }\n  getNotificationById(id) {\n    return this.notifications$.pipe(map(notifications => notifications.find(n => n.id === id)), catchError(error => {\n      this.logger.error('Error finding notification:', error);\n      return throwError(() => new Error('Failed to find notification'));\n    }));\n  }\n  getNotificationCount() {\n    return this.notifications.value?.length || 0;\n  }\n  getNotificationAttachments(notificationId) {\n    return this.apollo.query({\n      query: GET_NOTIFICATIONS_ATTACHAMENTS,\n      variables: {\n        id: notificationId\n      },\n      fetchPolicy: 'network-only'\n    }).pipe(map(result => result.data?.getNotificationAttachments || []), catchError(error => {\n      this.logger.error('Error fetching notification attachments:', error);\n      return throwError(() => new Error('Failed to fetch attachments'));\n    }));\n  }\n  getUnreadNotifications() {\n    return this.notifications$.pipe(map(notifications => notifications.filter(n => !n.isRead)));\n  }\n  /**\n   * Supprime une notification\n   * @param notificationId ID de la notification à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteNotification(notificationId) {\n    this.logger.debug('MessageService', `Suppression de la notification ${notificationId}`);\n    if (!notificationId) {\n      this.logger.warn('MessageService', 'ID de notification invalide');\n      return throwError(() => new Error('ID de notification invalide'));\n    }\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    this.notificationCache.delete(notificationId);\n    this.notifications.next(Array.from(this.notificationCache.values()));\n    this.updateUnreadCount();\n    this.saveNotificationsToLocalStorage();\n    // Appeler le backend pour supprimer la notification\n    return this.apollo.mutate({\n      mutation: DELETE_NOTIFICATION_MUTATION,\n      variables: {\n        notificationId\n      }\n    }).pipe(map(result => {\n      const response = result.data?.deleteNotification;\n      if (!response) {\n        throw new Error('Réponse de suppression invalide');\n      }\n      this.logger.debug('MessageService', 'Résultat de la suppression:', response);\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Erreur lors de la suppression de la notification:', error);\n      // En cas d'erreur, on garde la suppression locale\n      return of({\n        success: true,\n        message: 'Notification supprimée localement (erreur serveur)'\n      });\n    }));\n  }\n  /**\n   * Sauvegarde les notifications dans le localStorage\n   * @private\n   */\n  saveNotificationsToLocalStorage() {\n    try {\n      const notifications = Array.from(this.notificationCache.values());\n      localStorage.setItem('notifications', JSON.stringify(notifications));\n      this.logger.debug('MessageService', 'Notifications sauvegardées localement');\n    } catch (error) {\n      this.logger.error('MessageService', 'Erreur lors de la sauvegarde des notifications:', error);\n    }\n  }\n  /**\n   * Supprime toutes les notifications de l'utilisateur\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteAllNotifications() {\n    this.logger.debug('MessageService', 'Suppression de toutes les notifications');\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const count = this.notificationCache.size;\n    this.notificationCache.clear();\n    this.notifications.next([]);\n    this.notificationCount.next(0);\n    this.saveNotificationsToLocalStorage();\n    // Appeler le backend pour supprimer toutes les notifications\n    return this.apollo.mutate({\n      mutation: DELETE_ALL_NOTIFICATIONS_MUTATION\n    }).pipe(map(result => {\n      const response = result.data?.deleteAllNotifications;\n      if (!response) {\n        throw new Error('Réponse de suppression invalide');\n      }\n      this.logger.debug('MessageService', 'Résultat de la suppression de toutes les notifications:', response);\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Erreur lors de la suppression de toutes les notifications:', error);\n      // En cas d'erreur, on garde la suppression locale\n      return of({\n        success: true,\n        count,\n        message: `${count} notifications supprimées localement (erreur serveur)`\n      });\n    }));\n  }\n  /**\n   * Supprime plusieurs notifications\n   * @param notificationIds IDs des notifications à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteMultipleNotifications(notificationIds) {\n    this.logger.debug('MessageService', `Suppression de ${notificationIds.length} notifications`);\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'Aucun ID de notification fourni');\n      return throwError(() => new Error('Aucun ID de notification fourni'));\n    }\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    let count = 0;\n    notificationIds.forEach(id => {\n      if (this.notificationCache.has(id)) {\n        this.notificationCache.delete(id);\n        count++;\n      }\n    });\n    this.notifications.next(Array.from(this.notificationCache.values()));\n    this.updateUnreadCount();\n    this.saveNotificationsToLocalStorage();\n    // Appeler le backend pour supprimer les notifications\n    return this.apollo.mutate({\n      mutation: DELETE_MULTIPLE_NOTIFICATIONS_MUTATION,\n      variables: {\n        notificationIds\n      }\n    }).pipe(map(result => {\n      const response = result.data?.deleteMultipleNotifications;\n      if (!response) {\n        throw new Error('Réponse de suppression invalide');\n      }\n      this.logger.debug('MessageService', 'Résultat de la suppression multiple:', response);\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Erreur lors de la suppression multiple de notifications:', error);\n      // En cas d'erreur, on garde la suppression locale\n      return of({\n        success: count > 0,\n        count,\n        message: `${count} notifications supprimées localement (erreur serveur)`\n      });\n    }));\n  }\n  groupNotificationsByType() {\n    return this.notifications$.pipe(map(notifications => {\n      const groups = new Map();\n      notifications.forEach(notif => {\n        if (!groups.has(notif.type)) {\n          groups.set(notif.type, []);\n        }\n        groups.get(notif.type)?.push(notif);\n      });\n      return groups;\n    }));\n  }\n  markAsRead(notificationIds) {\n    this.logger.debug('MessageService', `Marking notifications as read: ${notificationIds?.join(', ') || 'none'}`);\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'No notification IDs provided');\n      return of({\n        success: false,\n        readCount: 0,\n        remainingCount: this.notificationCount.value\n      });\n    }\n    // Vérifier que tous les IDs sont valides\n    const validIds = notificationIds.filter(id => id && typeof id === 'string' && id.trim() !== '');\n    if (validIds.length !== notificationIds.length) {\n      this.logger.error('MessageService', 'Some notification IDs are invalid', {\n        provided: notificationIds,\n        valid: validIds\n      });\n      return throwError(() => new Error('Some notification IDs are invalid'));\n    }\n    this.logger.debug('MessageService', 'Sending mutation to mark notifications as read', validIds);\n    // Mettre à jour localement d'abord pour une meilleure expérience utilisateur\n    this.updateNotificationStatus(validIds, true);\n    // Créer une réponse optimiste\n    const optimisticResponse = {\n      markNotificationsAsRead: {\n        success: true,\n        readCount: validIds.length,\n        remainingCount: Math.max(0, this.notificationCount.value - validIds.length)\n      }\n    };\n    // Afficher des informations de débogage supplémentaires\n    console.log('Sending markNotificationsAsRead mutation with variables:', {\n      notificationIds: validIds\n    });\n    console.log('Using mutation:', MARK_NOTIFICATION_READ_MUTATION);\n    return this.apollo.mutate({\n      mutation: MARK_NOTIFICATION_READ_MUTATION,\n      variables: {\n        notificationIds: validIds\n      },\n      optimisticResponse: optimisticResponse,\n      errorPolicy: 'all',\n      fetchPolicy: 'no-cache' // Ne pas utiliser le cache pour cette mutation\n    }).pipe(map(result => {\n      this.logger.debug('MessageService', 'Mutation result', result);\n      console.log('Mutation result:', result);\n      // Si nous avons des erreurs GraphQL, les logger mais continuer\n      if (result.errors) {\n        this.logger.error('MessageService', 'GraphQL errors:', result.errors);\n        console.error('GraphQL errors:', result.errors);\n      }\n      // Utiliser la réponse du serveur ou notre réponse optimiste\n      const response = result.data?.markNotificationsAsRead ?? optimisticResponse.markNotificationsAsRead;\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error marking notifications as read:', error);\n      console.error('Error in markAsRead:', error);\n      // En cas d'erreur, retourner quand même un succès simulé\n      // puisque nous avons déjà mis à jour l'interface utilisateur\n      return of({\n        success: true,\n        readCount: validIds.length,\n        remainingCount: Math.max(0, this.notificationCount.value - validIds.length)\n      });\n    }));\n  }\n  // --------------------------------------------------------------------------\n  // Section 3: Méthodes pour les Appels\n  // --------------------------------------------------------------------------\n  /**\n   * Initie un appel avec un autre utilisateur\n   * @param recipientId ID de l'utilisateur à appeler\n   * @param callType Type d'appel (audio, vidéo)\n   * @param conversationId ID de la conversation (optionnel)\n   * @param options Options d'appel (optionnel)\n   * @returns Observable avec les informations de l'appel\n   */\n  initiateCall(recipientId, callType, conversationId, options) {\n    return this.setupMediaDevices(callType).pipe(switchMap(stream => {\n      this.localStream = stream;\n      this.localStream$.next(stream);\n      // Créer une connexion peer\n      this.peerConnection = new RTCPeerConnection(this.rtcConfig);\n      // Ajouter les pistes audio/vidéo\n      stream.getTracks().forEach(track => {\n        this.peerConnection.addTrack(track, stream);\n      });\n      // Écouter les candidats ICE\n      this.peerConnection.onicecandidate = event => {\n        if (event.candidate) {\n          this.sendCallSignal(this.generateCallId(), 'ice-candidate', JSON.stringify(event.candidate));\n        }\n      };\n      // Écouter les pistes distantes\n      this.peerConnection.ontrack = event => {\n        if (!this.remoteStream) {\n          this.remoteStream = new MediaStream();\n          this.remoteStream$.next(this.remoteStream);\n        }\n        event.streams[0].getTracks().forEach(track => {\n          this.remoteStream.addTrack(track);\n        });\n      };\n      // Créer l'offre SDP\n      return from(this.peerConnection.createOffer()).pipe(switchMap(offer => {\n        return from(this.peerConnection.setLocalDescription(offer)).pipe(map(() => offer));\n      }));\n    }), switchMap(offer => {\n      // Générer un ID d'appel unique\n      const callId = this.generateCallId();\n      // Envoyer l'offre au serveur\n      return this.apollo.mutate({\n        mutation: INITIATE_CALL_MUTATION,\n        variables: {\n          recipientId,\n          callType,\n          callId,\n          offer: JSON.stringify(offer),\n          conversationId,\n          options\n        }\n      }).pipe(map(result => {\n        const call = result.data?.initiateCall;\n        if (!call) {\n          throw new Error('Failed to initiate call');\n        }\n        // Mettre à jour l'état de l'appel actif\n        this.activeCall.next(call);\n        // S'abonner aux signaux d'appel\n        const signalSub = this.subscribeToCallSignals(call.id).subscribe();\n        this.subscriptions.push(signalSub);\n        return call;\n      }));\n    }), catchError(error => {\n      this.logger.error('Error initiating call', error);\n      this.cleanupCall();\n      return throwError(() => new Error('Failed to initiate call'));\n    }));\n  }\n  /**\n   * Accepte un appel entrant\n   * @param incomingCall Appel entrant à accepter\n   * @returns Observable avec les informations de l'appel\n   */\n  acceptCall(incomingCall) {\n    this.stop('ringtone');\n    return this.setupMediaDevices(incomingCall.type).pipe(switchMap(stream => {\n      this.localStream = stream;\n      this.localStream$.next(stream);\n      // Créer une connexion peer\n      this.peerConnection = new RTCPeerConnection(this.rtcConfig);\n      // Ajouter les pistes audio/vidéo\n      stream.getTracks().forEach(track => {\n        this.peerConnection.addTrack(track, stream);\n      });\n      // Écouter les candidats ICE\n      this.peerConnection.onicecandidate = event => {\n        if (event.candidate) {\n          this.sendCallSignal(incomingCall.id, 'ice-candidate', JSON.stringify(event.candidate));\n        }\n      };\n      // Écouter les pistes distantes\n      this.peerConnection.ontrack = event => {\n        if (!this.remoteStream) {\n          this.remoteStream = new MediaStream();\n          this.remoteStream$.next(this.remoteStream);\n        }\n        event.streams[0].getTracks().forEach(track => {\n          this.remoteStream.addTrack(track);\n        });\n      };\n      // Définir l'offre distante\n      const offer = JSON.parse(incomingCall.offer);\n      return from(this.peerConnection.setRemoteDescription(new RTCSessionDescription(offer))).pipe(switchMap(() => from(this.peerConnection.createAnswer())), switchMap(answer => {\n        return from(this.peerConnection.setLocalDescription(answer)).pipe(map(() => answer));\n      }));\n    }), switchMap(answer => {\n      // Envoyer la réponse au serveur\n      return this.apollo.mutate({\n        mutation: ACCEPT_CALL_MUTATION,\n        variables: {\n          callId: incomingCall.id,\n          answer: JSON.stringify(answer)\n        }\n      }).pipe(map(result => {\n        const call = result.data?.acceptCall;\n        if (!call) {\n          throw new Error('Failed to accept call');\n        }\n        // Jouer le son de connexion\n        this.play('call-connected');\n        // Mettre à jour l'état de l'appel actif\n        this.activeCall.next({\n          ...call,\n          caller: incomingCall.caller,\n          type: incomingCall.type,\n          conversationId: incomingCall.conversationId\n        });\n        // S'abonner aux signaux d'appel\n        const signalSub = this.subscribeToCallSignals(incomingCall.id).subscribe();\n        this.subscriptions.push(signalSub);\n        // Effacer l'appel entrant\n        this.incomingCall.next(null);\n        return call;\n      }));\n    }), catchError(error => {\n      this.logger.error('Error accepting call', error);\n      this.cleanupCall();\n      return throwError(() => new Error('Failed to accept call'));\n    }));\n  }\n  /**\n   * Rejette un appel entrant\n   * @param callId ID de l'appel à rejeter\n   * @param reason Raison du rejet (optionnel)\n   * @returns Observable avec les informations de l'appel\n   */\n  rejectCall(callId, reason) {\n    this.stop('ringtone');\n    return this.apollo.mutate({\n      mutation: REJECT_CALL_MUTATION,\n      variables: {\n        callId,\n        reason\n      }\n    }).pipe(map(result => {\n      const call = result.data?.rejectCall;\n      if (!call) {\n        throw new Error('Failed to reject call');\n      }\n      // Effacer l'appel entrant\n      this.incomingCall.next(null);\n      return call;\n    }), catchError(error => {\n      this.logger.error('Error rejecting call', error);\n      return throwError(() => new Error('Failed to reject call'));\n    }));\n  }\n  /**\n   * Termine un appel en cours\n   * @param callId ID de l'appel à terminer\n   * @param feedback Commentaires sur l'appel (optionnel)\n   * @returns Observable avec les informations de l'appel\n   */\n  endCall(callId, feedback) {\n    this.stop('ringtone');\n    this.play('call-end');\n    return this.apollo.mutate({\n      mutation: END_CALL_MUTATION,\n      variables: {\n        callId,\n        feedback\n      }\n    }).pipe(map(result => {\n      const call = result.data?.endCall;\n      if (!call) {\n        throw new Error('Failed to end call');\n      }\n      // Nettoyer les ressources\n      this.cleanupCall();\n      // Mettre à jour l'état de l'appel actif\n      this.activeCall.next(null);\n      return call;\n    }), catchError(error => {\n      this.logger.error('Error ending call', error);\n      this.cleanupCall();\n      return throwError(() => new Error('Failed to end call'));\n    }));\n  }\n  /**\n   * Active ou désactive la caméra ou le micro\n   * @param callId ID de l'appel\n   * @param video État de la caméra (optionnel)\n   * @param audio État du micro (optionnel)\n   * @returns Observable avec le résultat de l'opération\n   */\n  toggleMedia(callId, video, audio) {\n    if (this.localStream) {\n      // Mettre à jour les pistes locales\n      if (video !== undefined) {\n        this.localStream.getVideoTracks().forEach(track => {\n          track.enabled = video;\n        });\n      }\n      if (audio !== undefined) {\n        this.localStream.getAudioTracks().forEach(track => {\n          track.enabled = audio;\n        });\n      }\n    }\n    return this.apollo.mutate({\n      mutation: TOGGLE_CALL_MEDIA_MUTATION,\n      variables: {\n        callId,\n        video,\n        audio\n      }\n    }).pipe(map(result => {\n      const success = result.data?.toggleCallMedia;\n      if (!success) {\n        throw new Error('Failed to toggle media');\n      }\n      return success;\n    }), catchError(error => {\n      this.logger.error('Error toggling media', error);\n      return throwError(() => new Error('Failed to toggle media'));\n    }));\n  }\n  /**\n   * S'abonne aux signaux d'appel\n   * @param callId ID de l'appel\n   * @returns Observable avec les signaux d'appel\n   */\n  subscribeToCallSignals(callId) {\n    return this.apollo.subscribe({\n      query: CALL_SIGNAL_SUBSCRIPTION,\n      variables: {\n        callId\n      }\n    }).pipe(map(({\n      data\n    }) => {\n      if (!data?.callSignal) {\n        throw new Error('No call signal received');\n      }\n      return data.callSignal;\n    }), tap(signal => {\n      this.callSignals.next(signal);\n      this.handleCallSignal(signal);\n    }), catchError(error => {\n      this.logger.error('Error in call signal subscription', error);\n      return throwError(() => new Error('Call signal subscription failed'));\n    }));\n  }\n  /**\n   * Envoie un signal d'appel\n   * @param callId ID de l'appel\n   * @param signalType Type de signal\n   * @param signalData Données du signal\n   * @returns Observable avec le résultat de l'opération\n   */\n  sendCallSignal(callId, signalType, signalData) {\n    return this.apollo.mutate({\n      mutation: SEND_CALL_SIGNAL_MUTATION,\n      variables: {\n        callId,\n        signalType,\n        signalData\n      }\n    }).pipe(map(result => {\n      const success = result.data?.sendCallSignal;\n      if (!success) {\n        throw new Error('Failed to send call signal');\n      }\n      return success;\n    }), catchError(error => {\n      this.logger.error('Error sending call signal', error);\n      return throwError(() => new Error('Failed to send call signal'));\n    }));\n  }\n  /**\n   * Récupère l'historique des appels avec filtres\n   * @param limit Nombre d'appels à récupérer\n   * @param offset Décalage pour la pagination\n   * @param status Filtres de statut\n   * @param type Filtres de type\n   * @param startDate Date de début\n   * @param endDate Date de fin\n   * @returns Observable avec l'historique des appels\n   */\n  getCallHistory(limit = 20, offset = 0, status, type, startDate, endDate) {\n    return this.apollo.watchQuery({\n      query: CALL_HISTORY_QUERY,\n      variables: {\n        limit,\n        offset,\n        status,\n        type,\n        startDate,\n        endDate\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const history = result.data?.callHistory || [];\n      this.logger.debug(`Retrieved ${history.length} call history items`);\n      return history;\n    }), catchError(error => {\n      this.logger.error('Error fetching call history:', error);\n      return throwError(() => new Error('Failed to fetch call history'));\n    }));\n  }\n  /**\n   * Récupère les détails d'un appel spécifique\n   * @param callId ID de l'appel\n   * @returns Observable avec les détails de l'appel\n   */\n  getCallDetails(callId) {\n    return this.apollo.watchQuery({\n      query: CALL_DETAILS_QUERY,\n      variables: {\n        callId\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const details = result.data?.callDetails;\n      if (!details) {\n        throw new Error('Call details not found');\n      }\n      this.logger.debug(`Retrieved call details for: ${callId}`);\n      return details;\n    }), catchError(error => {\n      this.logger.error('Error fetching call details:', error);\n      return throwError(() => new Error('Failed to fetch call details'));\n    }));\n  }\n  /**\n   * Récupère les statistiques d'appels\n   * @returns Observable avec les statistiques d'appels\n   */\n  getCallStats() {\n    return this.apollo.watchQuery({\n      query: CALL_STATS_QUERY,\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const stats = result.data?.callStats;\n      if (!stats) {\n        throw new Error('Call stats not found');\n      }\n      this.logger.debug('Retrieved call stats:', stats);\n      return stats;\n    }), catchError(error => {\n      this.logger.error('Error fetching call stats:', error);\n      return throwError(() => new Error('Failed to fetch call stats'));\n    }));\n  }\n  /**\n   * Gère un signal d'appel reçu\n   * @param signal Signal d'appel\n   */\n  handleCallSignal(signal) {\n    switch (signal.type) {\n      case 'ice-candidate':\n        this.handleIceCandidate(signal);\n        break;\n      case 'answer':\n        this.handleAnswer(signal);\n        break;\n      case 'end-call':\n        this.handleEndCall(signal);\n        break;\n      case 'reject':\n        this.handleRejectCall(signal);\n        break;\n      default:\n        this.logger.debug(`Unhandled signal type: ${signal.type}`, signal);\n    }\n  }\n  /**\n   * Gère un candidat ICE reçu\n   * @param signal Signal d'appel contenant un candidat ICE\n   */\n  handleIceCandidate(signal) {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for ICE candidate');\n      return;\n    }\n    try {\n      const candidate = JSON.parse(signal.data);\n      this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate)).catch(error => {\n        this.logger.error('Error adding ICE candidate', error);\n      });\n    } catch (error) {\n      this.logger.error('Error parsing ICE candidate', error);\n    }\n  }\n  /**\n   * Gère une réponse SDP reçue\n   * @param signal Signal d'appel contenant une réponse SDP\n   */\n  handleAnswer(signal) {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for answer');\n      return;\n    }\n    try {\n      const answer = JSON.parse(signal.data);\n      this.peerConnection.setRemoteDescription(new RTCSessionDescription(answer)).catch(error => {\n        this.logger.error('Error setting remote description', error);\n      });\n    } catch (error) {\n      this.logger.error('Error parsing answer', error);\n    }\n  }\n  /**\n   * Gère la fin d'un appel\n   * @param signal Signal d'appel indiquant la fin de l'appel\n   */\n  handleEndCall(signal) {\n    this.stop('ringtone');\n    this.cleanupCall();\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.ENDED,\n        endTime: new Date().toISOString()\n      });\n    }\n  }\n  /**\n   * Gère le rejet d'un appel\n   * @param signal Signal d'appel indiquant le rejet de l'appel\n   */\n  handleRejectCall(signal) {\n    this.stop('ringtone');\n    this.cleanupCall();\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.REJECTED,\n        endTime: new Date().toISOString()\n      });\n    }\n  }\n  /**\n   * Nettoie les ressources d'appel\n   */\n  cleanupCall() {\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => track.stop());\n      this.localStream = null;\n      this.localStream$.next(null);\n    }\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n    this.remoteStream = null;\n    this.remoteStream$.next(null);\n  }\n  /**\n   * Configure les périphériques média pour un appel\n   * @param callType Type d'appel (audio, vidéo)\n   * @returns Observable avec le flux média\n   */\n  setupMediaDevices(callType) {\n    const constraints = {\n      audio: true,\n      video: callType !== CallType.AUDIO ? {\n        width: {\n          ideal: 1280\n        },\n        height: {\n          ideal: 720\n        }\n      } : false\n    };\n    return new Observable(observer => {\n      navigator.mediaDevices.getUserMedia(constraints).then(stream => {\n        observer.next(stream);\n        observer.complete();\n      }).catch(error => {\n        this.logger.error('Error accessing media devices', error);\n        observer.error(new Error('Failed to access media devices'));\n      });\n    });\n  }\n  /**\n   * Génère un ID d'appel unique\n   * @returns ID d'appel unique\n   */\n  generateCallId() {\n    return Date.now().toString() + Math.random().toString(36).substring(2, 9);\n  }\n  // --------------------------------------------------------------------------\n  // Section 4: Méthodes pour les Utilisateurs/Groupes\n  // --------------------------------------------------------------------------\n  // User methods\n  getAllUsers(forceRefresh = false, search, page = 1, limit = 10, sortBy = 'username', sortOrder = 'asc', isOnline) {\n    this.logger.info('MessageService', `Getting users with params: forceRefresh=${forceRefresh}, search=${search || '(empty)'}, page=${page}, limit=${limit}, sortBy=${sortBy}, sortOrder=${sortOrder}, isOnline=${isOnline}`);\n    const now = Date.now();\n    const cacheValid = !forceRefresh && this.usersCache.length > 0 && now - this.lastFetchTime <= this.CACHE_DURATION && !search && page === 1 && limit >= this.usersCache.length;\n    // Use cache only for first page with no filters\n    if (cacheValid) {\n      this.logger.debug('MessageService', `Using cached users (${this.usersCache.length} users)`);\n      return of([...this.usersCache]);\n    }\n    this.logger.debug('MessageService', `Fetching users from server with pagination, fetchPolicy=${forceRefresh ? 'network-only' : 'cache-first'}`);\n    return this.apollo.watchQuery({\n      query: GET_ALL_USER_QUERY,\n      variables: {\n        search,\n        page,\n        limit,\n        sortBy,\n        sortOrder,\n        isOnline: isOnline !== undefined ? isOnline : null\n      },\n      fetchPolicy: forceRefresh ? 'network-only' : 'cache-first'\n    }).valueChanges.pipe(map(result => {\n      this.logger.debug('MessageService', 'Users response received', result);\n      if (result.errors) {\n        this.logger.error('MessageService', 'GraphQL errors in getAllUsers:', result.errors);\n        throw new Error(result.errors.map(e => e.message).join(', '));\n      }\n      if (!result.data?.getAllUsers) {\n        this.logger.warn('MessageService', 'No users data received from server');\n        return [];\n      }\n      const paginatedResponse = result.data.getAllUsers;\n      // Log pagination metadata\n      this.logger.debug('MessageService', 'Pagination metadata:', {\n        totalCount: paginatedResponse.totalCount,\n        totalPages: paginatedResponse.totalPages,\n        currentPage: paginatedResponse.currentPage,\n        hasNextPage: paginatedResponse.hasNextPage,\n        hasPreviousPage: paginatedResponse.hasPreviousPage\n      });\n      // Normalize users with error handling\n      const users = [];\n      for (const user of paginatedResponse.users) {\n        try {\n          if (user) {\n            users.push(this.normalizeUser(user));\n          }\n        } catch (error) {\n          this.logger.warn('MessageService', `Error normalizing user, skipping:`, error);\n        }\n      }\n      this.logger.info('MessageService', `Received ${users.length} users from server (page ${paginatedResponse.currentPage} of ${paginatedResponse.totalPages})`);\n      // Update cache only for first page with no filters\n      if (!search && page === 1 && !isOnline) {\n        this.usersCache = [...users];\n        this.lastFetchTime = Date.now();\n        this.logger.debug('MessageService', `User cache updated with ${users.length} users`);\n      }\n      // Store pagination metadata in a property for component access\n      this.currentUserPagination = {\n        totalCount: paginatedResponse.totalCount,\n        totalPages: paginatedResponse.totalPages,\n        currentPage: paginatedResponse.currentPage,\n        hasNextPage: paginatedResponse.hasNextPage,\n        hasPreviousPage: paginatedResponse.hasPreviousPage\n      };\n      return users;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error fetching users:', error);\n      if (error.graphQLErrors) {\n        this.logger.error('MessageService', 'GraphQL errors:', error.graphQLErrors);\n      }\n      if (error.networkError) {\n        this.logger.error('MessageService', 'Network error:', error.networkError);\n      }\n      // Return cache if available (only for first page)\n      if (this.usersCache.length > 0 && page === 1 && !search && !isOnline) {\n        this.logger.warn('MessageService', `Returning ${this.usersCache.length} cached users due to fetch error`);\n        return of([...this.usersCache]);\n      }\n      return throwError(() => new Error(`Failed to fetch users: ${error.message || 'Unknown error'}`));\n    }));\n  }\n  getOneUser(userId) {\n    return this.apollo.watchQuery({\n      query: GET_USER_QUERY,\n      variables: {\n        id: userId\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => this.normalizeUser(result.data?.getOneUser)), catchError(error => {\n      this.logger.error('MessageService', 'Error fetching user:', error);\n      return throwError(() => new Error('Failed to fetch user'));\n    }));\n  }\n  getCurrentUser() {\n    return this.apollo.watchQuery({\n      query: GET_CURRENT_USER_QUERY,\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => this.normalizeUser(result.data?.getCurrentUser)), catchError(error => {\n      this.logger.error('MessageService', 'Error fetching current user:', error);\n      return throwError(() => new Error('Failed to fetch current user'));\n    }));\n  }\n  setUserOnline(userId) {\n    return this.apollo.mutate({\n      mutation: SET_USER_ONLINE_MUTATION,\n      variables: {\n        userId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.setUserOnline) throw new Error('Failed to set user online');\n      return this.normalizeUser(result.data.setUserOnline);\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error setting user online:', error);\n      return throwError(() => new Error('Failed to set user online'));\n    }));\n  }\n  setUserOffline(userId) {\n    return this.apollo.mutate({\n      mutation: SET_USER_OFFLINE_MUTATION,\n      variables: {\n        userId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.setUserOffline) throw new Error('Failed to set user offline');\n      return this.normalizeUser(result.data.setUserOffline);\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error setting user offline:', error);\n      return throwError(() => new Error('Failed to set user offline'));\n    }));\n  }\n  // --------------------------------------------------------------------------\n  // Section: Gestion des Groupes\n  // --------------------------------------------------------------------------\n  /**\n   * Crée un nouveau groupe\n   */\n  createGroup(name, participantIds, photo, description) {\n    this.logger.debug('MessageService', `Creating group: ${name} with ${participantIds.length} participants`);\n    if (!name || !participantIds || participantIds.length === 0) {\n      return throwError(() => new Error('Nom du groupe et participants requis'));\n    }\n    return this.apollo.mutate({\n      mutation: CREATE_GROUP_MUTATION,\n      variables: {\n        name,\n        participantIds,\n        photo,\n        description\n      }\n    }).pipe(map(result => {\n      const group = result.data?.createGroup;\n      if (!group) {\n        throw new Error('Échec de la création du groupe');\n      }\n      this.logger.info('MessageService', `Group created successfully: ${group.id}`);\n      return group;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error creating group:', error);\n      return throwError(() => new Error('Échec de la création du groupe'));\n    }));\n  }\n  /**\n   * Met à jour un groupe existant\n   */\n  updateGroup(groupId, input) {\n    this.logger.debug('MessageService', `Updating group: ${groupId}`);\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n    return this.apollo.mutate({\n      mutation: UPDATE_GROUP_MUTATION,\n      variables: {\n        id: groupId,\n        input\n      }\n    }).pipe(map(result => {\n      const group = result.data?.updateGroup;\n      if (!group) {\n        throw new Error('Échec de la mise à jour du groupe');\n      }\n      this.logger.info('MessageService', `Group updated successfully: ${group.id}`);\n      return group;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error updating group:', error);\n      return throwError(() => new Error('Échec de la mise à jour du groupe'));\n    }));\n  }\n  /**\n   * Supprime un groupe\n   */\n  deleteGroup(groupId) {\n    this.logger.debug('MessageService', `Deleting group: ${groupId}`);\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n    return this.apollo.mutate({\n      mutation: DELETE_GROUP_MUTATION,\n      variables: {\n        id: groupId\n      }\n    }).pipe(map(result => {\n      const response = result.data?.deleteGroup;\n      if (!response) {\n        throw new Error('Échec de la suppression du groupe');\n      }\n      this.logger.info('MessageService', `Group deleted successfully: ${groupId}`);\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error deleting group:', error);\n      return throwError(() => new Error('Échec de la suppression du groupe'));\n    }));\n  }\n  /**\n   * Quitte un groupe\n   */\n  leaveGroup(groupId) {\n    this.logger.debug('MessageService', `Leaving group: ${groupId}`);\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n    return this.apollo.mutate({\n      mutation: LEAVE_GROUP_MUTATION,\n      variables: {\n        groupId\n      }\n    }).pipe(map(result => {\n      const response = result.data?.leaveGroup;\n      if (!response) {\n        throw new Error('Échec de la sortie du groupe');\n      }\n      this.logger.info('MessageService', `Left group successfully: ${groupId}`);\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error leaving group:', error);\n      return throwError(() => new Error('Échec de la sortie du groupe'));\n    }));\n  }\n  /**\n   * Récupère les informations d'un groupe\n   */\n  getGroup(groupId) {\n    this.logger.debug('MessageService', `Getting group: ${groupId}`);\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n    return this.apollo.query({\n      query: GET_GROUP_QUERY,\n      variables: {\n        id: groupId\n      },\n      fetchPolicy: 'network-only'\n    }).pipe(map(result => {\n      const group = result.data?.getGroup;\n      if (!group) {\n        throw new Error('Groupe non trouvé');\n      }\n      this.logger.info('MessageService', `Group retrieved successfully: ${groupId}`);\n      return group;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error getting group:', error);\n      return throwError(() => new Error('Échec de la récupération du groupe'));\n    }));\n  }\n  /**\n   * Récupère les groupes d'un utilisateur\n   */\n  getUserGroups(userId) {\n    this.logger.debug('MessageService', `Getting groups for user: ${userId}`);\n    if (!userId) {\n      return throwError(() => new Error(\"ID de l'utilisateur requis\"));\n    }\n    return this.apollo.query({\n      query: GET_USER_GROUPS_QUERY,\n      variables: {\n        userId\n      },\n      fetchPolicy: 'network-only'\n    }).pipe(map(result => {\n      const groups = result.data?.getUserGroups || [];\n      this.logger.info('MessageService', `Retrieved ${groups.length} groups for user: ${userId}`);\n      return groups;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error getting user groups:', error);\n      return throwError(() => new Error('Échec de la récupération des groupes'));\n    }));\n  }\n  // --------------------------------------------------------------------------\n  // Section 4: Subscriptions et Gestion Temps Réel\n  // --------------------------------------------------------------------------\n  subscribeToNewMessages(conversationId) {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\"Tentative d'abonnement aux messages avec un token invalide ou expiré\");\n      return of(null);\n    }\n    this.logger.debug(`Démarrage de l'abonnement aux nouveaux messages pour la conversation: ${conversationId}`);\n    const sub$ = this.apollo.subscribe({\n      query: MESSAGE_SENT_SUBSCRIPTION,\n      variables: {\n        conversationId\n      }\n    }).pipe(map(result => {\n      const msg = result.data?.messageSent;\n      if (!msg) {\n        this.logger.warn('No message payload received');\n        throw new Error('No message payload received');\n      }\n      // Vérifier que l'ID est présent\n      if (!msg.id && !msg._id) {\n        this.logger.warn('Message without ID received:', msg);\n        // Générer un ID temporaire si nécessaire\n        msg.id = `temp-${Date.now()}`;\n      }\n      try {\n        // Utiliser normalizeMessage pour une normalisation complète\n        const normalizedMessage = this.normalizeMessage(msg);\n        // Si c'est un message vocal, s'assurer qu'il est correctement traité\n        if (normalizedMessage.type === MessageType.AUDIO || normalizedMessage.attachments && normalizedMessage.attachments.some(att => att.type === 'audio')) {\n          this.logger.debug('MessageService', 'Voice message received in real-time', normalizedMessage);\n          // Mettre à jour la conversation avec le nouveau message\n          this.updateConversationWithNewMessage(conversationId, normalizedMessage);\n        }\n        return normalizedMessage;\n      } catch (err) {\n        this.logger.error('Error normalizing message:', err);\n        // Créer un message minimal mais valide\n        const minimalMessage = {\n          id: msg.id || msg._id || `temp-${Date.now()}`,\n          content: msg.content || '',\n          type: msg.type || MessageType.TEXT,\n          timestamp: this.safeDate(msg.timestamp),\n          isRead: false,\n          sender: msg.sender ? this.normalizeUser(msg.sender) : {\n            id: this.getCurrentUserId(),\n            username: 'Unknown'\n          }\n        };\n        return minimalMessage;\n      }\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Message subscription error:', error);\n      // Retourner un observable vide au lieu de null\n      return EMPTY;\n    }),\n    // Filtrer les valeurs null\n    filter(message => !!message),\n    // Réessayer après un délai en cas d'erreur\n    retry(3));\n    const sub = sub$.subscribe({\n      next: message => {\n        // Traitement supplémentaire pour s'assurer que le message est bien affiché\n        this.logger.debug('MessageService', 'New message received:', message);\n        // Mettre à jour la conversation avec le nouveau message\n        this.updateConversationWithNewMessage(conversationId, message);\n      },\n      error: err => {\n        this.logger.error('Error in message subscription:', err);\n      }\n    });\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  /**\n   * Met à jour une conversation avec un nouveau message\n   * @param conversationId ID de la conversation\n   * @param message Nouveau message\n   */\n  updateConversationWithNewMessage(conversationId, message) {\n    // Forcer une mise à jour de la conversation en récupérant les données à jour\n    this.getConversation(conversationId).subscribe({\n      next: conversation => {\n        this.logger.debug('MessageService', `Conversation ${conversationId} refreshed with new message ${message.id}, has ${conversation?.messages?.length || 0} messages`);\n        // Émettre un événement pour informer les composants que la conversation a été mise à jour\n        this.activeConversation.next(conversationId);\n      },\n      error: error => {\n        this.logger.error('MessageService', `Error refreshing conversation ${conversationId}:`, error);\n      }\n    });\n  }\n  subscribeToUserStatus() {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\"Tentative d'abonnement au statut utilisateur avec un token invalide ou expiré\");\n      return throwError(() => new Error('Invalid or expired token'));\n    }\n    this.logger.debug(\"Démarrage de l'abonnement au statut utilisateur\");\n    const sub$ = this.apollo.subscribe({\n      query: USER_STATUS_SUBSCRIPTION\n    }).pipe(tap(result => this.logger.debug(\"Données reçues de l'abonnement au statut utilisateur:\", result)), map(result => {\n      const user = result.data?.userStatusChanged;\n      if (!user) {\n        this.logger.error('No status payload received');\n        throw new Error('No status payload received');\n      }\n      return this.normalizeUser(user);\n    }), catchError(error => {\n      this.logger.error('Status subscription error:', error);\n      return throwError(() => new Error('Status subscription failed'));\n    }), retry(3) // Réessayer 3 fois en cas d'erreur\n    );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToConversationUpdates(conversationId) {\n    const sub$ = this.apollo.subscribe({\n      query: CONVERSATION_UPDATED_SUBSCRIPTION,\n      variables: {\n        conversationId\n      }\n    }).pipe(map(result => {\n      const conv = result.data?.conversationUpdated;\n      if (!conv) throw new Error('No conversation payload received');\n      const normalizedConversation = {\n        ...conv,\n        participants: conv.participants?.map(p => this.normalizeUser(p)) || [],\n        lastMessage: conv.lastMessage ? {\n          ...conv.lastMessage,\n          sender: this.normalizeUser(conv.lastMessage.sender),\n          timestamp: this.safeDate(conv.lastMessage.timestamp),\n          readAt: conv.lastMessage.readAt ? this.safeDate(conv.lastMessage.readAt) : undefined,\n          // Conservez toutes les autres propriétés du message\n          id: conv.lastMessage.id,\n          content: conv.lastMessage.content,\n          type: conv.lastMessage.type,\n          isRead: conv.lastMessage.isRead\n          // ... autres propriétés nécessaires\n        } : null // On conserve null comme dans votre version originale\n      };\n\n      return normalizedConversation; // Assertion de type si nécessaire\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Conversation subscription error:', error);\n      return throwError(() => new Error('Conversation subscription failed'));\n    }));\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToTypingIndicator(conversationId) {\n    const sub$ = this.apollo.subscribe({\n      query: TYPING_INDICATOR_SUBSCRIPTION,\n      variables: {\n        conversationId\n      }\n    }).pipe(map(result => result.data?.typingIndicator), filter(Boolean), catchError(error => {\n      this.logger.error('MessageService', 'Typing indicator subscription error:', error);\n      return throwError(() => new Error('Typing indicator subscription failed'));\n    }));\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  isTokenValid() {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn('Aucun token trouvé');\n      return false;\n    }\n    try {\n      // Décoder le token JWT (format: header.payload.signature)\n      const parts = token.split('.');\n      if (parts.length !== 3) {\n        this.logger.warn('Format de token invalide');\n        return false;\n      }\n      // Décoder le payload (deuxième partie du token)\n      const payload = JSON.parse(atob(parts[1]));\n      // Vérifier l'expiration\n      if (!payload.exp) {\n        this.logger.warn(\"Token sans date d'expiration\");\n        return false;\n      }\n      const expirationDate = new Date(payload.exp * 1000);\n      const now = new Date();\n      if (expirationDate < now) {\n        this.logger.warn('Token expiré', {\n          expiration: expirationDate.toISOString(),\n          now: now.toISOString()\n        });\n        return false;\n      }\n      return true;\n    } catch (error) {\n      this.logger.error('Erreur lors de la vérification du token:', error);\n      return false;\n    }\n  }\n  subscribeToNotificationsRead() {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\"Tentative d'abonnement aux notifications avec un token invalide ou expiré\");\n      return of([]);\n    }\n    this.logger.debug(\"Démarrage de l'abonnement aux notifications lues\");\n    const sub$ = this.apollo.subscribe({\n      query: NOTIFICATIONS_READ_SUBSCRIPTION\n    }).pipe(tap(result => this.logger.debug(\"Données reçues de l'abonnement aux notifications lues:\", result)), map(result => {\n      const notificationIds = result.data?.notificationsRead || [];\n      this.logger.debug('Notifications marquées comme lues:', notificationIds);\n      this.updateNotificationStatus(notificationIds, true);\n      return notificationIds;\n    }), catchError(err => {\n      this.logger.error('Notifications read subscription error:', err);\n      // Retourner un tableau vide au lieu de propager l'erreur\n      return of([]);\n    }),\n    // Réessayer après un délai en cas d'erreur\n    retry(3) // Réessayer 3 fois en cas d'erreur\n    );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToNewNotifications() {\n    // Vérifier si l'utilisateur est connecté\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn(\"Tentative d'abonnement aux notifications sans être connecté\");\n      // Créer un Observable vide plutôt que de retourner null\n      return EMPTY;\n    }\n    const source$ = this.apollo.subscribe({\n      query: NOTIFICATION_SUBSCRIPTION\n    });\n    const processed$ = source$.pipe(map(result => {\n      const notification = result.data?.notificationReceived;\n      if (!notification) {\n        throw new Error('No notification payload received');\n      }\n      const normalized = this.normalizeNotification(notification);\n      // Vérifier si cette notification existe déjà dans le cache\n      if (this.notificationCache.has(normalized.id)) {\n        this.logger.debug('MessageService', `Notification ${normalized.id} already exists in cache, skipping`);\n        // Utiliser une technique différente pour ignorer cette notification\n        throw new Error('Notification already exists in cache');\n      }\n      // Jouer le son de notification\n      this.playNotificationSound();\n      // Mettre à jour le cache et émettre immédiatement la nouvelle notification\n      this.updateNotificationCache(normalized);\n      this.logger.debug('MessageService', 'New notification received and processed', normalized);\n      return normalized;\n    }),\n    // Utiliser catchError pour gérer les erreurs spécifiques\n    catchError(err => {\n      // Si c'est l'erreur spécifique pour les notifications déjà existantes, on ignore silencieusement\n      if (err instanceof Error && err.message === 'Notification already exists in cache') {\n        return EMPTY;\n      }\n      this.logger.error('New notification subscription error:', err);\n      // Retourner un Observable vide au lieu de null\n      return EMPTY;\n    }));\n    const sub = processed$.subscribe({\n      next: notification => {\n        this.logger.debug('MessageService', 'Notification subscription next handler', notification);\n      },\n      error: error => {\n        this.logger.error('MessageService', 'Error in notification subscription', error);\n      }\n    });\n    this.subscriptions.push(sub);\n    return processed$;\n  }\n  // --------------------------------------------------------------------------\n  // Helpers et Utilitaires\n  // --------------------------------------------------------------------------\n  startCleanupInterval() {\n    this.cleanupInterval = setInterval(() => {\n      this.cleanupExpiredNotifications();\n    }, 3600000);\n  }\n  cleanupExpiredNotifications() {\n    const now = new Date();\n    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n    let expiredCount = 0;\n    this.notificationCache.forEach((notification, id) => {\n      const notificationDate = new Date(notification.timestamp);\n      if (notificationDate < thirtyDaysAgo) {\n        this.notificationCache.delete(id);\n        expiredCount++;\n      }\n    });\n    if (expiredCount > 0) {\n      this.logger.debug(`Cleaned up ${expiredCount} expired notifications`);\n      this.notifications.next(Array.from(this.notificationCache.values()));\n      this.updateUnreadCount();\n    }\n  }\n  getCurrentUserId() {\n    return localStorage.getItem('userId') || '';\n  }\n  normalizeMessage(message) {\n    if (!message) {\n      this.logger.error('[MessageService] Cannot normalize null or undefined message');\n      throw new Error('Message object is required');\n    }\n    try {\n      // Vérification des champs obligatoires\n      if (!message.id && !message._id) {\n        this.logger.error('[MessageService] Message ID is missing', undefined, message);\n        throw new Error('Message ID is required');\n      }\n      // Normaliser le sender avec gestion d'erreur\n      let normalizedSender;\n      try {\n        normalizedSender = message.sender ? this.normalizeUser(message.sender) : undefined;\n      } catch (error) {\n        this.logger.warn('[MessageService] Error normalizing message sender, using default values', error);\n        normalizedSender = {\n          _id: message.senderId || 'unknown',\n          id: message.senderId || 'unknown',\n          username: 'Unknown User',\n          email: '<EMAIL>',\n          role: 'user',\n          isActive: true\n        };\n      }\n      // Normaliser le receiver si présent\n      let normalizedReceiver;\n      if (message.receiver) {\n        try {\n          normalizedReceiver = this.normalizeUser(message.receiver);\n        } catch (error) {\n          this.logger.warn('[MessageService] Error normalizing message receiver, using default values', error);\n          normalizedReceiver = {\n            _id: message.receiverId || 'unknown',\n            id: message.receiverId || 'unknown',\n            username: 'Unknown User',\n            email: '<EMAIL>',\n            role: 'user',\n            isActive: true\n          };\n        }\n      }\n      // Normaliser les pièces jointes si présentes\n      const normalizedAttachments = message.attachments?.map(att => ({\n        id: att.id || att._id || `attachment-${Date.now()}`,\n        url: att.url || '',\n        type: att.type || 'unknown',\n        name: att.name || 'attachment',\n        size: att.size || 0,\n        duration: att.duration || 0\n      })) || [];\n      // Construire le message normalisé\n      const normalizedMessage = {\n        ...message,\n        _id: message.id || message._id,\n        id: message.id || message._id,\n        content: message.content || '',\n        sender: normalizedSender,\n        timestamp: this.normalizeDate(message.timestamp),\n        readAt: message.readAt ? this.normalizeDate(message.readAt) : undefined,\n        attachments: normalizedAttachments,\n        metadata: message.metadata || null\n      };\n      // Ajouter le receiver seulement s'il existe\n      if (normalizedReceiver) {\n        normalizedMessage.receiver = normalizedReceiver;\n      }\n      this.logger.debug('[MessageService] Message normalized successfully', {\n        messageId: normalizedMessage.id,\n        senderId: normalizedMessage.sender?.id\n      });\n      return normalizedMessage;\n    } catch (error) {\n      this.logger.error('[MessageService] Error normalizing message:', error instanceof Error ? error : new Error(String(error)), message);\n      throw new Error(`Failed to normalize message: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n  normalizeNotMessage(message) {\n    return {\n      ...message,\n      ...(message.attachments && {\n        attachments: message.attachments.map(att => ({\n          url: att.url,\n          type: att.type,\n          ...(att.name && {\n            name: att.name\n          }),\n          ...(att.size && {\n            size: att.size\n          })\n        }))\n      })\n    };\n  }\n  normalizeUser(user) {\n    if (!user) {\n      throw new Error('User object is required');\n    }\n    // Vérification des champs obligatoires avec valeurs par défaut\n    const userId = user.id || user._id;\n    if (!userId) {\n      throw new Error('User ID is required');\n    }\n    // Utiliser des valeurs par défaut pour les champs manquants\n    const username = user.username || 'Unknown User';\n    const email = user.email || `user-${userId}@example.com`;\n    const isActive = user.isActive !== undefined && user.isActive !== null ? user.isActive : true;\n    const role = user.role || 'user';\n    // Construire l'objet utilisateur normalisé\n    return {\n      _id: userId,\n      id: userId,\n      username: username,\n      email: email,\n      role: role,\n      isActive: isActive,\n      // Champs optionnels\n      image: user.image ?? null,\n      bio: user.bio,\n      isOnline: user.isOnline || false,\n      lastActive: user.lastActive ? new Date(user.lastActive) : undefined,\n      createdAt: user.createdAt ? new Date(user.createdAt) : undefined,\n      updatedAt: user.updatedAt ? new Date(user.updatedAt) : undefined,\n      followingCount: user.followingCount,\n      followersCount: user.followersCount,\n      postCount: user.postCount\n    };\n  }\n  normalizeConversation(conv) {\n    if (!conv) {\n      this.logger.error('[MessageService] Cannot normalize null or undefined conversation');\n      throw new Error('Conversation object is required');\n    }\n    try {\n      // Vérification des champs obligatoires\n      if (!conv.id && !conv._id) {\n        this.logger.error('[MessageService] Conversation ID is missing', undefined, conv);\n        throw new Error('Conversation ID is required');\n      }\n      // Normaliser les participants avec gestion d'erreur\n      const normalizedParticipants = [];\n      if (conv.participants && Array.isArray(conv.participants)) {\n        for (const participant of conv.participants) {\n          try {\n            if (participant) {\n              normalizedParticipants.push(this.normalizeUser(participant));\n            }\n          } catch (error) {\n            this.logger.warn('[MessageService] Error normalizing participant, skipping', error);\n          }\n        }\n      } else {\n        this.logger.warn('[MessageService] Conversation has no participants or invalid participants array', conv);\n      }\n      // Normaliser les messages avec gestion d'erreur\n      const normalizedMessages = [];\n      if (conv.messages && Array.isArray(conv.messages)) {\n        this.logger.debug('[MessageService] Processing conversation messages', {\n          count: conv.messages.length\n        });\n        for (const message of conv.messages) {\n          try {\n            if (message) {\n              const normalizedMessage = this.normalizeMessage(message);\n              this.logger.debug('[MessageService] Successfully normalized message', {\n                messageId: normalizedMessage.id,\n                content: normalizedMessage.content?.substring(0, 20),\n                sender: normalizedMessage.sender?.username\n              });\n              normalizedMessages.push(normalizedMessage);\n            }\n          } catch (error) {\n            this.logger.warn('[MessageService] Error normalizing message in conversation, skipping', error);\n          }\n        }\n      } else {\n        this.logger.debug('[MessageService] No messages found in conversation or invalid messages array');\n      }\n      // Normaliser le dernier message avec gestion d'erreur\n      let normalizedLastMessage = null;\n      if (conv.lastMessage) {\n        try {\n          normalizedLastMessage = this.normalizeMessage(conv.lastMessage);\n        } catch (error) {\n          this.logger.warn('[MessageService] Error normalizing last message, using null', error);\n        }\n      }\n      // Construire la conversation normalisée\n      const normalizedConversation = {\n        ...conv,\n        _id: conv.id || conv._id,\n        id: conv.id || conv._id,\n        participants: normalizedParticipants,\n        messages: normalizedMessages,\n        lastMessage: normalizedLastMessage,\n        unreadCount: conv.unreadCount || 0,\n        isGroup: !!conv.isGroup,\n        createdAt: this.normalizeDate(conv.createdAt),\n        updatedAt: this.normalizeDate(conv.updatedAt)\n      };\n      this.logger.debug('[MessageService] Conversation normalized successfully', {\n        conversationId: normalizedConversation.id,\n        participantCount: normalizedParticipants.length,\n        messageCount: normalizedMessages.length\n      });\n      return normalizedConversation;\n    } catch (error) {\n      this.logger.error('[MessageService] Error normalizing conversation:', error instanceof Error ? error : new Error(String(error)), conv);\n      throw new Error(`Failed to normalize conversation: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n  normalizeDate(date) {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to parse date: ${date}`, error);\n      return new Date();\n    }\n  }\n  // Méthode sécurisée pour créer une date à partir d'une valeur potentiellement undefined\n  safeDate(date) {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to create safe date: ${date}`, error);\n      return new Date();\n    }\n  }\n  normalizeNotification(notification) {\n    this.logger.debug('MessageService', 'Normalizing notification', notification);\n    if (!notification) {\n      this.logger.error('MessageService', 'Notification is null or undefined');\n      throw new Error('Notification is required');\n    }\n    // Vérifier et normaliser l'ID\n    const notificationId = notification.id || notification._id;\n    if (!notificationId) {\n      this.logger.error('MessageService', 'Notification ID is missing', notification);\n      throw new Error('Notification ID is required');\n    }\n    if (!notification.timestamp) {\n      this.logger.warn('MessageService', 'Notification timestamp is missing, using current time', notification);\n      notification.timestamp = new Date();\n    }\n    try {\n      const normalized = {\n        ...notification,\n        _id: notificationId,\n        id: notificationId,\n        timestamp: new Date(notification.timestamp),\n        ...(notification.senderId && {\n          senderId: this.normalizeSender(notification.senderId)\n        }),\n        ...(notification.message && {\n          message: this.normalizeNotMessage(notification.message)\n        })\n      };\n      this.logger.debug('MessageService', 'Normalized notification result', normalized);\n      return normalized;\n    } catch (error) {\n      this.logger.error('MessageService', 'Error in normalizeNotification', error);\n      throw error;\n    }\n  }\n  normalizeSender(sender) {\n    return {\n      id: sender.id,\n      username: sender.username,\n      ...(sender.image && {\n        image: sender.image\n      })\n    };\n  }\n  /**\n   * Normalise un message de notification\n   * @param message Message à normaliser\n   * @returns Message normalisé\n   */\n  normalizeNotMessage(message) {\n    if (!message) return null;\n    return {\n      id: message.id || message._id,\n      content: message.content || '',\n      type: message.type || 'TEXT',\n      timestamp: this.safeDate(message.timestamp),\n      attachments: message.attachments || [],\n      ...(message.sender && {\n        sender: this.normalizeSender(message.sender)\n      })\n    };\n  }\n  updateCache(notifications) {\n    this.logger.debug('MessageService', `Updating notification cache with ${notifications.length} notifications`);\n    if (notifications.length === 0) {\n      this.logger.warn('MessageService', 'No notifications to update in cache');\n      return;\n    }\n    console.log(`Starting to update cache with ${notifications.length} notifications`);\n    // Vérifier si les notifications ont des IDs valides\n    const validNotifications = notifications.filter(notif => notif && (notif.id || notif._id));\n    if (validNotifications.length !== notifications.length) {\n      console.warn(`Found ${notifications.length - validNotifications.length} notifications without valid IDs`);\n    }\n    // Traiter chaque notification\n    validNotifications.forEach((notif, index) => {\n      try {\n        // S'assurer que la notification a un ID\n        const notifId = notif.id || notif._id;\n        if (!notifId) {\n          console.error('Notification without ID:', notif);\n          return;\n        }\n        // Normaliser la notification\n        const normalized = this.normalizeNotification(notif);\n        // Vérifier si cette notification existe déjà dans le cache\n        if (this.notificationCache.has(normalized.id)) {\n          console.log(`Notification ${normalized.id} already exists in cache, skipping`);\n          return;\n        }\n        // Ajouter au cache\n        this.notificationCache.set(normalized.id, normalized);\n        console.log(`Added notification ${normalized.id} to cache`);\n      } catch (error) {\n        console.error(`Error processing notification ${index + 1}:`, error);\n        console.error('Problematic notification:', notif);\n      }\n    });\n    console.log(`Notification cache updated, now contains ${this.notificationCache.size} notifications`);\n    // Sauvegarder les notifications dans le localStorage après la mise à jour du cache\n    this.saveNotificationsToLocalStorage();\n  }\n  updateUnreadCount() {\n    const count = Array.from(this.notificationCache.values()).filter(n => !n.isRead).length;\n    this.notificationCount.next(count);\n  }\n  updateNotificationCache(notification) {\n    // Vérifier si la notification existe déjà dans le cache (pour éviter les doublons)\n    if (!this.notificationCache.has(notification.id)) {\n      this.notificationCache.set(notification.id, notification);\n      this.notifications.next(Array.from(this.notificationCache.values()));\n      this.updateUnreadCount();\n      // Sauvegarder les notifications dans le localStorage après chaque mise à jour\n      this.saveNotificationsToLocalStorage();\n    } else {\n      this.logger.debug('MessageService', `Notification ${notification.id} already exists in cache, skipping`);\n    }\n  }\n  updateNotificationStatus(ids, isRead) {\n    ids.forEach(id => {\n      const notif = this.notificationCache.get(id);\n      if (notif) {\n        this.notificationCache.set(id, {\n          ...notif,\n          isRead\n        });\n      }\n    });\n    this.notifications.next(Array.from(this.notificationCache.values()));\n    this.updateUnreadCount();\n  }\n  // Typing indicators\n  startTyping(conversationId) {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot start typing: no user ID');\n      return of(false);\n    }\n    return this.apollo.mutate({\n      mutation: START_TYPING_MUTATION,\n      variables: {\n        input: {\n          conversationId,\n          userId\n        }\n      }\n    }).pipe(map(result => result.data?.startTyping || false), catchError(error => {\n      this.logger.error('MessageService', 'Error starting typing indicator', error);\n      return throwError(() => new Error('Failed to start typing indicator'));\n    }));\n  }\n  stopTyping(conversationId) {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot stop typing: no user ID');\n      return of(false);\n    }\n    return this.apollo.mutate({\n      mutation: STOP_TYPING_MUTATION,\n      variables: {\n        input: {\n          conversationId,\n          userId\n        }\n      }\n    }).pipe(map(result => result.data?.stopTyping || false), catchError(error => {\n      this.logger.error('MessageService', 'Error stopping typing indicator', error);\n      return throwError(() => new Error('Failed to stop typing indicator'));\n    }));\n  }\n  // destroy\n  cleanupSubscriptions() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.subscriptions = [];\n    if (this.cleanupInterval) {\n      clearInterval(this.cleanupInterval);\n    }\n    this.notificationCache.clear();\n    this.logger.debug('NotificationService destroyed');\n  }\n  ngOnDestroy() {\n    this.cleanupSubscriptions();\n  }\n  static {\n    this.ɵfac = function MessageService_Factory(t) {\n      return new (t || MessageService)(i0.ɵɵinject(i1.Apollo), i0.ɵɵinject(i2.LoggerService), i0.ɵɵinject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MessageService,\n      factory: MessageService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "Observable", "of", "throwError", "retry", "EMPTY", "map", "catchError", "tap", "filter", "switchMap", "from", "MessageType", "CallType", "CallStatus", "GET_CONVERSATIONS_QUERY", "GET_NOTIFICATIONS_QUERY", "NOTIFICATION_SUBSCRIPTION", "GET_CONVERSATION_QUERY", "SEND_MESSAGE_MUTATION", "MARK_AS_READ_MUTATION", "MESSAGE_SENT_SUBSCRIPTION", "USER_STATUS_SUBSCRIPTION", "GET_USER_QUERY", "GET_ALL_USER_QUERY", "CONVERSATION_UPDATED_SUBSCRIPTION", "SEARCH_MESSAGES_QUERY", "GET_UNREAD_MESSAGES_QUERY", "SET_USER_ONLINE_MUTATION", "SET_USER_OFFLINE_MUTATION", "START_TYPING_MUTATION", "STOP_TYPING_MUTATION", "TYPING_INDICATOR_SUBSCRIPTION", "GET_CURRENT_USER_QUERY", "REACT_TO_MESSAGE_MUTATION", "FORWARD_MESSAGE_MUTATION", "PIN_MESSAGE_MUTATION", "CREATE_GROUP_MUTATION", "UPDATE_GROUP_MUTATION", "DELETE_GROUP_MUTATION", "LEAVE_GROUP_MUTATION", "GET_GROUP_QUERY", "GET_USER_GROUPS_QUERY", "EDIT_MESSAGE_MUTATION", "DELETE_MESSAGE_MUTATION", "GET_MESSAGES_QUERY", "GET_NOTIFICATIONS_ATTACHAMENTS", "MARK_NOTIFICATION_READ_MUTATION", "NOTIFICATIONS_READ_SUBSCRIPTION", "CREATE_CONVERSATION_MUTATION", "DELETE_NOTIFICATION_MUTATION", "DELETE_MULTIPLE_NOTIFICATIONS_MUTATION", "DELETE_ALL_NOTIFICATIONS_MUTATION", "CALL_HISTORY_QUERY", "CALL_DETAILS_QUERY", "CALL_STATS_QUERY", "INITIATE_CALL_MUTATION", "SEND_CALL_SIGNAL_MUTATION", "ACCEPT_CALL_MUTATION", "REJECT_CALL_MUTATION", "END_CALL_MUTATION", "TOGGLE_CALL_MEDIA_MUTATION", "CALL_SIGNAL_SUBSCRIPTION", "INCOMING_CALL_SUBSCRIPTION", "GET_VOICE_MESSAGES_QUERY", "MessageService", "constructor", "apollo", "logger", "zone", "activeConversation", "notifications", "notificationCache", "Map", "notificationCount", "onlineUsers", "subscriptions", "CACHE_DURATION", "lastFetchTime", "activeCall", "incomingCall", "callSignals", "localStream", "remoteStream", "peerConnection", "activeCall$", "asObservable", "incomingCall$", "callSignals$", "localStream$", "remoteStream$", "rtcConfig", "iceServers", "urls", "usersCache", "currentUserPagination", "totalCount", "totalPages", "currentPage", "hasNextPage", "hasPreviousPage", "activeConversation$", "notifications$", "notificationCount$", "sounds", "isPlaying", "muted", "notificationPagination", "limit", "hasMoreNotifications", "toSafeISOString", "date", "undefined", "toISOString", "loadNotificationsFromLocalStorage", "initSubscriptions", "startCleanupInterval", "preloadSounds", "savedNotifications", "localStorage", "getItem", "JSON", "parse", "debug", "length", "clear", "for<PERSON>ach", "notification", "id", "set", "next", "Array", "values", "updateUnreadCount", "size", "error", "runOutsideAngular", "subscribeToNewNotifications", "subscribe", "subscribeToNotificationsRead", "subscribeToIncomingCalls", "subscribeToUserStatus", "query", "pipe", "data", "handleIncomingCall", "call", "play", "loadSound", "name", "path", "audio", "Audio", "load", "addEventListener", "loop", "sound", "warn", "currentTime", "catch", "stop", "pause", "stopAllSounds", "Object", "keys", "setMuted", "info", "isMuted", "playNotificationSound", "console", "log", "audioContext", "window", "AudioContext", "webkitAudioContext", "oscillator", "createOscillator", "gainNode", "createGain", "type", "frequency", "setValueAtTime", "gain", "linearRampToValueAtTime", "connect", "destination", "start", "volume", "err", "audioError", "sendVoiceMessage", "receiverId", "audioBlob", "conversationId", "duration", "Error", "timestamp", "Date", "now", "audioFile", "File", "lastModified", "metadata", "isVoiceMessage", "sendMessage", "VOICE_MESSAGE", "playAudio", "audioUrl", "Promise", "resolve", "reject", "onended", "onerror", "getVoiceMessages", "watch<PERSON><PERSON>y", "fetchPolicy", "valueChanges", "result", "voiceMessages", "getMessages", "senderId", "page", "variables", "messages", "msg", "normalizeMessage", "editMessage", "messageId", "newContent", "mutate", "mutation", "deleteMessage", "content", "file", "messageType", "TEXT", "replyTo", "substring", "token", "finalMessageType", "startsWith", "IMAGE", "VIDEO", "AUDIO", "FILE", "defineProperty", "value", "enumerable", "writable", "context", "useMultipart", "normalizedMessage", "normalizationError", "minimalMessage", "isRead", "sender", "getCurrentUserId", "username", "markMessageAsRead", "readAt", "reactToMessage", "emoji", "forwardMessage", "conversationIds", "normalizeDate", "pinMessage", "pinnedAt", "searchMessages", "filters", "dateFrom", "dateTo", "safeDate", "normalizeUser", "getUnreadMessages", "userId", "setActiveConversation", "getConversations", "conversations", "conv", "normalizeConversation", "getConversation", "offset", "normalizedConversation", "participants", "createConversation", "conversation", "message", "getOrCreateConversation", "currentUserId", "existingConversation", "find", "isGroup", "participantIds", "p", "_id", "includes", "getNotifications", "refresh", "deletedNotificationIds", "getDeletedNotificationIds", "errors", "e", "join", "getUserNotifications", "filteredNotifications", "notif", "has", "index", "updateCache", "cachedNotifications", "saveNotificationsToLocalStorage", "graphQLErrors", "networkError", "deletedIds", "Set", "savedNotificationIds", "n", "serverNotifications", "client", "readQuery", "add", "loadMoreNotifications", "nextPage", "getNotificationById", "getNotificationCount", "getNotificationAttachments", "notificationId", "getUnreadNotifications", "deleteNotification", "delete", "response", "success", "setItem", "stringify", "deleteAllNotifications", "count", "deleteMultipleNotifications", "notificationIds", "groupNotificationsByType", "groups", "get", "push", "mark<PERSON><PERSON><PERSON>", "readCount", "remainingCount", "validIds", "trim", "provided", "valid", "updateNotificationStatus", "optimisticResponse", "markNotificationsAsRead", "Math", "max", "errorPolicy", "initiateCall", "recipientId", "callType", "options", "setupMediaDevices", "stream", "RTCPeerConnection", "getTracks", "track", "addTrack", "onicecandidate", "event", "candidate", "sendCallSignal", "generateCallId", "ontrack", "MediaStream", "streams", "createOffer", "offer", "setLocalDescription", "callId", "signalSub", "subscribeToCallSignals", "cleanupCall", "acceptCall", "setRemoteDescription", "RTCSessionDescription", "createAnswer", "answer", "caller", "rejectCall", "reason", "endCall", "feedback", "toggleMedia", "video", "getVideoTracks", "enabled", "getAudioTracks", "toggleCallMedia", "callSignal", "signal", "handleCallSignal", "signalType", "signalData", "getCallHistory", "status", "startDate", "endDate", "history", "callHistory", "getCallDetails", "details", "callDetails", "getCallStats", "stats", "callStats", "handleIceCandidate", "handleAnswer", "handleEndCall", "handleRejectCall", "addIceCandidate", "RTCIceCandidate", "currentCall", "ENDED", "endTime", "REJECTED", "close", "constraints", "width", "ideal", "height", "observer", "navigator", "mediaDevices", "getUserMedia", "then", "complete", "toString", "random", "getAllUsers", "forceRefresh", "search", "sortBy", "sortOrder", "isOnline", "cacheValid", "paginatedResponse", "users", "user", "getOneUser", "getCurrentUser", "setUserOnline", "setUserOffline", "createGroup", "photo", "description", "group", "updateGroup", "groupId", "input", "deleteGroup", "leaveGroup", "getGroup", "getUserGroups", "subscribeToNewMessages", "isTokenValid", "sub$", "messageSent", "attachments", "some", "att", "updateConversationWithNewMessage", "sub", "userStatusChanged", "subscribeToConversationUpdates", "conversationUpdated", "lastMessage", "subscribeToTypingIndicator", "typingIndicator", "Boolean", "parts", "split", "payload", "atob", "exp", "expirationDate", "expiration", "notificationsRead", "source$", "processed$", "notificationReceived", "normalized", "normalizeNotification", "updateNotificationCache", "cleanupInterval", "setInterval", "cleanupExpiredNotifications", "thirtyDaysAgo", "getTime", "expiredCount", "notificationDate", "normalizedSender", "email", "role", "isActive", "normalizedReceiver", "receiver", "normalizedAttachments", "url", "String", "normalizeNotMessage", "image", "bio", "lastActive", "createdAt", "updatedAt", "followingCount", "followersCount", "postCount", "normalizedParticipants", "isArray", "participant", "normalizedMessages", "normalizedLastMessage", "unreadCount", "participantCount", "messageCount", "normalizeSender", "validNotifications", "notifId", "ids", "startTyping", "stopTyping", "cleanupSubscriptions", "unsubscribe", "clearInterval", "ngOnDestroy", "i0", "ɵɵinject", "i1", "Apollo", "i2", "LoggerService", "NgZone", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\message.service.ts"], "sourcesContent": ["import { Injectable, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/core';\nimport { <PERSON> } from 'apollo-angular';\nimport {\n  BehaviorSubject,\n  Observable,\n  of,\n  Subscription,\n  throwError,\n  retry,\n  EMPTY,\n} from 'rxjs';\nimport {\n  map,\n  catchError,\n  tap,\n  filter,\n  switchMap,\n  concatMap,\n  toArray,\n} from 'rxjs/operators';\nimport { from } from 'rxjs';\nimport {\n  MessageType,\n  Call,\n  CallType,\n  CallStatus,\n  IncomingCall,\n  CallSignal,\n  CallOptions,\n  CallFeedback,\n  CallSuccess,\n} from '../models/message.model';\nimport {\n  GET_CONVERSATIONS_QUERY,\n  GET_NOTIFICATIONS_QUERY,\n  NOTIFICATION_SUBSCRIPTION,\n  GET_CONVERSATION_QUERY,\n  SEND_MESSAGE_MUTATION,\n  MARK_AS_READ_MUTATION,\n  MESSAGE_SENT_SUBSCRIPTION,\n  USER_STATUS_SUBSCRIPTION,\n  GET_USER_QUERY,\n  GET_ALL_USER_QUERY,\n  CONVERSATION_UPDATED_SUBSCRIPTION,\n  SEARCH_MESSAGES_QUERY,\n  GET_UNREAD_MESSAGES_QUERY,\n  SET_USER_ONLINE_MUTATION,\n  SET_USER_OFFLINE_MUTATION,\n  START_TYPING_MUTATION,\n  STOP_TYPING_MUTATION,\n  TYPING_INDICATOR_SUBSCRIPTION,\n  GET_CURRENT_USER_QUERY,\n  REACT_TO_MESSAGE_MUTATION,\n  FORWARD_MESSAGE_MUTATION,\n  PIN_MESSAGE_MUTATION,\n  CREATE_GROUP_MUTATION,\n  UPDATE_GROUP_MUTATION,\n  DELETE_GROUP_MUTATION,\n  ADD_GROUP_PARTICIPANTS_MUTATION,\n  REMOVE_GROUP_PARTICIPANTS_MUTATION,\n  LEAVE_GROUP_MUTATION,\n  GET_GROUP_QUERY,\n  GET_USER_GROUPS_QUERY,\n  EDIT_MESSAGE_MUTATION,\n  DELETE_MESSAGE_MUTATION,\n  GET_MESSAGES_QUERY,\n  GET_NOTIFICATIONS_ATTACHAMENTS,\n  MARK_NOTIFICATION_READ_MUTATION,\n  NOTIFICATIONS_READ_SUBSCRIPTION,\n  CREATE_CONVERSATION_MUTATION,\n  DELETE_NOTIFICATION_MUTATION,\n  DELETE_MULTIPLE_NOTIFICATIONS_MUTATION,\n  DELETE_ALL_NOTIFICATIONS_MUTATION,\n  // Requêtes et mutations pour les appels\n  CALL_HISTORY_QUERY,\n  CALL_DETAILS_QUERY,\n  CALL_STATS_QUERY,\n  INITIATE_CALL_MUTATION,\n  SEND_CALL_SIGNAL_MUTATION,\n  ACCEPT_CALL_MUTATION,\n  REJECT_CALL_MUTATION,\n  END_CALL_MUTATION,\n  TOGGLE_CALL_MEDIA_MUTATION,\n  CALL_SIGNAL_SUBSCRIPTION,\n  INCOMING_CALL_SUBSCRIPTION,\n  CALL_STATUS_CHANGED_SUBSCRIPTION,\n  GET_VOICE_MESSAGES_QUERY,\n} from '../graphql/message.graphql';\nimport {\n  Conversation,\n  Message,\n  Notification,\n  User,\n  Attachment,\n  getNotificationAttachmentsEvent,\n  Group,\n  MessageFilter,\n  TypingIndicatorEvent,\n  GetConversationsResponse,\n  GetConversationResponse,\n  MarkAsReadResponse,\n  ReactToMessageResponse,\n  ForwardMessageResponse,\n  PinMessageResponse,\n  SearchMessagesResponse,\n  SendMessageResponse,\n  GetUnreadMessagesResponse,\n  GetAllUsersResponse,\n  GetOneUserResponse,\n  getCurrentUserResponse,\n  SetUserOnlineResponse,\n  SetUserOfflineResponse,\n  GetGroupResponse,\n  GetUserGroupsResponse,\n  CreateGroupResponse,\n  UpdateGroupResponse,\n  StartTupingResponse,\n  StopTypingResponse,\n  TypingIndicatorEvents,\n  getUserNotificationsResponse,\n  NotificationType,\n  MarkNotificationsAsReadResponse,\n  NotificationReceivedEvent,\n  NotificationsReadEvent,\n} from '../models/message.model';\nimport { LoggerService } from './logger.service';\n@Injectable({\n  providedIn: 'root',\n})\nexport class MessageService implements OnDestroy {\n  // État partagé\n  private activeConversation = new BehaviorSubject<string | null>(null);\n  private notifications = new BehaviorSubject<Notification[]>([]);\n  private notificationCache = new Map<string, Notification>();\n  private cleanupInterval: any;\n  private notificationCount = new BehaviorSubject<number>(0);\n  private onlineUsers = new Map<string, User>();\n  private subscriptions: Subscription[] = [];\n  private readonly CACHE_DURATION = 300000;\n  private lastFetchTime = 0;\n\n  // Propriétés pour les appels\n  private activeCall = new BehaviorSubject<Call | null>(null);\n  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);\n  private callSignals = new BehaviorSubject<CallSignal | null>(null);\n  private localStream: MediaStream | null = null;\n  private remoteStream: MediaStream | null = null;\n  private peerConnection: RTCPeerConnection | null = null;\n\n  // Observables publics pour les appels\n  public activeCall$ = this.activeCall.asObservable();\n  public incomingCall$ = this.incomingCall.asObservable();\n  public callSignals$ = this.callSignals.asObservable();\n  public localStream$ = new BehaviorSubject<MediaStream | null>(null);\n  public remoteStream$ = new BehaviorSubject<MediaStream | null>(null);\n\n  // Configuration WebRTC\n  private readonly rtcConfig: RTCConfiguration = {\n    iceServers: [\n      { urls: 'stun:stun.l.google.com:19302' },\n      { urls: 'stun:stun1.l.google.com:19302' },\n    ],\n  };\n  private usersCache: User[] = [];\n\n  // Pagination metadata for user list\n  public currentUserPagination: {\n    totalCount: number;\n    totalPages: number;\n    currentPage: number;\n    hasNextPage: boolean;\n    hasPreviousPage: boolean;\n  } = {\n    totalCount: 0,\n    totalPages: 0,\n    currentPage: 1,\n    hasNextPage: false,\n    hasPreviousPage: false,\n  };\n\n  // Observables publics\n  public activeConversation$ = this.activeConversation.asObservable();\n  public notifications$ = this.notifications.asObservable();\n  public notificationCount$ = this.notificationCount.asObservable();\n\n  // Propriétés pour la gestion des sons\n  private sounds: { [key: string]: HTMLAudioElement } = {};\n  private isPlaying: { [key: string]: boolean } = {};\n  private muted = false;\n\n  constructor(\n    private apollo: Apollo,\n    private logger: LoggerService,\n    private zone: NgZone\n  ) {\n    this.loadNotificationsFromLocalStorage();\n    this.initSubscriptions();\n    this.startCleanupInterval();\n    this.preloadSounds();\n  }\n\n  /**\n   * Charge les notifications depuis le localStorage\n   * @private\n   */\n  private loadNotificationsFromLocalStorage(): void {\n    try {\n      const savedNotifications = localStorage.getItem('notifications');\n      if (savedNotifications) {\n        const notifications = JSON.parse(savedNotifications) as Notification[];\n        this.logger.debug(\n          'MessageService',\n          `Chargement de ${notifications.length} notifications depuis le localStorage`\n        );\n\n        // Vider le cache avant de charger les notifications pour éviter les doublons\n        this.notificationCache.clear();\n\n        // Mettre à jour le cache avec les notifications sauvegardées\n        notifications.forEach((notification) => {\n          // Vérifier que la notification a un ID valide\n          if (notification && notification.id) {\n            this.notificationCache.set(notification.id, notification);\n          }\n        });\n\n        // Mettre à jour le BehaviorSubject avec les notifications chargées\n        this.notifications.next(Array.from(this.notificationCache.values()));\n        this.updateUnreadCount();\n\n        this.logger.debug(\n          'MessageService',\n          `${this.notificationCache.size} notifications chargées dans le cache`\n        );\n      }\n    } catch (error) {\n      this.logger.error(\n        'MessageService',\n        'Erreur lors du chargement des notifications depuis le localStorage:',\n        error\n      );\n    }\n  }\n  private initSubscriptions(): void {\n    this.zone.runOutsideAngular(() => {\n      this.subscribeToNewNotifications().subscribe();\n      this.subscribeToNotificationsRead().subscribe();\n      this.subscribeToIncomingCalls().subscribe();\n    });\n    this.subscribeToUserStatus();\n  }\n\n  /**\n   * S'abonne aux appels entrants\n   */\n  private subscribeToIncomingCalls(): Observable<IncomingCall | null> {\n    return this.apollo\n      .subscribe<{ incomingCall: IncomingCall }>({\n        query: INCOMING_CALL_SUBSCRIPTION,\n      })\n      .pipe(\n        map(({ data }) => {\n          if (!data?.incomingCall) {\n            return null;\n          }\n\n          // Gérer l'appel entrant\n          this.handleIncomingCall(data.incomingCall);\n          return data.incomingCall;\n        }),\n        catchError((error) => {\n          this.logger.error('Error in incoming call subscription', error);\n          return of(null);\n        })\n      );\n  }\n\n  /**\n   * Gère un appel entrant\n   */\n  private handleIncomingCall(call: IncomingCall): void {\n    this.logger.debug('Incoming call received', call);\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n  }\n\n  // --------------------------------------------------------------------------\n  // Section: Gestion des sons (intégré depuis SoundService)\n  // --------------------------------------------------------------------------\n\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  private preloadSounds(): void {\n    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n    this.loadSound('notification', 'assets/sounds/notification.mp3');\n  }\n\n  /**\n   * Charge un fichier audio\n   * @param name Nom du son\n   * @param path Chemin du fichier\n   */\n  private loadSound(name: string, path: string): void {\n    try {\n      const audio = new Audio(path);\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n\n      // Gérer la fin de la lecture\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n      });\n\n      this.logger.debug('MessageService', `Son chargé: ${name} (${path})`);\n    } catch (error) {\n      this.logger.error(\n        'MessageService',\n        `Erreur lors du chargement du son ${name}:`,\n        error\n      );\n    }\n  }\n\n  /**\n   * Joue un son\n   * @param name Nom du son\n   * @param loop Lecture en boucle\n   */\n  play(name: string, loop: boolean = false): void {\n    if (this.muted) {\n      this.logger.debug('MessageService', `Son ${name} non joué (muet)`);\n      return;\n    }\n\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        this.logger.warn('MessageService', `Son ${name} non trouvé`);\n        return;\n      }\n\n      // Configurer la lecture en boucle\n      sound.loop = loop;\n\n      // Jouer le son s'il n'est pas déjà en cours de lecture\n      if (!this.isPlaying[name]) {\n        sound.currentTime = 0;\n        sound.play().catch((error) => {\n          this.logger.error(\n            'MessageService',\n            `Erreur lors de la lecture du son ${name}:`,\n            error\n          );\n        });\n        this.isPlaying[name] = true;\n        this.logger.debug(\n          'MessageService',\n          `Lecture du son: ${name}, boucle: ${loop}`\n        );\n      }\n    } catch (error) {\n      this.logger.error(\n        'MessageService',\n        `Erreur lors de la lecture du son ${name}:`,\n        error\n      );\n    }\n  }\n\n  /**\n   * Arrête un son\n   * @param name Nom du son\n   */\n  stop(name: string): void {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        this.logger.warn('MessageService', `Son ${name} non trouvé`);\n        return;\n      }\n\n      // Arrêter le son s'il est en cours de lecture\n      if (this.isPlaying[name]) {\n        sound.pause();\n        sound.currentTime = 0;\n        this.isPlaying[name] = false;\n        this.logger.debug('MessageService', `Son arrêté: ${name}`);\n      }\n    } catch (error) {\n      this.logger.error(\n        'MessageService',\n        `Erreur lors de l'arrêt du son ${name}:`,\n        error\n      );\n    }\n  }\n\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds(): void {\n    Object.keys(this.sounds).forEach((name) => {\n      this.stop(name);\n    });\n    this.logger.debug('MessageService', 'Tous les sons ont été arrêtés');\n  }\n\n  /**\n   * Active ou désactive le son\n   * @param muted True pour désactiver le son, false pour l'activer\n   */\n  setMuted(muted: boolean): void {\n    this.muted = muted;\n    this.logger.info('MessageService', `Son ${muted ? 'désactivé' : 'activé'}`);\n\n    if (muted) {\n      this.stopAllSounds();\n    }\n  }\n\n  /**\n   * Vérifie si le son est désactivé\n   * @returns True si le son est désactivé, false sinon\n   */\n  isMuted(): boolean {\n    return this.muted;\n  }\n\n  /**\n   * Joue le son de notification\n   */\n  playNotificationSound(): void {\n    console.log('MessageService: Tentative de lecture du son de notification');\n\n    if (this.muted) {\n      console.log('MessageService: Son désactivé, notification ignorée');\n      return;\n    }\n\n    // Utiliser l'API Web Audio pour générer un son de notification simple\n    try {\n      // Créer un contexte audio\n      const audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n\n      // Créer un oscillateur pour générer un son\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n\n      // Configurer l'oscillateur\n      oscillator.type = 'sine';\n      oscillator.frequency.setValueAtTime(880, audioContext.currentTime); // La note A5\n\n      // Configurer le volume\n      gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n      gainNode.gain.linearRampToValueAtTime(\n        0.5,\n        audioContext.currentTime + 0.01\n      );\n      gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.3);\n\n      // Connecter les nœuds\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n\n      // Démarrer et arrêter l'oscillateur\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.3);\n\n      console.log('MessageService: Son de notification généré avec succès');\n    } catch (error) {\n      console.error(\n        'MessageService: Erreur lors de la génération du son:',\n        error\n      );\n\n      // Fallback à la méthode originale en cas d'erreur\n      try {\n        const audio = new Audio('assets/sounds/notification.mp3');\n        audio.volume = 1.0; // Volume maximum\n        audio.play().catch((err) => {\n          console.error(\n            'MessageService: Erreur lors de la lecture du fichier son:',\n            err\n          );\n        });\n      } catch (audioError) {\n        console.error(\n          'MessageService: Exception lors de la lecture du fichier son:',\n          audioError\n        );\n      }\n    }\n  }\n  // --------------------------------------------------------------------------\n  // Section 1: Méthodes pour les Messages\n  // --------------------------------------------------------------------------\n\n  /**\n   * Envoie un message vocal à un utilisateur\n   * @param receiverId ID de l'utilisateur destinataire\n   * @param audioBlob Blob audio à envoyer\n   * @param conversationId ID de la conversation (optionnel)\n   * @param duration Durée de l'enregistrement en secondes (optionnel)\n   * @returns Observable avec le message envoyé\n   */\n  sendVoiceMessage(\n    receiverId: string,\n    audioBlob: Blob,\n    conversationId?: string,\n    duration?: number\n  ): Observable<Message> {\n    this.logger.debug(\n      `[MessageService] Sending voice message to user ${receiverId}, duration: ${duration}s`\n    );\n\n    // Vérifier que le blob audio est valide\n    if (!audioBlob || audioBlob.size === 0) {\n      this.logger.error('[MessageService] Invalid audio blob');\n      return throwError(() => new Error('Invalid audio blob'));\n    }\n\n    // Créer un fichier à partir du blob audio avec un nom unique\n    const timestamp = Date.now();\n    const audioFile = new File([audioBlob], `voice-message-${timestamp}.webm`, {\n      type: 'audio/webm',\n      lastModified: timestamp,\n    });\n\n    // Vérifier que le fichier a été créé correctement\n    if (!audioFile || audioFile.size === 0) {\n      this.logger.error('[MessageService] Failed to create audio file');\n      return throwError(() => new Error('Failed to create audio file'));\n    }\n\n    this.logger.debug(\n      `[MessageService] Created audio file: ${audioFile.name}, size: ${audioFile.size} bytes`\n    );\n\n    // Créer des métadonnées pour le message vocal\n    const metadata = {\n      duration: duration || 0,\n      isVoiceMessage: true,\n      timestamp: timestamp,\n    };\n\n    // Utiliser la méthode sendMessage avec le type VOICE_MESSAGE\n    // Utiliser une chaîne vide comme contenu pour éviter les problèmes de validation\n    return this.sendMessage(\n      receiverId,\n      ' ', // Espace comme contenu minimal pour passer la validation\n      audioFile,\n      MessageType.VOICE_MESSAGE,\n      conversationId,\n      undefined,\n      metadata\n    );\n  }\n\n  /**\n   * Joue un fichier audio\n   * @param audioUrl URL du fichier audio à jouer\n   * @returns Promise qui se résout lorsque la lecture est terminée\n   */\n  playAudio(audioUrl: string): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const audio = new Audio(audioUrl);\n\n      audio.onended = () => {\n        resolve();\n      };\n\n      audio.onerror = (error) => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      };\n\n      audio.play().catch((error) => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      });\n    });\n  }\n\n  /**\n   * Récupère tous les messages vocaux de l'utilisateur\n   * @returns Observable avec la liste des messages vocaux\n   */\n  getVoiceMessages(): Observable<Call[]> {\n    this.logger.debug('[MessageService] Getting voice messages');\n\n    return this.apollo\n      .watchQuery<{ getVoiceMessages: Call[] }>({\n        query: GET_VOICE_MESSAGES_QUERY,\n        fetchPolicy: 'network-only', // Ne pas utiliser le cache pour cette requête\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const voiceMessages = result.data?.getVoiceMessages || [];\n          this.logger.debug(\n            `[MessageService] Retrieved ${voiceMessages.length} voice messages`\n          );\n          return voiceMessages;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            '[MessageService] Error fetching voice messages:',\n            error\n          );\n          return throwError(() => new Error('Failed to fetch voice messages'));\n        })\n      );\n  }\n  // Message methods\n  getMessages(\n    senderId: string,\n    receiverId: string,\n    conversationId: string,\n    page: number = 1,\n    limit: number = 10\n  ): Observable<Message[]> {\n    return this.apollo\n      .watchQuery<{ getMessages: Message[] }>({\n        query: GET_MESSAGES_QUERY,\n        variables: { senderId, receiverId, conversationId, limit, page },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const messages = result.data?.getMessages || [];\n          return messages.map((msg) => this.normalizeMessage(msg));\n        }),\n        catchError((error) => {\n          this.logger.error('Error fetching messages:', error);\n          return throwError(() => new Error('Failed to fetch messages'));\n        })\n      );\n  }\n  editMessage(messageId: string, newContent: string): Observable<Message> {\n    return this.apollo\n      .mutate<{ editMessage: Message }>({\n        mutation: EDIT_MESSAGE_MUTATION,\n        variables: { messageId, newContent },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.editMessage) {\n            throw new Error('Failed to edit message');\n          }\n          return this.normalizeMessage(result.data.editMessage);\n        }),\n        catchError((error) => {\n          this.logger.error('Error editing message:', error);\n          return throwError(() => new Error('Failed to edit message'));\n        })\n      );\n  }\n\n  deleteMessage(messageId: string): Observable<Message> {\n    return this.apollo\n      .mutate<{ deleteMessage: Message }>({\n        mutation: DELETE_MESSAGE_MUTATION,\n        variables: { messageId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.deleteMessage) {\n            throw new Error('Failed to delete message');\n          }\n          return this.normalizeMessage(result.data.deleteMessage);\n        }),\n        catchError((error) => {\n          this.logger.error('Error deleting message:', error);\n          return throwError(() => new Error('Failed to delete message'));\n        })\n      );\n  }\n\n  sendMessage(\n    receiverId: string,\n    content: string,\n    file?: File,\n    messageType: MessageType = MessageType.TEXT,\n    conversationId?: string,\n    replyTo?: string,\n    metadata?: any\n  ): Observable<Message> {\n    this.logger.info(\n      `[MessageService] Sending message to: ${receiverId}, hasFile: ${!!file}`\n    );\n    this.logger.debug(\n      `[MessageService] Message content: \"${content?.substring(0, 50)}${\n        content?.length > 50 ? '...' : ''\n      }\"`\n    );\n\n    // Vérifier l'authentification\n    const token = localStorage.getItem('token');\n    this.logger.debug(\n      `[MessageService] Authentication check before sending message: token=${!!token}`\n    );\n\n    // Utiliser le type de message fourni ou le déterminer automatiquement\n    let finalMessageType = messageType;\n\n    // Si le type n'est pas explicitement fourni et qu'il y a un fichier, déterminer le type\n    if (file) {\n      // Si le type est déjà VOICE_MESSAGE, le conserver\n      if (messageType === MessageType.VOICE_MESSAGE) {\n        finalMessageType = MessageType.VOICE_MESSAGE;\n        this.logger.debug(`[MessageService] Using explicit VOICE_MESSAGE type`);\n      }\n      // Sinon, déterminer le type en fonction du type de fichier\n      else if (messageType === MessageType.TEXT) {\n        if (file.type.startsWith('image/')) {\n          finalMessageType = MessageType.IMAGE;\n        } else if (file.type.startsWith('video/')) {\n          finalMessageType = MessageType.VIDEO;\n        } else if (file.type.startsWith('audio/')) {\n          // Vérifier si c'est un message vocal basé sur les métadonnées\n          if (metadata && metadata.isVoiceMessage) {\n            finalMessageType = MessageType.VOICE_MESSAGE;\n          } else {\n            finalMessageType = MessageType.AUDIO;\n          }\n        } else {\n          finalMessageType = MessageType.FILE;\n        }\n      }\n    }\n\n    this.logger.debug(\n      `[MessageService] Message type determined: ${finalMessageType}`\n    );\n\n    // Ajouter le type de message aux variables\n    // Utiliser directement la valeur de l'énumération sans conversion\n    const variables: any = {\n      receiverId,\n      content,\n      type: finalMessageType, // Ajouter explicitement le type de message\n    };\n\n    // Forcer le type à être une valeur d'énumération GraphQL\n    // Cela empêche Apollo de convertir la valeur en minuscules\n    if (variables.type) {\n      Object.defineProperty(variables, 'type', {\n        value: finalMessageType,\n        enumerable: true,\n        writable: false,\n      });\n    }\n\n    // Ajouter les métadonnées si elles sont fournies\n    if (metadata) {\n      variables.metadata = metadata;\n      this.logger.debug(`[MessageService] Metadata attached:`, metadata);\n    }\n\n    if (file) {\n      variables.file = file;\n      this.logger.debug(\n        `[MessageService] File attached: ${file.name}, size: ${file.size}, type: ${file.type}, messageType: ${finalMessageType}`\n      );\n    }\n    if (conversationId) {\n      variables.conversationId = conversationId;\n      this.logger.debug(\n        `[MessageService] Using existing conversation: ${conversationId}`\n      );\n    }\n    if (replyTo) {\n      variables.replyTo = replyTo;\n      this.logger.debug(`[MessageService] Replying to message: ${replyTo}`);\n    }\n\n    const context = file ? { useMultipart: true, file } : undefined;\n\n    this.logger.debug(\n      `[MessageService] Sending GraphQL mutation with variables:`,\n      variables\n    );\n\n    return this.apollo\n      .mutate<SendMessageResponse>({\n        mutation: SEND_MESSAGE_MUTATION,\n        variables,\n        context,\n      })\n      .pipe(\n        map((result) => {\n          this.logger.debug(`[MessageService] Message send response:`, result);\n\n          if (!result.data?.sendMessage) {\n            this.logger.error(\n              `[MessageService] Failed to send message: No data returned`\n            );\n            throw new Error('Failed to send message');\n          }\n\n          try {\n            this.logger.debug(\n              `[MessageService] Normalizing sent message`,\n              result.data.sendMessage\n            );\n            const normalizedMessage = this.normalizeMessage(\n              result.data.sendMessage\n            );\n\n            this.logger.info(\n              `[MessageService] Message sent successfully: ${normalizedMessage.id}`\n            );\n            return normalizedMessage;\n          } catch (normalizationError) {\n            this.logger.error(\n              `[MessageService] Error normalizing message:`,\n              normalizationError\n            );\n\n            // Retourner un message minimal mais valide plutôt que de lancer une erreur\n            const minimalMessage: Message = {\n              id: result.data.sendMessage.id || 'temp-' + Date.now(),\n              content: result.data.sendMessage.content || '',\n              type: result.data.sendMessage.type || MessageType.TEXT,\n              timestamp: new Date(),\n              isRead: false,\n              sender: {\n                id: this.getCurrentUserId(),\n                username: 'You',\n              },\n            };\n\n            this.logger.info(\n              `[MessageService] Returning minimal message: ${minimalMessage.id}`\n            );\n            return minimalMessage;\n          }\n        }),\n        catchError((error) => {\n          this.logger.error(`[MessageService] Error sending message:`, error);\n          return throwError(() => new Error('Failed to send message'));\n        })\n      );\n  }\n\n  markMessageAsRead(messageId: string): Observable<Message> {\n    return this.apollo\n      .mutate<MarkAsReadResponse>({\n        mutation: MARK_AS_READ_MUTATION,\n        variables: { messageId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.markMessageAsRead)\n            throw new Error('Failed to mark message as read');\n          return {\n            ...result.data.markMessageAsRead,\n            readAt: new Date(),\n          };\n        }),\n        catchError((error) => {\n          console.error('Error marking message as read:', error);\n          return throwError(() => new Error('Failed to mark message as read'));\n        })\n      );\n  }\n\n  reactToMessage(messageId: string, emoji: string): Observable<Message> {\n    return this.apollo\n      .mutate<ReactToMessageResponse>({\n        mutation: REACT_TO_MESSAGE_MUTATION,\n        variables: { messageId, emoji },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.reactToMessage)\n            throw new Error('Failed to react to message');\n          return result.data.reactToMessage;\n        }),\n        catchError((error) => {\n          console.error('Error reacting to message:', error);\n          return throwError(() => new Error('Failed to react to message'));\n        })\n      );\n  }\n\n  forwardMessage(\n    messageId: string,\n    conversationIds: string[]\n  ): Observable<Message[]> {\n    return this.apollo\n      .mutate<ForwardMessageResponse>({\n        mutation: FORWARD_MESSAGE_MUTATION,\n        variables: { messageId, conversationIds },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.forwardMessage)\n            throw new Error('Failed to forward message');\n          return result.data.forwardMessage.map((msg) => ({\n            ...msg,\n            timestamp: msg.timestamp\n              ? this.normalizeDate(msg.timestamp)\n              : new Date(),\n          }));\n        }),\n        catchError((error) => {\n          console.error('Error forwarding message:', error);\n          return throwError(() => new Error('Failed to forward message'));\n        })\n      );\n  }\n\n  pinMessage(messageId: string, conversationId: string): Observable<Message> {\n    return this.apollo\n      .mutate<PinMessageResponse>({\n        mutation: PIN_MESSAGE_MUTATION,\n        variables: { messageId, conversationId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.pinMessage)\n            throw new Error('Failed to pin message');\n          return {\n            ...result.data.pinMessage,\n            pinnedAt: new Date(),\n          };\n        }),\n        catchError((error) => {\n          console.error('Error pinning message:', error);\n          return throwError(() => new Error('Failed to pin message'));\n        })\n      );\n  }\n\n  searchMessages(\n    query: string,\n    conversationId?: string,\n    filters: MessageFilter = {}\n  ): Observable<Message[]> {\n    return this.apollo\n      .watchQuery<SearchMessagesResponse>({\n        query: SEARCH_MESSAGES_QUERY,\n        variables: {\n          query,\n          conversationId,\n          ...filters,\n          dateFrom: this.toSafeISOString(filters.dateFrom),\n          dateTo: this.toSafeISOString(filters.dateTo),\n        },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map(\n          (result) =>\n            result.data?.searchMessages?.map((msg) => ({\n              ...msg,\n              timestamp: this.safeDate(msg.timestamp),\n              sender: this.normalizeUser(msg.sender),\n            })) || []\n        ),\n        catchError((error) => {\n          console.error('Error searching messages:', error);\n          return throwError(() => new Error('Failed to search messages'));\n        })\n      );\n  }\n\n  getUnreadMessages(userId: string): Observable<Message[]> {\n    return this.apollo\n      .watchQuery<GetUnreadMessagesResponse>({\n        query: GET_UNREAD_MESSAGES_QUERY,\n        variables: { userId },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map(\n          (result) =>\n            result.data?.getUnreadMessages?.map((msg) => ({\n              ...msg,\n              timestamp: this.safeDate(msg.timestamp),\n              sender: this.normalizeUser(msg.sender),\n            })) || []\n        ),\n        catchError((error) => {\n          console.error('Error fetching unread messages:', error);\n          return throwError(() => new Error('Failed to fetch unread messages'));\n        })\n      );\n  }\n\n  setActiveConversation(conversationId: string): void {\n    this.activeConversation.next(conversationId);\n  }\n\n  getConversations(): Observable<Conversation[]> {\n    return this.apollo\n      .watchQuery<GetConversationsResponse>({\n        query: GET_CONVERSATIONS_QUERY,\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const conversations = result.data?.getConversations || [];\n          return conversations.map((conv) => this.normalizeConversation(conv));\n        }),\n        catchError((error) => {\n          console.error('Error fetching conversations:', error);\n          return throwError(() => new Error('Failed to load conversations'));\n        })\n      );\n  }\n\n  getConversation(\n    conversationId: string,\n    limit?: number,\n    page?: number\n  ): Observable<Conversation> {\n    this.logger.info(\n      `[MessageService] Getting conversation: ${conversationId}, limit: ${limit}, page: ${page}`\n    );\n\n    const variables: any = { conversationId };\n\n    // Ajouter les paramètres de pagination s'ils sont fournis\n    if (limit !== undefined) {\n      variables.limit = limit;\n    } else {\n      variables.limit = 10; // Valeur par défaut\n    }\n\n    // Calculer l'offset à partir de la page si elle est fournie\n    if (page !== undefined) {\n      // La requête GraphQL utilise offset, donc nous devons convertir la page en offset\n      const offset = (page - 1) * variables.limit;\n      variables.offset = offset;\n      this.logger.debug(\n        `[MessageService] Calculated offset: ${offset} from page: ${page} and limit: ${variables.limit}`\n      );\n    } else {\n      variables.offset = 0; // Valeur par défaut\n    }\n\n    this.logger.debug(\n      `[MessageService] Final pagination parameters: limit=${variables.limit}, offset=${variables.offset}`\n    );\n\n    return this.apollo\n      .watchQuery<GetConversationResponse>({\n        query: GET_CONVERSATION_QUERY,\n        variables: variables,\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          this.logger.debug(\n            `[MessageService] Conversation response received:`,\n            result\n          );\n\n          const conv = result.data?.getConversation;\n          if (!conv) {\n            this.logger.error(\n              `[MessageService] Conversation not found: ${conversationId}`\n            );\n            throw new Error('Conversation not found');\n          }\n\n          this.logger.debug(\n            `[MessageService] Normalizing conversation: ${conversationId}`\n          );\n          const normalizedConversation = this.normalizeConversation(conv);\n\n          this.logger.info(\n            `[MessageService] Conversation loaded successfully: ${conversationId}, participants: ${\n              normalizedConversation.participants?.length || 0\n            }, messages: ${normalizedConversation.messages?.length || 0}`\n          );\n          return normalizedConversation;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            `[MessageService] Error fetching conversation:`,\n            error\n          );\n          return throwError(() => new Error('Failed to load conversation'));\n        })\n      );\n  }\n\n  createConversation(userId: string): Observable<Conversation> {\n    this.logger.info(\n      `[MessageService] Creating conversation with user: ${userId}`\n    );\n\n    if (!userId) {\n      this.logger.error(\n        `[MessageService] Cannot create conversation: userId is undefined`\n      );\n      return throwError(\n        () => new Error('User ID is required to create a conversation')\n      );\n    }\n\n    return this.apollo\n      .mutate<{ createConversation: Conversation }>({\n        mutation: CREATE_CONVERSATION_MUTATION,\n        variables: { userId },\n      })\n      .pipe(\n        map((result) => {\n          this.logger.debug(\n            `[MessageService] Conversation creation response:`,\n            result\n          );\n\n          const conversation = result.data?.createConversation;\n          if (!conversation) {\n            this.logger.error(\n              `[MessageService] Failed to create conversation with user: ${userId}`\n            );\n            throw new Error('Failed to create conversation');\n          }\n\n          try {\n            const normalizedConversation =\n              this.normalizeConversation(conversation);\n            this.logger.info(\n              `[MessageService] Conversation created successfully: ${normalizedConversation.id}`\n            );\n            return normalizedConversation;\n          } catch (error) {\n            this.logger.error(\n              `[MessageService] Error normalizing created conversation:`,\n              error\n            );\n            throw new Error('Error processing created conversation');\n          }\n        }),\n        catchError((error) => {\n          this.logger.error(\n            `[MessageService] Error creating conversation with user ${userId}:`,\n            error\n          );\n          return throwError(\n            () => new Error(`Failed to create conversation: ${error.message}`)\n          );\n        })\n      );\n  }\n\n  /**\n   * Récupère une conversation existante ou en crée une nouvelle si elle n'existe pas\n   * @param userId ID de l'utilisateur avec qui créer/récupérer une conversation\n   * @returns Observable avec la conversation\n   */\n  getOrCreateConversation(userId: string): Observable<Conversation> {\n    this.logger.info(\n      `[MessageService] Getting or creating conversation with user: ${userId}`\n    );\n\n    if (!userId) {\n      this.logger.error(\n        `[MessageService] Cannot get/create conversation: userId is undefined`\n      );\n      return throwError(\n        () => new Error('User ID is required to get/create a conversation')\n      );\n    }\n\n    // D'abord, essayons de trouver une conversation existante entre les deux utilisateurs\n    return this.getConversations().pipe(\n      map((conversations) => {\n        // Récupérer l'ID de l'utilisateur actuel\n        const currentUserId = this.getCurrentUserId();\n\n        // Chercher une conversation directe (non groupe) entre les deux utilisateurs\n        const existingConversation = conversations.find((conv) => {\n          if (conv.isGroup) return false;\n\n          // Vérifier si la conversation contient les deux utilisateurs\n          const participantIds =\n            conv.participants?.map((p) => p.id || p._id) || [];\n          return (\n            participantIds.includes(userId) &&\n            participantIds.includes(currentUserId)\n          );\n        });\n\n        if (existingConversation) {\n          this.logger.info(\n            `[MessageService] Found existing conversation: ${existingConversation.id}`\n          );\n          return existingConversation;\n        }\n\n        // Si aucune conversation n'est trouvée, en créer une nouvelle\n        throw new Error('No existing conversation found');\n      }),\n      catchError((error) => {\n        this.logger.info(\n          `[MessageService] No existing conversation found, creating new one: ${error.message}`\n        );\n        return this.createConversation(userId);\n      })\n    );\n  }\n\n  // --------------------------------------------------------------------------\n  // Section 2: Méthodes pour les Notifications\n  // --------------------------------------------------------------------------\n  // Propriétés pour la pagination des notifications\n  private notificationPagination = {\n    currentPage: 1,\n    limit: 10,\n    hasMoreNotifications: true,\n  };\n\n  getNotifications(\n    refresh = false,\n    page = 1,\n    limit = 10\n  ): Observable<Notification[]> {\n    this.logger.info(\n      'MessageService',\n      `Fetching notifications, refresh: ${refresh}, page: ${page}, limit: ${limit}`\n    );\n    this.logger.debug('MessageService', 'Using query', {\n      query: GET_NOTIFICATIONS_QUERY,\n    });\n\n    // Si refresh est true, réinitialiser la pagination mais ne pas vider le cache\n    // pour conserver les suppressions locales\n    if (refresh) {\n      this.logger.debug(\n        'MessageService',\n        'Resetting pagination due to refresh'\n      );\n      this.notificationPagination.currentPage = 1;\n      this.notificationPagination.hasMoreNotifications = true;\n    }\n\n    // Mettre à jour les paramètres de pagination\n    this.notificationPagination.currentPage = page;\n    this.notificationPagination.limit = limit;\n\n    // Récupérer les IDs des notifications supprimées du localStorage\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    this.logger.debug(\n      'MessageService',\n      `Found ${deletedNotificationIds.size} deleted notification IDs in localStorage`\n    );\n\n    return this.apollo\n      .watchQuery<getUserNotificationsResponse>({\n        query: GET_NOTIFICATIONS_QUERY,\n        variables: {\n          page: page,\n          limit: limit,\n        },\n        fetchPolicy: refresh ? 'network-only' : 'cache-first',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          this.logger.debug(\n            'MessageService',\n            'Notifications response received'\n          );\n\n          if (result.errors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors:',\n              result.errors\n            );\n            throw new Error(result.errors.map((e) => e.message).join(', '));\n          }\n\n          const notifications = result.data?.getUserNotifications || [];\n          this.logger.debug(\n            'MessageService',\n            `Received ${notifications.length} notifications from server for page ${page}`\n          );\n\n          // Vérifier s'il y a plus de notifications à charger\n          this.notificationPagination.hasMoreNotifications =\n            notifications.length >= limit;\n\n          if (notifications.length === 0) {\n            this.logger.info(\n              'MessageService',\n              'No notifications received from server'\n            );\n            this.notificationPagination.hasMoreNotifications = false;\n          }\n\n          // Filtrer les notifications supprimées\n          const filteredNotifications = notifications.filter(\n            (notif) => !deletedNotificationIds.has(notif.id)\n          );\n\n          this.logger.debug(\n            'MessageService',\n            `Filtered out ${\n              notifications.length - filteredNotifications.length\n            } deleted notifications`\n          );\n\n          // Afficher les notifications reçues pour le débogage\n          filteredNotifications.forEach((notif, index) => {\n            console.log(`Notification ${index + 1} (page ${page}):`, {\n              id: notif.id || (notif as any)._id,\n              type: notif.type,\n              content: notif.content,\n              isRead: notif.isRead,\n            });\n          });\n\n          // Vérifier si les notifications existent déjà dans le cache avant de les ajouter\n          // Mettre à jour le cache avec les nouvelles notifications\n          this.updateCache(filteredNotifications);\n\n          // Récupérer toutes les notifications du cache\n          const cachedNotifications = Array.from(\n            this.notificationCache.values()\n          );\n\n          console.log(\n            `Total notifications in cache after update: ${cachedNotifications.length}`\n          );\n\n          // Mettre à jour le BehaviorSubject avec toutes les notifications\n          this.notifications.next(cachedNotifications);\n\n          // Mettre à jour le compteur de notifications non lues\n          this.updateUnreadCount();\n\n          // Sauvegarder les notifications dans le localStorage\n          this.saveNotificationsToLocalStorage();\n\n          return cachedNotifications;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error loading notifications:',\n            error\n          );\n\n          if (error.graphQLErrors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors:',\n              error.graphQLErrors\n            );\n          }\n\n          if (error.networkError) {\n            this.logger.error(\n              'MessageService',\n              'Network error:',\n              error.networkError\n            );\n          }\n\n          return throwError(() => new Error('Failed to load notifications'));\n        })\n      );\n  }\n\n  /**\n   * Récupère les IDs des notifications supprimées du localStorage\n   * @private\n   * @returns Set contenant les IDs des notifications supprimées\n   */\n  private getDeletedNotificationIds(): Set<string> {\n    try {\n      const deletedIds = new Set<string>();\n      const savedNotifications = localStorage.getItem('notifications');\n\n      // Si aucune notification n'est sauvegardée, retourner un ensemble vide\n      if (!savedNotifications) {\n        return deletedIds;\n      }\n\n      // Récupérer les IDs des notifications sauvegardées\n      const savedNotificationIds = new Set(\n        JSON.parse(savedNotifications).map((n: Notification) => n.id)\n      );\n\n      // Récupérer les notifications du serveur (si disponibles dans le cache Apollo)\n      const serverNotifications =\n        this.apollo.client.readQuery<getUserNotificationsResponse>({\n          query: GET_NOTIFICATIONS_QUERY,\n        })?.getUserNotifications || [];\n\n      // Pour chaque notification du serveur, vérifier si elle est dans les notifications sauvegardées\n      serverNotifications.forEach((notification) => {\n        if (!savedNotificationIds.has(notification.id)) {\n          deletedIds.add(notification.id);\n        }\n      });\n\n      return deletedIds;\n    } catch (error) {\n      this.logger.error(\n        'MessageService',\n        'Erreur lors de la récupération des IDs de notifications supprimées:',\n        error\n      );\n      return new Set<string>();\n    }\n  }\n\n  // Méthode pour vérifier s'il y a plus de notifications à charger\n  hasMoreNotifications(): boolean {\n    return this.notificationPagination.hasMoreNotifications;\n  }\n\n  // Méthode pour charger la page suivante de notifications\n  loadMoreNotifications(): Observable<Notification[]> {\n    const nextPage = this.notificationPagination.currentPage + 1;\n    return this.getNotifications(\n      false,\n      nextPage,\n      this.notificationPagination.limit\n    );\n  }\n  getNotificationById(id: string): Observable<Notification | undefined> {\n    return this.notifications$.pipe(\n      map((notifications) => notifications.find((n) => n.id === id)),\n      catchError((error) => {\n        this.logger.error('Error finding notification:', error);\n        return throwError(() => new Error('Failed to find notification'));\n      })\n    );\n  }\n  getNotificationCount(): number {\n    return this.notifications.value?.length || 0;\n  }\n  getNotificationAttachments(notificationId: string): Observable<Attachment[]> {\n    return this.apollo\n      .query<getNotificationAttachmentsEvent>({\n        query: GET_NOTIFICATIONS_ATTACHAMENTS,\n        variables: { id: notificationId },\n        fetchPolicy: 'network-only',\n      })\n      .pipe(\n        map((result) => result.data?.getNotificationAttachments || []),\n        catchError((error) => {\n          this.logger.error('Error fetching notification attachments:', error);\n          return throwError(() => new Error('Failed to fetch attachments'));\n        })\n      );\n  }\n  getUnreadNotifications(): Observable<Notification[]> {\n    return this.notifications$.pipe(\n      map((notifications) => notifications.filter((n) => !n.isRead))\n    );\n  }\n\n  /**\n   * Supprime une notification\n   * @param notificationId ID de la notification à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteNotification(\n    notificationId: string\n  ): Observable<{ success: boolean; message: string }> {\n    this.logger.debug(\n      'MessageService',\n      `Suppression de la notification ${notificationId}`\n    );\n\n    if (!notificationId) {\n      this.logger.warn('MessageService', 'ID de notification invalide');\n      return throwError(() => new Error('ID de notification invalide'));\n    }\n\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    this.notificationCache.delete(notificationId);\n    this.notifications.next(Array.from(this.notificationCache.values()));\n    this.updateUnreadCount();\n    this.saveNotificationsToLocalStorage();\n\n    // Appeler le backend pour supprimer la notification\n    return this.apollo\n      .mutate<{ deleteNotification: { success: boolean; message: string } }>({\n        mutation: DELETE_NOTIFICATION_MUTATION,\n        variables: { notificationId },\n      })\n      .pipe(\n        map((result) => {\n          const response = result.data?.deleteNotification;\n          if (!response) {\n            throw new Error('Réponse de suppression invalide');\n          }\n\n          this.logger.debug(\n            'MessageService',\n            'Résultat de la suppression:',\n            response\n          );\n\n          return response;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Erreur lors de la suppression de la notification:',\n            error\n          );\n\n          // En cas d'erreur, on garde la suppression locale\n          return of({\n            success: true,\n            message: 'Notification supprimée localement (erreur serveur)',\n          });\n        })\n      );\n  }\n\n  /**\n   * Sauvegarde les notifications dans le localStorage\n   * @private\n   */\n  private saveNotificationsToLocalStorage(): void {\n    try {\n      const notifications = Array.from(this.notificationCache.values());\n      localStorage.setItem('notifications', JSON.stringify(notifications));\n      this.logger.debug(\n        'MessageService',\n        'Notifications sauvegardées localement'\n      );\n    } catch (error) {\n      this.logger.error(\n        'MessageService',\n        'Erreur lors de la sauvegarde des notifications:',\n        error\n      );\n    }\n  }\n\n  /**\n   * Supprime toutes les notifications de l'utilisateur\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteAllNotifications(): Observable<{\n    success: boolean;\n    count: number;\n    message: string;\n  }> {\n    this.logger.debug(\n      'MessageService',\n      'Suppression de toutes les notifications'\n    );\n\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const count = this.notificationCache.size;\n    this.notificationCache.clear();\n    this.notifications.next([]);\n    this.notificationCount.next(0);\n    this.saveNotificationsToLocalStorage();\n\n    // Appeler le backend pour supprimer toutes les notifications\n    return this.apollo\n      .mutate<{\n        deleteAllNotifications: {\n          success: boolean;\n          count: number;\n          message: string;\n        };\n      }>({\n        mutation: DELETE_ALL_NOTIFICATIONS_MUTATION,\n      })\n      .pipe(\n        map((result) => {\n          const response = result.data?.deleteAllNotifications;\n          if (!response) {\n            throw new Error('Réponse de suppression invalide');\n          }\n\n          this.logger.debug(\n            'MessageService',\n            'Résultat de la suppression de toutes les notifications:',\n            response\n          );\n\n          return response;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Erreur lors de la suppression de toutes les notifications:',\n            error\n          );\n\n          // En cas d'erreur, on garde la suppression locale\n          return of({\n            success: true,\n            count,\n            message: `${count} notifications supprimées localement (erreur serveur)`,\n          });\n        })\n      );\n  }\n\n  /**\n   * Supprime plusieurs notifications\n   * @param notificationIds IDs des notifications à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteMultipleNotifications(\n    notificationIds: string[]\n  ): Observable<{ success: boolean; count: number; message: string }> {\n    this.logger.debug(\n      'MessageService',\n      `Suppression de ${notificationIds.length} notifications`\n    );\n\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'Aucun ID de notification fourni');\n      return throwError(() => new Error('Aucun ID de notification fourni'));\n    }\n\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    let count = 0;\n    notificationIds.forEach((id) => {\n      if (this.notificationCache.has(id)) {\n        this.notificationCache.delete(id);\n        count++;\n      }\n    });\n\n    this.notifications.next(Array.from(this.notificationCache.values()));\n    this.updateUnreadCount();\n    this.saveNotificationsToLocalStorage();\n\n    // Appeler le backend pour supprimer les notifications\n    return this.apollo\n      .mutate<{\n        deleteMultipleNotifications: {\n          success: boolean;\n          count: number;\n          message: string;\n        };\n      }>({\n        mutation: DELETE_MULTIPLE_NOTIFICATIONS_MUTATION,\n        variables: { notificationIds },\n      })\n      .pipe(\n        map((result) => {\n          const response = result.data?.deleteMultipleNotifications;\n          if (!response) {\n            throw new Error('Réponse de suppression invalide');\n          }\n\n          this.logger.debug(\n            'MessageService',\n            'Résultat de la suppression multiple:',\n            response\n          );\n\n          return response;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Erreur lors de la suppression multiple de notifications:',\n            error\n          );\n\n          // En cas d'erreur, on garde la suppression locale\n          return of({\n            success: count > 0,\n            count,\n            message: `${count} notifications supprimées localement (erreur serveur)`,\n          });\n        })\n      );\n  }\n  groupNotificationsByType(): Observable<\n    Map<NotificationType, Notification[]>\n  > {\n    return this.notifications$.pipe(\n      map((notifications) => {\n        const groups = new Map<NotificationType, Notification[]>();\n        notifications.forEach((notif) => {\n          if (!groups.has(notif.type)) {\n            groups.set(notif.type, []);\n          }\n          groups.get(notif.type)?.push(notif);\n        });\n        return groups;\n      })\n    );\n  }\n  markAsRead(notificationIds: string[]): Observable<{\n    success: boolean;\n    readCount: number;\n    remainingCount: number;\n  }> {\n    this.logger.debug(\n      'MessageService',\n      `Marking notifications as read: ${notificationIds?.join(', ') || 'none'}`\n    );\n\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'No notification IDs provided');\n      return of({\n        success: false,\n        readCount: 0,\n        remainingCount: this.notificationCount.value,\n      });\n    }\n\n    // Vérifier que tous les IDs sont valides\n    const validIds = notificationIds.filter(\n      (id) => id && typeof id === 'string' && id.trim() !== ''\n    );\n\n    if (validIds.length !== notificationIds.length) {\n      this.logger.error('MessageService', 'Some notification IDs are invalid', {\n        provided: notificationIds,\n        valid: validIds,\n      });\n      return throwError(() => new Error('Some notification IDs are invalid'));\n    }\n\n    this.logger.debug(\n      'MessageService',\n      'Sending mutation to mark notifications as read',\n      validIds\n    );\n\n    // Mettre à jour localement d'abord pour une meilleure expérience utilisateur\n    this.updateNotificationStatus(validIds, true);\n\n    // Créer une réponse optimiste\n    const optimisticResponse = {\n      markNotificationsAsRead: {\n        success: true,\n        readCount: validIds.length,\n        remainingCount: Math.max(\n          0,\n          this.notificationCount.value - validIds.length\n        ),\n      },\n    };\n\n    // Afficher des informations de débogage supplémentaires\n    console.log('Sending markNotificationsAsRead mutation with variables:', {\n      notificationIds: validIds,\n    });\n    console.log('Using mutation:', MARK_NOTIFICATION_READ_MUTATION);\n\n    return this.apollo\n      .mutate<MarkNotificationsAsReadResponse>({\n        mutation: MARK_NOTIFICATION_READ_MUTATION,\n        variables: { notificationIds: validIds },\n        optimisticResponse: optimisticResponse,\n        errorPolicy: 'all', // Continuer même en cas d'erreur\n        fetchPolicy: 'no-cache', // Ne pas utiliser le cache pour cette mutation\n      })\n      .pipe(\n        map((result) => {\n          this.logger.debug('MessageService', 'Mutation result', result);\n          console.log('Mutation result:', result);\n\n          // Si nous avons des erreurs GraphQL, les logger mais continuer\n          if (result.errors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors:',\n              result.errors\n            );\n            console.error('GraphQL errors:', result.errors);\n          }\n\n          // Utiliser la réponse du serveur ou notre réponse optimiste\n          const response =\n            result.data?.markNotificationsAsRead ??\n            optimisticResponse.markNotificationsAsRead;\n\n          return response;\n        }),\n        catchError((error: Error) => {\n          this.logger.error(\n            'MessageService',\n            'Error marking notifications as read:',\n            error\n          );\n          console.error('Error in markAsRead:', error);\n\n          // En cas d'erreur, retourner quand même un succès simulé\n          // puisque nous avons déjà mis à jour l'interface utilisateur\n          return of({\n            success: true,\n            readCount: validIds.length,\n            remainingCount: Math.max(\n              0,\n              this.notificationCount.value - validIds.length\n            ),\n          });\n        })\n      );\n  }\n  // --------------------------------------------------------------------------\n  // Section 3: Méthodes pour les Appels\n  // --------------------------------------------------------------------------\n\n  /**\n   * Initie un appel avec un autre utilisateur\n   * @param recipientId ID de l'utilisateur à appeler\n   * @param callType Type d'appel (audio, vidéo)\n   * @param conversationId ID de la conversation (optionnel)\n   * @param options Options d'appel (optionnel)\n   * @returns Observable avec les informations de l'appel\n   */\n  initiateCall(\n    recipientId: string,\n    callType: CallType,\n    conversationId?: string,\n    options?: CallOptions\n  ): Observable<Call> {\n    return this.setupMediaDevices(callType).pipe(\n      switchMap((stream) => {\n        this.localStream = stream;\n        this.localStream$.next(stream);\n\n        // Créer une connexion peer\n        this.peerConnection = new RTCPeerConnection(this.rtcConfig);\n\n        // Ajouter les pistes audio/vidéo\n        stream.getTracks().forEach((track) => {\n          this.peerConnection!.addTrack(track, stream);\n        });\n\n        // Écouter les candidats ICE\n        this.peerConnection.onicecandidate = (event) => {\n          if (event.candidate) {\n            this.sendCallSignal(\n              this.generateCallId(),\n              'ice-candidate',\n              JSON.stringify(event.candidate)\n            );\n          }\n        };\n\n        // Écouter les pistes distantes\n        this.peerConnection.ontrack = (event) => {\n          if (!this.remoteStream) {\n            this.remoteStream = new MediaStream();\n            this.remoteStream$.next(this.remoteStream);\n          }\n          event.streams[0].getTracks().forEach((track) => {\n            this.remoteStream!.addTrack(track);\n          });\n        };\n\n        // Créer l'offre SDP\n        return from(this.peerConnection.createOffer()).pipe(\n          switchMap((offer) => {\n            return from(this.peerConnection!.setLocalDescription(offer)).pipe(\n              map(() => offer)\n            );\n          })\n        );\n      }),\n      switchMap((offer) => {\n        // Générer un ID d'appel unique\n        const callId = this.generateCallId();\n\n        // Envoyer l'offre au serveur\n        return this.apollo\n          .mutate<{ initiateCall: Call }>({\n            mutation: INITIATE_CALL_MUTATION,\n            variables: {\n              recipientId,\n              callType,\n              callId,\n              offer: JSON.stringify(offer),\n              conversationId,\n              options,\n            },\n          })\n          .pipe(\n            map((result) => {\n              const call = result.data?.initiateCall;\n              if (!call) {\n                throw new Error('Failed to initiate call');\n              }\n\n              // Mettre à jour l'état de l'appel actif\n              this.activeCall.next(call);\n\n              // S'abonner aux signaux d'appel\n              const signalSub = this.subscribeToCallSignals(\n                call.id\n              ).subscribe();\n              this.subscriptions.push(signalSub);\n\n              return call;\n            })\n          );\n      }),\n      catchError((error) => {\n        this.logger.error('Error initiating call', error);\n        this.cleanupCall();\n        return throwError(() => new Error('Failed to initiate call'));\n      })\n    );\n  }\n\n  /**\n   * Accepte un appel entrant\n   * @param incomingCall Appel entrant à accepter\n   * @returns Observable avec les informations de l'appel\n   */\n  acceptCall(incomingCall: IncomingCall): Observable<Call> {\n    this.stop('ringtone');\n\n    return this.setupMediaDevices(incomingCall.type).pipe(\n      switchMap((stream) => {\n        this.localStream = stream;\n        this.localStream$.next(stream);\n\n        // Créer une connexion peer\n        this.peerConnection = new RTCPeerConnection(this.rtcConfig);\n\n        // Ajouter les pistes audio/vidéo\n        stream.getTracks().forEach((track) => {\n          this.peerConnection!.addTrack(track, stream);\n        });\n\n        // Écouter les candidats ICE\n        this.peerConnection.onicecandidate = (event) => {\n          if (event.candidate) {\n            this.sendCallSignal(\n              incomingCall.id,\n              'ice-candidate',\n              JSON.stringify(event.candidate)\n            );\n          }\n        };\n\n        // Écouter les pistes distantes\n        this.peerConnection.ontrack = (event) => {\n          if (!this.remoteStream) {\n            this.remoteStream = new MediaStream();\n            this.remoteStream$.next(this.remoteStream);\n          }\n          event.streams[0].getTracks().forEach((track) => {\n            this.remoteStream!.addTrack(track);\n          });\n        };\n\n        // Définir l'offre distante\n        const offer = JSON.parse(incomingCall.offer);\n        return from(\n          this.peerConnection.setRemoteDescription(\n            new RTCSessionDescription(offer)\n          )\n        ).pipe(\n          switchMap(() => from(this.peerConnection!.createAnswer())),\n          switchMap((answer) => {\n            return from(this.peerConnection!.setLocalDescription(answer)).pipe(\n              map(() => answer)\n            );\n          })\n        );\n      }),\n      switchMap((answer) => {\n        // Envoyer la réponse au serveur\n        return this.apollo\n          .mutate<{ acceptCall: Call }>({\n            mutation: ACCEPT_CALL_MUTATION,\n            variables: {\n              callId: incomingCall.id,\n              answer: JSON.stringify(answer),\n            },\n          })\n          .pipe(\n            map((result) => {\n              const call = result.data?.acceptCall;\n              if (!call) {\n                throw new Error('Failed to accept call');\n              }\n\n              // Jouer le son de connexion\n              this.play('call-connected');\n\n              // Mettre à jour l'état de l'appel actif\n              this.activeCall.next({\n                ...call,\n                caller: incomingCall.caller,\n                type: incomingCall.type,\n                conversationId: incomingCall.conversationId,\n              });\n\n              // S'abonner aux signaux d'appel\n              const signalSub = this.subscribeToCallSignals(\n                incomingCall.id\n              ).subscribe();\n              this.subscriptions.push(signalSub);\n\n              // Effacer l'appel entrant\n              this.incomingCall.next(null);\n\n              return call;\n            })\n          );\n      }),\n      catchError((error) => {\n        this.logger.error('Error accepting call', error);\n        this.cleanupCall();\n        return throwError(() => new Error('Failed to accept call'));\n      })\n    );\n  }\n\n  /**\n   * Rejette un appel entrant\n   * @param callId ID de l'appel à rejeter\n   * @param reason Raison du rejet (optionnel)\n   * @returns Observable avec les informations de l'appel\n   */\n  rejectCall(callId: string, reason?: string): Observable<Call> {\n    this.stop('ringtone');\n\n    return this.apollo\n      .mutate<{ rejectCall: Call }>({\n        mutation: REJECT_CALL_MUTATION,\n        variables: {\n          callId,\n          reason,\n        },\n      })\n      .pipe(\n        map((result) => {\n          const call = result.data?.rejectCall;\n          if (!call) {\n            throw new Error('Failed to reject call');\n          }\n\n          // Effacer l'appel entrant\n          this.incomingCall.next(null);\n\n          return call;\n        }),\n        catchError((error) => {\n          this.logger.error('Error rejecting call', error);\n          return throwError(() => new Error('Failed to reject call'));\n        })\n      );\n  }\n\n  /**\n   * Termine un appel en cours\n   * @param callId ID de l'appel à terminer\n   * @param feedback Commentaires sur l'appel (optionnel)\n   * @returns Observable avec les informations de l'appel\n   */\n  endCall(callId: string, feedback?: CallFeedback): Observable<Call> {\n    this.stop('ringtone');\n    this.play('call-end');\n\n    return this.apollo\n      .mutate<{ endCall: Call }>({\n        mutation: END_CALL_MUTATION,\n        variables: {\n          callId,\n          feedback,\n        },\n      })\n      .pipe(\n        map((result) => {\n          const call = result.data?.endCall;\n          if (!call) {\n            throw new Error('Failed to end call');\n          }\n\n          // Nettoyer les ressources\n          this.cleanupCall();\n\n          // Mettre à jour l'état de l'appel actif\n          this.activeCall.next(null);\n\n          return call;\n        }),\n        catchError((error) => {\n          this.logger.error('Error ending call', error);\n          this.cleanupCall();\n          return throwError(() => new Error('Failed to end call'));\n        })\n      );\n  }\n\n  /**\n   * Active ou désactive la caméra ou le micro\n   * @param callId ID de l'appel\n   * @param video État de la caméra (optionnel)\n   * @param audio État du micro (optionnel)\n   * @returns Observable avec le résultat de l'opération\n   */\n  toggleMedia(\n    callId: string,\n    video?: boolean,\n    audio?: boolean\n  ): Observable<CallSuccess> {\n    if (this.localStream) {\n      // Mettre à jour les pistes locales\n      if (video !== undefined) {\n        this.localStream.getVideoTracks().forEach((track) => {\n          track.enabled = video;\n        });\n      }\n\n      if (audio !== undefined) {\n        this.localStream.getAudioTracks().forEach((track) => {\n          track.enabled = audio;\n        });\n      }\n    }\n\n    return this.apollo\n      .mutate<{ toggleCallMedia: CallSuccess }>({\n        mutation: TOGGLE_CALL_MEDIA_MUTATION,\n        variables: {\n          callId,\n          video,\n          audio,\n        },\n      })\n      .pipe(\n        map((result) => {\n          const success = result.data?.toggleCallMedia;\n          if (!success) {\n            throw new Error('Failed to toggle media');\n          }\n          return success;\n        }),\n        catchError((error) => {\n          this.logger.error('Error toggling media', error);\n          return throwError(() => new Error('Failed to toggle media'));\n        })\n      );\n  }\n\n  /**\n   * S'abonne aux signaux d'appel\n   * @param callId ID de l'appel\n   * @returns Observable avec les signaux d'appel\n   */\n  subscribeToCallSignals(callId: string): Observable<CallSignal> {\n    return this.apollo\n      .subscribe<{ callSignal: CallSignal }>({\n        query: CALL_SIGNAL_SUBSCRIPTION,\n        variables: { callId },\n      })\n      .pipe(\n        map(({ data }) => {\n          if (!data?.callSignal) {\n            throw new Error('No call signal received');\n          }\n          return data.callSignal;\n        }),\n        tap((signal) => {\n          this.callSignals.next(signal);\n          this.handleCallSignal(signal);\n        }),\n        catchError((error) => {\n          this.logger.error('Error in call signal subscription', error);\n          return throwError(() => new Error('Call signal subscription failed'));\n        })\n      );\n  }\n\n  /**\n   * Envoie un signal d'appel\n   * @param callId ID de l'appel\n   * @param signalType Type de signal\n   * @param signalData Données du signal\n   * @returns Observable avec le résultat de l'opération\n   */\n  sendCallSignal(\n    callId: string,\n    signalType: string,\n    signalData: string\n  ): Observable<CallSuccess> {\n    return this.apollo\n      .mutate<{ sendCallSignal: CallSuccess }>({\n        mutation: SEND_CALL_SIGNAL_MUTATION,\n        variables: {\n          callId,\n          signalType,\n          signalData,\n        },\n      })\n      .pipe(\n        map((result) => {\n          const success = result.data?.sendCallSignal;\n          if (!success) {\n            throw new Error('Failed to send call signal');\n          }\n          return success;\n        }),\n        catchError((error) => {\n          this.logger.error('Error sending call signal', error);\n          return throwError(() => new Error('Failed to send call signal'));\n        })\n      );\n  }\n\n  /**\n   * Récupère l'historique des appels avec filtres\n   * @param limit Nombre d'appels à récupérer\n   * @param offset Décalage pour la pagination\n   * @param status Filtres de statut\n   * @param type Filtres de type\n   * @param startDate Date de début\n   * @param endDate Date de fin\n   * @returns Observable avec l'historique des appels\n   */\n  getCallHistory(\n    limit: number = 20,\n    offset: number = 0,\n    status?: string[],\n    type?: string[],\n    startDate?: string | null,\n    endDate?: string | null\n  ): Observable<Call[]> {\n    return this.apollo\n      .watchQuery<{ callHistory: Call[] }>({\n        query: CALL_HISTORY_QUERY,\n        variables: {\n          limit,\n          offset,\n          status,\n          type,\n          startDate,\n          endDate,\n        },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const history = result.data?.callHistory || [];\n          this.logger.debug(`Retrieved ${history.length} call history items`);\n          return history;\n        }),\n        catchError((error) => {\n          this.logger.error('Error fetching call history:', error);\n          return throwError(() => new Error('Failed to fetch call history'));\n        })\n      );\n  }\n\n  /**\n   * Récupère les détails d'un appel spécifique\n   * @param callId ID de l'appel\n   * @returns Observable avec les détails de l'appel\n   */\n  getCallDetails(callId: string): Observable<Call> {\n    return this.apollo\n      .watchQuery<{ callDetails: Call }>({\n        query: CALL_DETAILS_QUERY,\n        variables: { callId },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const details = result.data?.callDetails;\n          if (!details) {\n            throw new Error('Call details not found');\n          }\n          this.logger.debug(`Retrieved call details for: ${callId}`);\n          return details;\n        }),\n        catchError((error) => {\n          this.logger.error('Error fetching call details:', error);\n          return throwError(() => new Error('Failed to fetch call details'));\n        })\n      );\n  }\n\n  /**\n   * Récupère les statistiques d'appels\n   * @returns Observable avec les statistiques d'appels\n   */\n  getCallStats(): Observable<any> {\n    return this.apollo\n      .watchQuery<{ callStats: any }>({\n        query: CALL_STATS_QUERY,\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const stats = result.data?.callStats;\n          if (!stats) {\n            throw new Error('Call stats not found');\n          }\n          this.logger.debug('Retrieved call stats:', stats);\n          return stats;\n        }),\n        catchError((error) => {\n          this.logger.error('Error fetching call stats:', error);\n          return throwError(() => new Error('Failed to fetch call stats'));\n        })\n      );\n  }\n\n  /**\n   * Gère un signal d'appel reçu\n   * @param signal Signal d'appel\n   */\n  private handleCallSignal(signal: CallSignal): void {\n    switch (signal.type) {\n      case 'ice-candidate':\n        this.handleIceCandidate(signal);\n        break;\n      case 'answer':\n        this.handleAnswer(signal);\n        break;\n      case 'end-call':\n        this.handleEndCall(signal);\n        break;\n      case 'reject':\n        this.handleRejectCall(signal);\n        break;\n      default:\n        this.logger.debug(`Unhandled signal type: ${signal.type}`, signal);\n    }\n  }\n\n  /**\n   * Gère un candidat ICE reçu\n   * @param signal Signal d'appel contenant un candidat ICE\n   */\n  private handleIceCandidate(signal: CallSignal): void {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for ICE candidate');\n      return;\n    }\n\n    try {\n      const candidate = JSON.parse(signal.data);\n      this.peerConnection\n        .addIceCandidate(new RTCIceCandidate(candidate))\n        .catch((error) => {\n          this.logger.error('Error adding ICE candidate', error as Error);\n        });\n    } catch (error) {\n      this.logger.error('Error parsing ICE candidate', error as Error);\n    }\n  }\n\n  /**\n   * Gère une réponse SDP reçue\n   * @param signal Signal d'appel contenant une réponse SDP\n   */\n  private handleAnswer(signal: CallSignal): void {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for answer');\n      return;\n    }\n\n    try {\n      const answer = JSON.parse(signal.data);\n      this.peerConnection\n        .setRemoteDescription(new RTCSessionDescription(answer))\n        .catch((error) => {\n          this.logger.error('Error setting remote description', error as Error);\n        });\n    } catch (error) {\n      this.logger.error('Error parsing answer', error as Error);\n    }\n  }\n\n  /**\n   * Gère la fin d'un appel\n   * @param signal Signal d'appel indiquant la fin de l'appel\n   */\n  private handleEndCall(signal: CallSignal): void {\n    this.stop('ringtone');\n    this.cleanupCall();\n\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.ENDED,\n        endTime: new Date().toISOString(),\n      });\n    }\n  }\n\n  /**\n   * Gère le rejet d'un appel\n   * @param signal Signal d'appel indiquant le rejet de l'appel\n   */\n  private handleRejectCall(signal: CallSignal): void {\n    this.stop('ringtone');\n    this.cleanupCall();\n\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.REJECTED,\n        endTime: new Date().toISOString(),\n      });\n    }\n  }\n\n  /**\n   * Nettoie les ressources d'appel\n   */\n  private cleanupCall(): void {\n    if (this.localStream) {\n      this.localStream.getTracks().forEach((track) => track.stop());\n      this.localStream = null;\n      this.localStream$.next(null);\n    }\n\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n\n    this.remoteStream = null;\n    this.remoteStream$.next(null);\n  }\n\n  /**\n   * Configure les périphériques média pour un appel\n   * @param callType Type d'appel (audio, vidéo)\n   * @returns Observable avec le flux média\n   */\n  private setupMediaDevices(callType: CallType): Observable<MediaStream> {\n    const constraints: MediaStreamConstraints = {\n      audio: true,\n      video:\n        callType !== CallType.AUDIO\n          ? {\n              width: { ideal: 1280 },\n              height: { ideal: 720 },\n            }\n          : false,\n    };\n\n    return new Observable<MediaStream>((observer) => {\n      navigator.mediaDevices\n        .getUserMedia(constraints)\n        .then((stream) => {\n          observer.next(stream);\n          observer.complete();\n        })\n        .catch((error) => {\n          this.logger.error('Error accessing media devices', error);\n          observer.error(new Error('Failed to access media devices'));\n        });\n    });\n  }\n\n  /**\n   * Génère un ID d'appel unique\n   * @returns ID d'appel unique\n   */\n  private generateCallId(): string {\n    return Date.now().toString() + Math.random().toString(36).substring(2, 9);\n  }\n\n  // --------------------------------------------------------------------------\n  // Section 4: Méthodes pour les Utilisateurs/Groupes\n  // --------------------------------------------------------------------------\n  // User methods\n  getAllUsers(\n    forceRefresh = false,\n    search?: string,\n    page: number = 1,\n    limit: number = 10,\n    sortBy: string = 'username',\n    sortOrder: string = 'asc',\n    isOnline?: boolean\n  ): Observable<User[]> {\n    this.logger.info(\n      'MessageService',\n      `Getting users with params: forceRefresh=${forceRefresh}, search=${\n        search || '(empty)'\n      }, page=${page}, limit=${limit}, sortBy=${sortBy}, sortOrder=${sortOrder}, isOnline=${isOnline}`\n    );\n\n    const now = Date.now();\n    const cacheValid =\n      !forceRefresh &&\n      this.usersCache.length > 0 &&\n      now - this.lastFetchTime <= this.CACHE_DURATION &&\n      !search &&\n      page === 1 &&\n      limit >= this.usersCache.length;\n\n    // Use cache only for first page with no filters\n    if (cacheValid) {\n      this.logger.debug(\n        'MessageService',\n        `Using cached users (${this.usersCache.length} users)`\n      );\n      return of([...this.usersCache]);\n    }\n\n    this.logger.debug(\n      'MessageService',\n      `Fetching users from server with pagination, fetchPolicy=${\n        forceRefresh ? 'network-only' : 'cache-first'\n      }`\n    );\n\n    return this.apollo\n      .watchQuery<any>({\n        query: GET_ALL_USER_QUERY,\n        variables: {\n          search,\n          page,\n          limit,\n          sortBy,\n          sortOrder,\n          isOnline: isOnline !== undefined ? isOnline : null,\n        },\n        fetchPolicy: forceRefresh ? 'network-only' : 'cache-first',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          this.logger.debug(\n            'MessageService',\n            'Users response received',\n            result\n          );\n\n          if (result.errors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors in getAllUsers:',\n              result.errors\n            );\n            throw new Error(result.errors.map((e) => e.message).join(', '));\n          }\n\n          if (!result.data?.getAllUsers) {\n            this.logger.warn(\n              'MessageService',\n              'No users data received from server'\n            );\n            return [];\n          }\n\n          const paginatedResponse = result.data.getAllUsers;\n\n          // Log pagination metadata\n          this.logger.debug('MessageService', 'Pagination metadata:', {\n            totalCount: paginatedResponse.totalCount,\n            totalPages: paginatedResponse.totalPages,\n            currentPage: paginatedResponse.currentPage,\n            hasNextPage: paginatedResponse.hasNextPage,\n            hasPreviousPage: paginatedResponse.hasPreviousPage,\n          });\n\n          // Normalize users with error handling\n          const users: User[] = [];\n          for (const user of paginatedResponse.users) {\n            try {\n              if (user) {\n                users.push(this.normalizeUser(user));\n              }\n            } catch (error) {\n              this.logger.warn(\n                'MessageService',\n                `Error normalizing user, skipping:`,\n                error\n              );\n            }\n          }\n\n          this.logger.info(\n            'MessageService',\n            `Received ${users.length} users from server (page ${paginatedResponse.currentPage} of ${paginatedResponse.totalPages})`\n          );\n\n          // Update cache only for first page with no filters\n          if (!search && page === 1 && !isOnline) {\n            this.usersCache = [...users];\n            this.lastFetchTime = Date.now();\n            this.logger.debug(\n              'MessageService',\n              `User cache updated with ${users.length} users`\n            );\n          }\n\n          // Store pagination metadata in a property for component access\n          this.currentUserPagination = {\n            totalCount: paginatedResponse.totalCount,\n            totalPages: paginatedResponse.totalPages,\n            currentPage: paginatedResponse.currentPage,\n            hasNextPage: paginatedResponse.hasNextPage,\n            hasPreviousPage: paginatedResponse.hasPreviousPage,\n          };\n\n          return users;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error fetching users:', error);\n\n          if (error.graphQLErrors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors:',\n              error.graphQLErrors\n            );\n          }\n\n          if (error.networkError) {\n            this.logger.error(\n              'MessageService',\n              'Network error:',\n              error.networkError\n            );\n          }\n\n          // Return cache if available (only for first page)\n          if (\n            this.usersCache.length > 0 &&\n            page === 1 &&\n            !search &&\n            !isOnline\n          ) {\n            this.logger.warn(\n              'MessageService',\n              `Returning ${this.usersCache.length} cached users due to fetch error`\n            );\n            return of([...this.usersCache]);\n          }\n\n          return throwError(\n            () =>\n              new Error(\n                `Failed to fetch users: ${error.message || 'Unknown error'}`\n              )\n          );\n        })\n      );\n  }\n  getOneUser(userId: string): Observable<User> {\n    return this.apollo\n      .watchQuery<GetOneUserResponse>({\n        query: GET_USER_QUERY,\n        variables: { id: userId },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => this.normalizeUser(result.data?.getOneUser)),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error fetching user:', error);\n          return throwError(() => new Error('Failed to fetch user'));\n        })\n      );\n  }\n  getCurrentUser(): Observable<User> {\n    return this.apollo\n      .watchQuery<getCurrentUserResponse>({\n        query: GET_CURRENT_USER_QUERY,\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => this.normalizeUser(result.data?.getCurrentUser)),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error fetching current user:',\n            error\n          );\n          return throwError(() => new Error('Failed to fetch current user'));\n        })\n      );\n  }\n  setUserOnline(userId: string): Observable<User> {\n    return this.apollo\n      .mutate<SetUserOnlineResponse>({\n        mutation: SET_USER_ONLINE_MUTATION,\n        variables: { userId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.setUserOnline)\n            throw new Error('Failed to set user online');\n          return this.normalizeUser(result.data.setUserOnline);\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error setting user online:',\n            error\n          );\n          return throwError(() => new Error('Failed to set user online'));\n        })\n      );\n  }\n  setUserOffline(userId: string): Observable<User> {\n    return this.apollo\n      .mutate<SetUserOfflineResponse>({\n        mutation: SET_USER_OFFLINE_MUTATION,\n        variables: { userId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.setUserOffline)\n            throw new Error('Failed to set user offline');\n          return this.normalizeUser(result.data.setUserOffline);\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error setting user offline:',\n            error\n          );\n          return throwError(() => new Error('Failed to set user offline'));\n        })\n      );\n  }\n\n  // --------------------------------------------------------------------------\n  // Section: Gestion des Groupes\n  // --------------------------------------------------------------------------\n\n  /**\n   * Crée un nouveau groupe\n   */\n  createGroup(\n    name: string,\n    participantIds: string[],\n    photo?: File,\n    description?: string\n  ): Observable<any> {\n    this.logger.debug(\n      'MessageService',\n      `Creating group: ${name} with ${participantIds.length} participants`\n    );\n\n    if (!name || !participantIds || participantIds.length === 0) {\n      return throwError(\n        () => new Error('Nom du groupe et participants requis')\n      );\n    }\n\n    return this.apollo\n      .mutate({\n        mutation: CREATE_GROUP_MUTATION,\n        variables: { name, participantIds, photo, description },\n      })\n      .pipe(\n        map((result: any) => {\n          const group = result.data?.createGroup;\n          if (!group) {\n            throw new Error('Échec de la création du groupe');\n          }\n          this.logger.info(\n            'MessageService',\n            `Group created successfully: ${group.id}`\n          );\n          return group;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error creating group:', error);\n          return throwError(() => new Error('Échec de la création du groupe'));\n        })\n      );\n  }\n\n  /**\n   * Met à jour un groupe existant\n   */\n  updateGroup(groupId: string, input: any): Observable<any> {\n    this.logger.debug('MessageService', `Updating group: ${groupId}`);\n\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n\n    return this.apollo\n      .mutate({\n        mutation: UPDATE_GROUP_MUTATION,\n        variables: { id: groupId, input },\n      })\n      .pipe(\n        map((result: any) => {\n          const group = result.data?.updateGroup;\n          if (!group) {\n            throw new Error('Échec de la mise à jour du groupe');\n          }\n          this.logger.info(\n            'MessageService',\n            `Group updated successfully: ${group.id}`\n          );\n          return group;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error updating group:', error);\n          return throwError(\n            () => new Error('Échec de la mise à jour du groupe')\n          );\n        })\n      );\n  }\n\n  /**\n   * Supprime un groupe\n   */\n  deleteGroup(\n    groupId: string\n  ): Observable<{ success: boolean; message: string }> {\n    this.logger.debug('MessageService', `Deleting group: ${groupId}`);\n\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n\n    return this.apollo\n      .mutate({\n        mutation: DELETE_GROUP_MUTATION,\n        variables: { id: groupId },\n      })\n      .pipe(\n        map((result: any) => {\n          const response = result.data?.deleteGroup;\n          if (!response) {\n            throw new Error('Échec de la suppression du groupe');\n          }\n          this.logger.info(\n            'MessageService',\n            `Group deleted successfully: ${groupId}`\n          );\n          return response;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error deleting group:', error);\n          return throwError(\n            () => new Error('Échec de la suppression du groupe')\n          );\n        })\n      );\n  }\n\n  /**\n   * Quitte un groupe\n   */\n  leaveGroup(\n    groupId: string\n  ): Observable<{ success: boolean; message: string }> {\n    this.logger.debug('MessageService', `Leaving group: ${groupId}`);\n\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n\n    return this.apollo\n      .mutate({\n        mutation: LEAVE_GROUP_MUTATION,\n        variables: { groupId },\n      })\n      .pipe(\n        map((result: any) => {\n          const response = result.data?.leaveGroup;\n          if (!response) {\n            throw new Error('Échec de la sortie du groupe');\n          }\n          this.logger.info(\n            'MessageService',\n            `Left group successfully: ${groupId}`\n          );\n          return response;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error leaving group:', error);\n          return throwError(() => new Error('Échec de la sortie du groupe'));\n        })\n      );\n  }\n\n  /**\n   * Récupère les informations d'un groupe\n   */\n  getGroup(groupId: string): Observable<any> {\n    this.logger.debug('MessageService', `Getting group: ${groupId}`);\n\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n\n    return this.apollo\n      .query({\n        query: GET_GROUP_QUERY,\n        variables: { id: groupId },\n        fetchPolicy: 'network-only',\n      })\n      .pipe(\n        map((result: any) => {\n          const group = result.data?.getGroup;\n          if (!group) {\n            throw new Error('Groupe non trouvé');\n          }\n          this.logger.info(\n            'MessageService',\n            `Group retrieved successfully: ${groupId}`\n          );\n          return group;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error getting group:', error);\n          return throwError(\n            () => new Error('Échec de la récupération du groupe')\n          );\n        })\n      );\n  }\n\n  /**\n   * Récupère les groupes d'un utilisateur\n   */\n  getUserGroups(userId: string): Observable<any[]> {\n    this.logger.debug('MessageService', `Getting groups for user: ${userId}`);\n\n    if (!userId) {\n      return throwError(() => new Error(\"ID de l'utilisateur requis\"));\n    }\n\n    return this.apollo\n      .query({\n        query: GET_USER_GROUPS_QUERY,\n        variables: { userId },\n        fetchPolicy: 'network-only',\n      })\n      .pipe(\n        map((result: any) => {\n          const groups = result.data?.getUserGroups || [];\n          this.logger.info(\n            'MessageService',\n            `Retrieved ${groups.length} groups for user: ${userId}`\n          );\n          return groups;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error getting user groups:',\n            error\n          );\n          return throwError(\n            () => new Error('Échec de la récupération des groupes')\n          );\n        })\n      );\n  }\n\n  // --------------------------------------------------------------------------\n  // Section 4: Subscriptions et Gestion Temps Réel\n  // --------------------------------------------------------------------------\n  subscribeToNewMessages(conversationId: string): Observable<Message> {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\n        \"Tentative d'abonnement aux messages avec un token invalide ou expiré\"\n      );\n      return of(null as unknown as Message);\n    }\n\n    this.logger.debug(\n      `Démarrage de l'abonnement aux nouveaux messages pour la conversation: ${conversationId}`\n    );\n\n    const sub$ = this.apollo\n      .subscribe<{ messageSent: Message }>({\n        query: MESSAGE_SENT_SUBSCRIPTION,\n        variables: { conversationId },\n      })\n      .pipe(\n        map((result) => {\n          const msg = result.data?.messageSent;\n          if (!msg) {\n            this.logger.warn('No message payload received');\n            throw new Error('No message payload received');\n          }\n\n          // Vérifier que l'ID est présent\n          if (!msg.id && !msg._id) {\n            this.logger.warn('Message without ID received:', msg);\n            // Générer un ID temporaire si nécessaire\n            msg.id = `temp-${Date.now()}`;\n          }\n\n          try {\n            // Utiliser normalizeMessage pour une normalisation complète\n            const normalizedMessage = this.normalizeMessage(msg);\n\n            // Si c'est un message vocal, s'assurer qu'il est correctement traité\n            if (\n              normalizedMessage.type === MessageType.AUDIO ||\n              (normalizedMessage.attachments &&\n                normalizedMessage.attachments.some(\n                  (att) => att.type === 'audio'\n                ))\n            ) {\n              this.logger.debug(\n                'MessageService',\n                'Voice message received in real-time',\n                normalizedMessage\n              );\n\n              // Mettre à jour la conversation avec le nouveau message\n              this.updateConversationWithNewMessage(\n                conversationId,\n                normalizedMessage\n              );\n            }\n\n            return normalizedMessage;\n          } catch (err) {\n            this.logger.error('Error normalizing message:', err);\n\n            // Créer un message minimal mais valide\n            const minimalMessage: Message = {\n              id: msg.id || msg._id || `temp-${Date.now()}`,\n              content: msg.content || '',\n              type: msg.type || MessageType.TEXT,\n              timestamp: this.safeDate(msg.timestamp),\n              isRead: false,\n              sender: msg.sender\n                ? this.normalizeUser(msg.sender)\n                : {\n                    id: this.getCurrentUserId(),\n                    username: 'Unknown',\n                  },\n            };\n\n            return minimalMessage;\n          }\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Message subscription error:',\n            error\n          );\n          // Retourner un observable vide au lieu de null\n          return EMPTY;\n        }),\n        // Filtrer les valeurs null\n        filter((message) => !!message),\n        // Réessayer après un délai en cas d'erreur\n        retry(3)\n      );\n\n    const sub = sub$.subscribe({\n      next: (message) => {\n        // Traitement supplémentaire pour s'assurer que le message est bien affiché\n        this.logger.debug('MessageService', 'New message received:', message);\n\n        // Mettre à jour la conversation avec le nouveau message\n        this.updateConversationWithNewMessage(conversationId, message);\n      },\n      error: (err) => {\n        this.logger.error('Error in message subscription:', err);\n      },\n    });\n\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n\n  /**\n   * Met à jour une conversation avec un nouveau message\n   * @param conversationId ID de la conversation\n   * @param message Nouveau message\n   */\n  private updateConversationWithNewMessage(\n    conversationId: string,\n    message: Message\n  ): void {\n    // Forcer une mise à jour de la conversation en récupérant les données à jour\n    this.getConversation(conversationId).subscribe({\n      next: (conversation) => {\n        this.logger.debug(\n          'MessageService',\n          `Conversation ${conversationId} refreshed with new message ${\n            message.id\n          }, has ${conversation?.messages?.length || 0} messages`\n        );\n\n        // Émettre un événement pour informer les composants que la conversation a été mise à jour\n        this.activeConversation.next(conversationId);\n      },\n      error: (error) => {\n        this.logger.error(\n          'MessageService',\n          `Error refreshing conversation ${conversationId}:`,\n          error\n        );\n      },\n    });\n  }\n  subscribeToUserStatus(): Observable<User> {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\n        \"Tentative d'abonnement au statut utilisateur avec un token invalide ou expiré\"\n      );\n      return throwError(() => new Error('Invalid or expired token'));\n    }\n\n    this.logger.debug(\"Démarrage de l'abonnement au statut utilisateur\");\n\n    const sub$ = this.apollo\n      .subscribe<{ userStatusChanged: User }>({\n        query: USER_STATUS_SUBSCRIPTION,\n      })\n      .pipe(\n        tap((result) =>\n          this.logger.debug(\n            \"Données reçues de l'abonnement au statut utilisateur:\",\n            result\n          )\n        ),\n        map((result) => {\n          const user = result.data?.userStatusChanged;\n          if (!user) {\n            this.logger.error('No status payload received');\n            throw new Error('No status payload received');\n          }\n          return this.normalizeUser(user);\n        }),\n        catchError((error) => {\n          this.logger.error('Status subscription error:', error as Error);\n          return throwError(() => new Error('Status subscription failed'));\n        }),\n        retry(3) // Réessayer 3 fois en cas d'erreur\n      );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToConversationUpdates(\n    conversationId: string\n  ): Observable<Conversation> {\n    const sub$ = this.apollo\n      .subscribe<{ conversationUpdated: Conversation }>({\n        query: CONVERSATION_UPDATED_SUBSCRIPTION,\n        variables: { conversationId },\n      })\n      .pipe(\n        map((result) => {\n          const conv = result.data?.conversationUpdated;\n          if (!conv) throw new Error('No conversation payload received');\n\n          const normalizedConversation: Conversation = {\n            ...conv,\n            participants:\n              conv.participants?.map((p) => this.normalizeUser(p)) || [],\n            lastMessage: conv.lastMessage\n              ? {\n                  ...conv.lastMessage,\n                  sender: this.normalizeUser(conv.lastMessage.sender),\n                  timestamp: this.safeDate(conv.lastMessage.timestamp),\n                  readAt: conv.lastMessage.readAt\n                    ? this.safeDate(conv.lastMessage.readAt)\n                    : undefined,\n                  // Conservez toutes les autres propriétés du message\n                  id: conv.lastMessage.id,\n                  content: conv.lastMessage.content,\n                  type: conv.lastMessage.type,\n                  isRead: conv.lastMessage.isRead,\n                  // ... autres propriétés nécessaires\n                }\n              : null, // On conserve null comme dans votre version originale\n          };\n\n          return normalizedConversation as Conversation; // Assertion de type si nécessaire\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Conversation subscription error:',\n            error\n          );\n          return throwError(\n            () => new Error('Conversation subscription failed')\n          );\n        })\n      );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToTypingIndicator(\n    conversationId: string\n  ): Observable<TypingIndicatorEvent> {\n    const sub$ = this.apollo\n      .subscribe<TypingIndicatorEvents>({\n        query: TYPING_INDICATOR_SUBSCRIPTION,\n        variables: { conversationId },\n      })\n      .pipe(\n        map((result) => result.data?.typingIndicator),\n        filter(Boolean),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Typing indicator subscription error:',\n            error\n          );\n          return throwError(\n            () => new Error('Typing indicator subscription failed')\n          );\n        })\n      );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  private isTokenValid(): boolean {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn('Aucun token trouvé');\n      return false;\n    }\n\n    try {\n      // Décoder le token JWT (format: header.payload.signature)\n      const parts = token.split('.');\n      if (parts.length !== 3) {\n        this.logger.warn('Format de token invalide');\n        return false;\n      }\n\n      // Décoder le payload (deuxième partie du token)\n      const payload = JSON.parse(atob(parts[1]));\n\n      // Vérifier l'expiration\n      if (!payload.exp) {\n        this.logger.warn(\"Token sans date d'expiration\");\n        return false;\n      }\n\n      const expirationDate = new Date(payload.exp * 1000);\n      const now = new Date();\n\n      if (expirationDate < now) {\n        this.logger.warn('Token expiré', {\n          expiration: expirationDate.toISOString(),\n          now: now.toISOString(),\n        });\n        return false;\n      }\n\n      return true;\n    } catch (error) {\n      this.logger.error(\n        'Erreur lors de la vérification du token:',\n        error as Error\n      );\n      return false;\n    }\n  }\n\n  subscribeToNotificationsRead(): Observable<string[]> {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\n        \"Tentative d'abonnement aux notifications avec un token invalide ou expiré\"\n      );\n      return of([]);\n    }\n\n    this.logger.debug(\"Démarrage de l'abonnement aux notifications lues\");\n\n    const sub$ = this.apollo\n      .subscribe<NotificationsReadEvent>({\n        query: NOTIFICATIONS_READ_SUBSCRIPTION,\n      })\n      .pipe(\n        tap((result) =>\n          this.logger.debug(\n            \"Données reçues de l'abonnement aux notifications lues:\",\n            result\n          )\n        ),\n        map((result) => {\n          const notificationIds = result.data?.notificationsRead || [];\n          this.logger.debug(\n            'Notifications marquées comme lues:',\n            notificationIds\n          );\n          this.updateNotificationStatus(notificationIds, true);\n          return notificationIds;\n        }),\n        catchError((err) => {\n          this.logger.error(\n            'Notifications read subscription error:',\n            err as Error\n          );\n          // Retourner un tableau vide au lieu de propager l'erreur\n          return of([]);\n        }),\n        // Réessayer après un délai en cas d'erreur\n        retry(3) // Réessayer 3 fois en cas d'erreur\n      );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToNewNotifications(): Observable<Notification> {\n    // Vérifier si l'utilisateur est connecté\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn(\n        \"Tentative d'abonnement aux notifications sans être connecté\"\n      );\n      // Créer un Observable vide plutôt que de retourner null\n      return EMPTY;\n    }\n\n    const source$ = this.apollo.subscribe<NotificationReceivedEvent>({\n      query: NOTIFICATION_SUBSCRIPTION,\n    });\n\n    const processed$ = source$.pipe(\n      map((result) => {\n        const notification = result.data?.notificationReceived;\n        if (!notification) {\n          throw new Error('No notification payload received');\n        }\n\n        const normalized = this.normalizeNotification(notification);\n\n        // Vérifier si cette notification existe déjà dans le cache\n        if (this.notificationCache.has(normalized.id)) {\n          this.logger.debug(\n            'MessageService',\n            `Notification ${normalized.id} already exists in cache, skipping`\n          );\n          // Utiliser une technique différente pour ignorer cette notification\n          throw new Error('Notification already exists in cache');\n        }\n\n        // Jouer le son de notification\n        this.playNotificationSound();\n\n        // Mettre à jour le cache et émettre immédiatement la nouvelle notification\n        this.updateNotificationCache(normalized);\n\n        this.logger.debug(\n          'MessageService',\n          'New notification received and processed',\n          normalized\n        );\n\n        return normalized;\n      }),\n      // Utiliser catchError pour gérer les erreurs spécifiques\n      catchError((err) => {\n        // Si c'est l'erreur spécifique pour les notifications déjà existantes, on ignore silencieusement\n        if (\n          err instanceof Error &&\n          err.message === 'Notification already exists in cache'\n        ) {\n          return EMPTY;\n        }\n\n        this.logger.error('New notification subscription error:', err as Error);\n        // Retourner un Observable vide au lieu de null\n        return EMPTY;\n      })\n    );\n\n    const sub = processed$.subscribe({\n      next: (notification) => {\n        this.logger.debug(\n          'MessageService',\n          'Notification subscription next handler',\n          notification\n        );\n      },\n      error: (error) => {\n        this.logger.error(\n          'MessageService',\n          'Error in notification subscription',\n          error\n        );\n      },\n    });\n\n    this.subscriptions.push(sub);\n    return processed$;\n  }\n  // --------------------------------------------------------------------------\n  // Helpers et Utilitaires\n  // --------------------------------------------------------------------------\n\n  private startCleanupInterval(): void {\n    this.cleanupInterval = setInterval(() => {\n      this.cleanupExpiredNotifications();\n    }, 3600000);\n  }\n  private cleanupExpiredNotifications(): void {\n    const now = new Date();\n    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n\n    let expiredCount = 0;\n\n    this.notificationCache.forEach((notification, id) => {\n      const notificationDate = new Date(notification.timestamp);\n      if (notificationDate < thirtyDaysAgo) {\n        this.notificationCache.delete(id);\n        expiredCount++;\n      }\n    });\n\n    if (expiredCount > 0) {\n      this.logger.debug(`Cleaned up ${expiredCount} expired notifications`);\n      this.notifications.next(Array.from(this.notificationCache.values()));\n      this.updateUnreadCount();\n    }\n  }\n  private getCurrentUserId(): string {\n    return localStorage.getItem('userId') || '';\n  }\n  private normalizeMessage(message: Message): Message {\n    if (!message) {\n      this.logger.error(\n        '[MessageService] Cannot normalize null or undefined message'\n      );\n      throw new Error('Message object is required');\n    }\n\n    try {\n      // Vérification des champs obligatoires\n      if (!message.id && !message._id) {\n        this.logger.error(\n          '[MessageService] Message ID is missing',\n          undefined,\n          message\n        );\n        throw new Error('Message ID is required');\n      }\n\n      // Normaliser le sender avec gestion d'erreur\n      let normalizedSender;\n      try {\n        normalizedSender = message.sender\n          ? this.normalizeUser(message.sender)\n          : undefined;\n      } catch (error) {\n        this.logger.warn(\n          '[MessageService] Error normalizing message sender, using default values',\n          error\n        );\n        normalizedSender = {\n          _id: message.senderId || 'unknown',\n          id: message.senderId || 'unknown',\n          username: 'Unknown User',\n          email: '<EMAIL>',\n          role: 'user',\n          isActive: true,\n        };\n      }\n\n      // Normaliser le receiver si présent\n      let normalizedReceiver;\n      if (message.receiver) {\n        try {\n          normalizedReceiver = this.normalizeUser(message.receiver);\n        } catch (error) {\n          this.logger.warn(\n            '[MessageService] Error normalizing message receiver, using default values',\n            error\n          );\n          normalizedReceiver = {\n            _id: message.receiverId || 'unknown',\n            id: message.receiverId || 'unknown',\n            username: 'Unknown User',\n            email: '<EMAIL>',\n            role: 'user',\n            isActive: true,\n          };\n        }\n      }\n\n      // Normaliser les pièces jointes si présentes\n      const normalizedAttachments =\n        message.attachments?.map((att) => ({\n          id: att.id || att._id || `attachment-${Date.now()}`,\n          url: att.url || '',\n          type: att.type || 'unknown',\n          name: att.name || 'attachment',\n          size: att.size || 0,\n          duration: att.duration || 0,\n        })) || [];\n\n      // Construire le message normalisé\n      const normalizedMessage = {\n        ...message,\n        _id: message.id || message._id,\n        id: message.id || message._id,\n        content: message.content || '',\n        sender: normalizedSender,\n        timestamp: this.normalizeDate(message.timestamp),\n        readAt: message.readAt ? this.normalizeDate(message.readAt) : undefined,\n        attachments: normalizedAttachments,\n        metadata: message.metadata || null,\n      };\n\n      // Ajouter le receiver seulement s'il existe\n      if (normalizedReceiver) {\n        normalizedMessage.receiver = normalizedReceiver;\n      }\n\n      this.logger.debug('[MessageService] Message normalized successfully', {\n        messageId: normalizedMessage.id,\n        senderId: normalizedMessage.sender?.id,\n      });\n\n      return normalizedMessage;\n    } catch (error) {\n      this.logger.error(\n        '[MessageService] Error normalizing message:',\n        error instanceof Error ? error : new Error(String(error)),\n        message\n      );\n      throw new Error(\n        `Failed to normalize message: ${\n          error instanceof Error ? error.message : String(error)\n        }`\n      );\n    }\n  }\n  private normalizeNotMessage(message: any) {\n    return {\n      ...message,\n      ...(message.attachments && {\n        attachments: message.attachments.map((att: any) => ({\n          url: att.url,\n          type: att.type,\n          ...(att.name && { name: att.name }),\n          ...(att.size && { size: att.size }),\n        })),\n      }),\n    };\n  }\n  public normalizeUser(user: any): User {\n    if (!user) {\n      throw new Error('User object is required');\n    }\n\n    // Vérification des champs obligatoires avec valeurs par défaut\n    const userId = user.id || user._id;\n    if (!userId) {\n      throw new Error('User ID is required');\n    }\n\n    // Utiliser des valeurs par défaut pour les champs manquants\n    const username = user.username || 'Unknown User';\n    const email = user.email || `user-${userId}@example.com`;\n    const isActive =\n      user.isActive !== undefined && user.isActive !== null\n        ? user.isActive\n        : true;\n    const role = user.role || 'user';\n\n    // Construire l'objet utilisateur normalisé\n    return {\n      _id: userId,\n      id: userId,\n      username: username,\n      email: email,\n      role: role,\n      isActive: isActive,\n      // Champs optionnels\n      image: user.image ?? null,\n      bio: user.bio,\n      isOnline: user.isOnline || false,\n      lastActive: user.lastActive ? new Date(user.lastActive) : undefined,\n      createdAt: user.createdAt ? new Date(user.createdAt) : undefined,\n      updatedAt: user.updatedAt ? new Date(user.updatedAt) : undefined,\n      followingCount: user.followingCount,\n      followersCount: user.followersCount,\n      postCount: user.postCount,\n    };\n  }\n  private normalizeConversation(conv: Conversation): Conversation {\n    if (!conv) {\n      this.logger.error(\n        '[MessageService] Cannot normalize null or undefined conversation'\n      );\n      throw new Error('Conversation object is required');\n    }\n\n    try {\n      // Vérification des champs obligatoires\n      if (!conv.id && !conv._id) {\n        this.logger.error(\n          '[MessageService] Conversation ID is missing',\n          undefined,\n          conv\n        );\n        throw new Error('Conversation ID is required');\n      }\n\n      // Normaliser les participants avec gestion d'erreur\n      const normalizedParticipants = [];\n      if (conv.participants && Array.isArray(conv.participants)) {\n        for (const participant of conv.participants) {\n          try {\n            if (participant) {\n              normalizedParticipants.push(this.normalizeUser(participant));\n            }\n          } catch (error) {\n            this.logger.warn(\n              '[MessageService] Error normalizing participant, skipping',\n              error\n            );\n          }\n        }\n      } else {\n        this.logger.warn(\n          '[MessageService] Conversation has no participants or invalid participants array',\n          conv\n        );\n      }\n\n      // Normaliser les messages avec gestion d'erreur\n      const normalizedMessages = [];\n      if (conv.messages && Array.isArray(conv.messages)) {\n        this.logger.debug('[MessageService] Processing conversation messages', {\n          count: conv.messages.length,\n        });\n\n        for (const message of conv.messages) {\n          try {\n            if (message) {\n              const normalizedMessage = this.normalizeMessage(message);\n              this.logger.debug(\n                '[MessageService] Successfully normalized message',\n                {\n                  messageId: normalizedMessage.id,\n                  content: normalizedMessage.content?.substring(0, 20),\n                  sender: normalizedMessage.sender?.username,\n                }\n              );\n              normalizedMessages.push(normalizedMessage);\n            }\n          } catch (error) {\n            this.logger.warn(\n              '[MessageService] Error normalizing message in conversation, skipping',\n              error\n            );\n          }\n        }\n      } else {\n        this.logger.debug(\n          '[MessageService] No messages found in conversation or invalid messages array'\n        );\n      }\n\n      // Normaliser le dernier message avec gestion d'erreur\n      let normalizedLastMessage = null;\n      if (conv.lastMessage) {\n        try {\n          normalizedLastMessage = this.normalizeMessage(conv.lastMessage);\n        } catch (error) {\n          this.logger.warn(\n            '[MessageService] Error normalizing last message, using null',\n            error\n          );\n        }\n      }\n\n      // Construire la conversation normalisée\n      const normalizedConversation = {\n        ...conv,\n        _id: conv.id || conv._id,\n        id: conv.id || conv._id,\n        participants: normalizedParticipants,\n        messages: normalizedMessages,\n        lastMessage: normalizedLastMessage,\n        unreadCount: conv.unreadCount || 0,\n        isGroup: !!conv.isGroup,\n        createdAt: this.normalizeDate(conv.createdAt),\n        updatedAt: this.normalizeDate(conv.updatedAt),\n      };\n\n      this.logger.debug(\n        '[MessageService] Conversation normalized successfully',\n        {\n          conversationId: normalizedConversation.id,\n          participantCount: normalizedParticipants.length,\n          messageCount: normalizedMessages.length,\n        }\n      );\n\n      return normalizedConversation;\n    } catch (error) {\n      this.logger.error(\n        '[MessageService] Error normalizing conversation:',\n        error instanceof Error ? error : new Error(String(error)),\n        conv\n      );\n      throw new Error(\n        `Failed to normalize conversation: ${\n          error instanceof Error ? error.message : String(error)\n        }`\n      );\n    }\n  }\n  private normalizeDate(date: string | Date | undefined): Date {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to parse date: ${date}`, error);\n      return new Date();\n    }\n  }\n\n  // Méthode sécurisée pour créer une date à partir d'une valeur potentiellement undefined\n  private safeDate(date: string | Date | undefined): Date {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to create safe date: ${date}`, error);\n      return new Date();\n    }\n  }\n  private toSafeISOString = (\n    date: Date | string | undefined\n  ): string | undefined => {\n    if (!date) return undefined;\n    return typeof date === 'string' ? date : date.toISOString();\n  };\n  private normalizeNotification(notification: Notification): Notification {\n    this.logger.debug(\n      'MessageService',\n      'Normalizing notification',\n      notification\n    );\n\n    if (!notification) {\n      this.logger.error('MessageService', 'Notification is null or undefined');\n      throw new Error('Notification is required');\n    }\n\n    // Vérifier et normaliser l'ID\n    const notificationId = notification.id || (notification as any)._id;\n    if (!notificationId) {\n      this.logger.error(\n        'MessageService',\n        'Notification ID is missing',\n        notification\n      );\n      throw new Error('Notification ID is required');\n    }\n\n    if (!notification.timestamp) {\n      this.logger.warn(\n        'MessageService',\n        'Notification timestamp is missing, using current time',\n        notification\n      );\n      notification.timestamp = new Date();\n    }\n\n    try {\n      const normalized = {\n        ...notification,\n        _id: notificationId, // Conserver l'ID MongoDB original\n        id: notificationId, // Utiliser le même ID pour les deux propriétés\n        timestamp: new Date(notification.timestamp),\n        ...(notification.senderId && {\n          senderId: this.normalizeSender(notification.senderId),\n        }),\n        ...(notification.message && {\n          message: this.normalizeNotMessage(notification.message),\n        }),\n      };\n\n      this.logger.debug(\n        'MessageService',\n        'Normalized notification result',\n        normalized\n      );\n      return normalized;\n    } catch (error) {\n      this.logger.error(\n        'MessageService',\n        'Error in normalizeNotification',\n        error\n      );\n      throw error;\n    }\n  }\n  private normalizeSender(sender: any) {\n    return {\n      id: sender.id,\n      username: sender.username,\n      ...(sender.image && { image: sender.image }),\n    };\n  }\n\n  /**\n   * Normalise un message de notification\n   * @param message Message à normaliser\n   * @returns Message normalisé\n   */\n  private normalizeNotMessage(message: any) {\n    if (!message) return null;\n\n    return {\n      id: message.id || message._id,\n      content: message.content || '',\n      type: message.type || 'TEXT',\n      timestamp: this.safeDate(message.timestamp),\n      attachments: message.attachments || [],\n      ...(message.sender && { sender: this.normalizeSender(message.sender) }),\n    };\n  }\n  private updateCache(notifications: Notification[]) {\n    this.logger.debug(\n      'MessageService',\n      `Updating notification cache with ${notifications.length} notifications`\n    );\n\n    if (notifications.length === 0) {\n      this.logger.warn('MessageService', 'No notifications to update in cache');\n      return;\n    }\n\n    console.log(\n      `Starting to update cache with ${notifications.length} notifications`\n    );\n\n    // Vérifier si les notifications ont des IDs valides\n    const validNotifications = notifications.filter(\n      (notif) => notif && (notif.id || (notif as any)._id)\n    );\n\n    if (validNotifications.length !== notifications.length) {\n      console.warn(\n        `Found ${\n          notifications.length - validNotifications.length\n        } notifications without valid IDs`\n      );\n    }\n\n    // Traiter chaque notification\n    validNotifications.forEach((notif, index) => {\n      try {\n        // S'assurer que la notification a un ID\n        const notifId = notif.id || (notif as any)._id;\n        if (!notifId) {\n          console.error('Notification without ID:', notif);\n          return;\n        }\n\n        // Normaliser la notification\n        const normalized = this.normalizeNotification(notif);\n\n        // Vérifier si cette notification existe déjà dans le cache\n        if (this.notificationCache.has(normalized.id)) {\n          console.log(\n            `Notification ${normalized.id} already exists in cache, skipping`\n          );\n          return;\n        }\n\n        // Ajouter au cache\n        this.notificationCache.set(normalized.id, normalized);\n\n        console.log(`Added notification ${normalized.id} to cache`);\n      } catch (error) {\n        console.error(`Error processing notification ${index + 1}:`, error);\n        console.error('Problematic notification:', notif);\n      }\n    });\n\n    console.log(\n      `Notification cache updated, now contains ${this.notificationCache.size} notifications`\n    );\n\n    // Sauvegarder les notifications dans le localStorage après la mise à jour du cache\n    this.saveNotificationsToLocalStorage();\n  }\n  private updateUnreadCount() {\n    const count = Array.from(this.notificationCache.values()).filter(\n      (n) => !n.isRead\n    ).length;\n    this.notificationCount.next(count);\n  }\n  private updateNotificationCache(notification: Notification): void {\n    // Vérifier si la notification existe déjà dans le cache (pour éviter les doublons)\n    if (!this.notificationCache.has(notification.id)) {\n      this.notificationCache.set(notification.id, notification);\n      this.notifications.next(Array.from(this.notificationCache.values()));\n      this.updateUnreadCount();\n      // Sauvegarder les notifications dans le localStorage après chaque mise à jour\n      this.saveNotificationsToLocalStorage();\n    } else {\n      this.logger.debug(\n        'MessageService',\n        `Notification ${notification.id} already exists in cache, skipping`\n      );\n    }\n  }\n  private updateNotificationStatus(ids: string[], isRead: boolean) {\n    ids.forEach((id) => {\n      const notif = this.notificationCache.get(id);\n      if (notif) {\n        this.notificationCache.set(id, { ...notif, isRead });\n      }\n    });\n    this.notifications.next(Array.from(this.notificationCache.values()));\n    this.updateUnreadCount();\n  }\n  // Typing indicators\n  startTyping(conversationId: string): Observable<boolean> {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot start typing: no user ID');\n      return of(false);\n    }\n\n    return this.apollo\n      .mutate<StartTupingResponse>({\n        mutation: START_TYPING_MUTATION,\n        variables: {\n          input: {\n            conversationId,\n            userId,\n          },\n        },\n      })\n      .pipe(\n        map((result) => result.data?.startTyping || false),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error starting typing indicator',\n            error\n          );\n          return throwError(\n            () => new Error('Failed to start typing indicator')\n          );\n        })\n      );\n  }\n\n  stopTyping(conversationId: string): Observable<boolean> {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot stop typing: no user ID');\n      return of(false);\n    }\n\n    return this.apollo\n      .mutate<StopTypingResponse>({\n        mutation: STOP_TYPING_MUTATION,\n        variables: {\n          input: {\n            conversationId,\n            userId,\n          },\n        },\n      })\n      .pipe(\n        map((result) => result.data?.stopTyping || false),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error stopping typing indicator',\n            error\n          );\n          return throwError(() => new Error('Failed to stop typing indicator'));\n        })\n      );\n  }\n\n  // destroy\n  cleanupSubscriptions(): void {\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n    this.subscriptions = [];\n    if (this.cleanupInterval) {\n      clearInterval(this.cleanupInterval);\n    }\n    this.notificationCache.clear();\n    this.logger.debug('NotificationService destroyed');\n  }\n\n  ngOnDestroy() {\n    this.cleanupSubscriptions();\n  }\n}\n"], "mappings": "AAEA,SACEA,eAAe,EACfC,UAAU,EACVC,EAAE,EAEFC,UAAU,EACVC,KAAK,EACLC,KAAK,QACA,MAAM;AACb,SACEC,GAAG,EACHC,UAAU,EACVC,GAAG,EACHC,MAAM,EACNC,SAAS,QAGJ,gBAAgB;AACvB,SAASC,IAAI,QAAQ,MAAM;AAC3B,SACEC,WAAW,EAEXC,QAAQ,EACRC,UAAU,QAML,yBAAyB;AAChC,SACEC,uBAAuB,EACvBC,uBAAuB,EACvBC,yBAAyB,EACzBC,sBAAsB,EACtBC,qBAAqB,EACrBC,qBAAqB,EACrBC,yBAAyB,EACzBC,wBAAwB,EACxBC,cAAc,EACdC,kBAAkB,EAClBC,iCAAiC,EACjCC,qBAAqB,EACrBC,yBAAyB,EACzBC,wBAAwB,EACxBC,yBAAyB,EACzBC,qBAAqB,EACrBC,oBAAoB,EACpBC,6BAA6B,EAC7BC,sBAAsB,EACtBC,yBAAyB,EACzBC,wBAAwB,EACxBC,oBAAoB,EACpBC,qBAAqB,EACrBC,qBAAqB,EACrBC,qBAAqB,EAGrBC,oBAAoB,EACpBC,eAAe,EACfC,qBAAqB,EACrBC,qBAAqB,EACrBC,uBAAuB,EACvBC,kBAAkB,EAClBC,8BAA8B,EAC9BC,+BAA+B,EAC/BC,+BAA+B,EAC/BC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,sCAAsC,EACtCC,iCAAiC;AACjC;AACAC,kBAAkB,EAClBC,kBAAkB,EAClBC,gBAAgB,EAChBC,sBAAsB,EACtBC,yBAAyB,EACzBC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB,EACjBC,0BAA0B,EAC1BC,wBAAwB,EACxBC,0BAA0B,EAE1BC,wBAAwB,QACnB,4BAA4B;;;;AA0CnC,OAAM,MAAOC,cAAc;EA6DzBC,YACUC,MAAc,EACdC,MAAqB,EACrBC,IAAY;IAFZ,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IA/Dd;IACQ,KAAAC,kBAAkB,GAAG,IAAItE,eAAe,CAAgB,IAAI,CAAC;IAC7D,KAAAuE,aAAa,GAAG,IAAIvE,eAAe,CAAiB,EAAE,CAAC;IACvD,KAAAwE,iBAAiB,GAAG,IAAIC,GAAG,EAAwB;IAEnD,KAAAC,iBAAiB,GAAG,IAAI1E,eAAe,CAAS,CAAC,CAAC;IAClD,KAAA2E,WAAW,GAAG,IAAIF,GAAG,EAAgB;IACrC,KAAAG,aAAa,GAAmB,EAAE;IACzB,KAAAC,cAAc,GAAG,MAAM;IAChC,KAAAC,aAAa,GAAG,CAAC;IAEzB;IACQ,KAAAC,UAAU,GAAG,IAAI/E,eAAe,CAAc,IAAI,CAAC;IACnD,KAAAgF,YAAY,GAAG,IAAIhF,eAAe,CAAsB,IAAI,CAAC;IAC7D,KAAAiF,WAAW,GAAG,IAAIjF,eAAe,CAAoB,IAAI,CAAC;IAC1D,KAAAkF,WAAW,GAAuB,IAAI;IACtC,KAAAC,YAAY,GAAuB,IAAI;IACvC,KAAAC,cAAc,GAA6B,IAAI;IAEvD;IACO,KAAAC,WAAW,GAAG,IAAI,CAACN,UAAU,CAACO,YAAY,EAAE;IAC5C,KAAAC,aAAa,GAAG,IAAI,CAACP,YAAY,CAACM,YAAY,EAAE;IAChD,KAAAE,YAAY,GAAG,IAAI,CAACP,WAAW,CAACK,YAAY,EAAE;IAC9C,KAAAG,YAAY,GAAG,IAAIzF,eAAe,CAAqB,IAAI,CAAC;IAC5D,KAAA0F,aAAa,GAAG,IAAI1F,eAAe,CAAqB,IAAI,CAAC;IAEpE;IACiB,KAAA2F,SAAS,GAAqB;MAC7CC,UAAU,EAAE,CACV;QAAEC,IAAI,EAAE;MAA8B,CAAE,EACxC;QAAEA,IAAI,EAAE;MAA+B,CAAE;KAE5C;IACO,KAAAC,UAAU,GAAW,EAAE;IAE/B;IACO,KAAAC,qBAAqB,GAMxB;MACFC,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,KAAK;MAClBC,eAAe,EAAE;KAClB;IAED;IACO,KAAAC,mBAAmB,GAAG,IAAI,CAAC/B,kBAAkB,CAACgB,YAAY,EAAE;IAC5D,KAAAgB,cAAc,GAAG,IAAI,CAAC/B,aAAa,CAACe,YAAY,EAAE;IAClD,KAAAiB,kBAAkB,GAAG,IAAI,CAAC7B,iBAAiB,CAACY,YAAY,EAAE;IAEjE;IACQ,KAAAkB,MAAM,GAAwC,EAAE;IAChD,KAAAC,SAAS,GAA+B,EAAE;IAC1C,KAAAC,KAAK,GAAG,KAAK;IAggCrB;IACA;IACA;IACA;IACQ,KAAAC,sBAAsB,GAAG;MAC/BT,WAAW,EAAE,CAAC;MACdU,KAAK,EAAE,EAAE;MACTC,oBAAoB,EAAE;KACvB;IAk+EO,KAAAC,eAAe,GACrBC,IAA+B,IACT;MACtB,IAAI,CAACA,IAAI,EAAE,OAAOC,SAAS;MAC3B,OAAO,OAAOD,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGA,IAAI,CAACE,WAAW,EAAE;IAC7D,CAAC;IAx+GC,IAAI,CAACC,iCAAiC,EAAE;IACxC,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEA;;;;EAIQH,iCAAiCA,CAAA;IACvC,IAAI;MACF,MAAMI,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;MAChE,IAAIF,kBAAkB,EAAE;QACtB,MAAM/C,aAAa,GAAGkD,IAAI,CAACC,KAAK,CAACJ,kBAAkB,CAAmB;QACtE,IAAI,CAAClD,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,iBAAiBpD,aAAa,CAACqD,MAAM,uCAAuC,CAC7E;QAED;QACA,IAAI,CAACpD,iBAAiB,CAACqD,KAAK,EAAE;QAE9B;QACAtD,aAAa,CAACuD,OAAO,CAAEC,YAAY,IAAI;UACrC;UACA,IAAIA,YAAY,IAAIA,YAAY,CAACC,EAAE,EAAE;YACnC,IAAI,CAACxD,iBAAiB,CAACyD,GAAG,CAACF,YAAY,CAACC,EAAE,EAAED,YAAY,CAAC;;QAE7D,CAAC,CAAC;QAEF;QACA,IAAI,CAACxD,aAAa,CAAC2D,IAAI,CAACC,KAAK,CAACxH,IAAI,CAAC,IAAI,CAAC6D,iBAAiB,CAAC4D,MAAM,EAAE,CAAC,CAAC;QACpE,IAAI,CAACC,iBAAiB,EAAE;QAExB,IAAI,CAACjE,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,GAAG,IAAI,CAACnD,iBAAiB,CAAC8D,IAAI,uCAAuC,CACtE;;KAEJ,CAAC,OAAOC,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,qEAAqE,EACrEA,KAAK,CACN;;EAEL;EACQpB,iBAAiBA,CAAA;IACvB,IAAI,CAAC9C,IAAI,CAACmE,iBAAiB,CAAC,MAAK;MAC/B,IAAI,CAACC,2BAA2B,EAAE,CAACC,SAAS,EAAE;MAC9C,IAAI,CAACC,4BAA4B,EAAE,CAACD,SAAS,EAAE;MAC/C,IAAI,CAACE,wBAAwB,EAAE,CAACF,SAAS,EAAE;IAC7C,CAAC,CAAC;IACF,IAAI,CAACG,qBAAqB,EAAE;EAC9B;EAEA;;;EAGQD,wBAAwBA,CAAA;IAC9B,OAAO,IAAI,CAACzE,MAAM,CACfuE,SAAS,CAAiC;MACzCI,KAAK,EAAE/E;KACR,CAAC,CACDgF,IAAI,CACHzI,GAAG,CAAC,CAAC;MAAE0I;IAAI,CAAE,KAAI;MACf,IAAI,CAACA,IAAI,EAAEhE,YAAY,EAAE;QACvB,OAAO,IAAI;;MAGb;MACA,IAAI,CAACiE,kBAAkB,CAACD,IAAI,CAAChE,YAAY,CAAC;MAC1C,OAAOgE,IAAI,CAAChE,YAAY;IAC1B,CAAC,CAAC,EACFzE,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC/D,OAAOrI,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH;EACL;EAEA;;;EAGQ+I,kBAAkBA,CAACC,IAAkB;IAC3C,IAAI,CAAC9E,MAAM,CAACuD,KAAK,CAAC,wBAAwB,EAAEuB,IAAI,CAAC;IACjD,IAAI,CAAClE,YAAY,CAACkD,IAAI,CAACgB,IAAI,CAAC;IAC5B,IAAI,CAACC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;EAC7B;EAEA;EACA;EACA;EAEA;;;EAGQ9B,aAAaA,CAAA;IACnB,IAAI,CAAC+B,SAAS,CAAC,UAAU,EAAE,4BAA4B,CAAC;IACxD,IAAI,CAACA,SAAS,CAAC,UAAU,EAAE,4BAA4B,CAAC;IACxD,IAAI,CAACA,SAAS,CAAC,gBAAgB,EAAE,kCAAkC,CAAC;IACpE,IAAI,CAACA,SAAS,CAAC,cAAc,EAAE,gCAAgC,CAAC;EAClE;EAEA;;;;;EAKQA,SAASA,CAACC,IAAY,EAAEC,IAAY;IAC1C,IAAI;MACF,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC;MAC7BC,KAAK,CAACE,IAAI,EAAE;MACZ,IAAI,CAACjD,MAAM,CAAC6C,IAAI,CAAC,GAAGE,KAAK;MACzB,IAAI,CAAC9C,SAAS,CAAC4C,IAAI,CAAC,GAAG,KAAK;MAE5B;MACAE,KAAK,CAACG,gBAAgB,CAAC,OAAO,EAAE,MAAK;QACnC,IAAI,CAACjD,SAAS,CAAC4C,IAAI,CAAC,GAAG,KAAK;MAC9B,CAAC,CAAC;MAEF,IAAI,CAACjF,MAAM,CAACuD,KAAK,CAAC,gBAAgB,EAAE,eAAe0B,IAAI,KAAKC,IAAI,GAAG,CAAC;KACrE,CAAC,OAAOf,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,oCAAoCc,IAAI,GAAG,EAC3Cd,KAAK,CACN;;EAEL;EAEA;;;;;EAKAY,IAAIA,CAACE,IAAY,EAAEM,IAAA,GAAgB,KAAK;IACtC,IAAI,IAAI,CAACjD,KAAK,EAAE;MACd,IAAI,CAACtC,MAAM,CAACuD,KAAK,CAAC,gBAAgB,EAAE,OAAO0B,IAAI,kBAAkB,CAAC;MAClE;;IAGF,IAAI;MACF,MAAMO,KAAK,GAAG,IAAI,CAACpD,MAAM,CAAC6C,IAAI,CAAC;MAC/B,IAAI,CAACO,KAAK,EAAE;QACV,IAAI,CAACxF,MAAM,CAACyF,IAAI,CAAC,gBAAgB,EAAE,OAAOR,IAAI,aAAa,CAAC;QAC5D;;MAGF;MACAO,KAAK,CAACD,IAAI,GAAGA,IAAI;MAEjB;MACA,IAAI,CAAC,IAAI,CAAClD,SAAS,CAAC4C,IAAI,CAAC,EAAE;QACzBO,KAAK,CAACE,WAAW,GAAG,CAAC;QACrBF,KAAK,CAACT,IAAI,EAAE,CAACY,KAAK,CAAExB,KAAK,IAAI;UAC3B,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,oCAAoCc,IAAI,GAAG,EAC3Cd,KAAK,CACN;QACH,CAAC,CAAC;QACF,IAAI,CAAC9B,SAAS,CAAC4C,IAAI,CAAC,GAAG,IAAI;QAC3B,IAAI,CAACjF,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,mBAAmB0B,IAAI,aAAaM,IAAI,EAAE,CAC3C;;KAEJ,CAAC,OAAOpB,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,oCAAoCc,IAAI,GAAG,EAC3Cd,KAAK,CACN;;EAEL;EAEA;;;;EAIAyB,IAAIA,CAACX,IAAY;IACf,IAAI;MACF,MAAMO,KAAK,GAAG,IAAI,CAACpD,MAAM,CAAC6C,IAAI,CAAC;MAC/B,IAAI,CAACO,KAAK,EAAE;QACV,IAAI,CAACxF,MAAM,CAACyF,IAAI,CAAC,gBAAgB,EAAE,OAAOR,IAAI,aAAa,CAAC;QAC5D;;MAGF;MACA,IAAI,IAAI,CAAC5C,SAAS,CAAC4C,IAAI,CAAC,EAAE;QACxBO,KAAK,CAACK,KAAK,EAAE;QACbL,KAAK,CAACE,WAAW,GAAG,CAAC;QACrB,IAAI,CAACrD,SAAS,CAAC4C,IAAI,CAAC,GAAG,KAAK;QAC5B,IAAI,CAACjF,MAAM,CAACuD,KAAK,CAAC,gBAAgB,EAAE,eAAe0B,IAAI,EAAE,CAAC;;KAE7D,CAAC,OAAOd,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iCAAiCc,IAAI,GAAG,EACxCd,KAAK,CACN;;EAEL;EAEA;;;EAGA2B,aAAaA,CAAA;IACXC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC5D,MAAM,CAAC,CAACsB,OAAO,CAAEuB,IAAI,IAAI;MACxC,IAAI,CAACW,IAAI,CAACX,IAAI,CAAC;IACjB,CAAC,CAAC;IACF,IAAI,CAACjF,MAAM,CAACuD,KAAK,CAAC,gBAAgB,EAAE,+BAA+B,CAAC;EACtE;EAEA;;;;EAIA0C,QAAQA,CAAC3D,KAAc;IACrB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACtC,MAAM,CAACkG,IAAI,CAAC,gBAAgB,EAAE,OAAO5D,KAAK,GAAG,WAAW,GAAG,QAAQ,EAAE,CAAC;IAE3E,IAAIA,KAAK,EAAE;MACT,IAAI,CAACwD,aAAa,EAAE;;EAExB;EAEA;;;;EAIAK,OAAOA,CAAA;IACL,OAAO,IAAI,CAAC7D,KAAK;EACnB;EAEA;;;EAGA8D,qBAAqBA,CAAA;IACnBC,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;IAE1E,IAAI,IAAI,CAAChE,KAAK,EAAE;MACd+D,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAClE;;IAGF;IACA,IAAI;MACF;MACA,MAAMC,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MAEvC;MACA,MAAMC,UAAU,GAAGJ,YAAY,CAACK,gBAAgB,EAAE;MAClD,MAAMC,QAAQ,GAAGN,YAAY,CAACO,UAAU,EAAE;MAE1C;MACAH,UAAU,CAACI,IAAI,GAAG,MAAM;MACxBJ,UAAU,CAACK,SAAS,CAACC,cAAc,CAAC,GAAG,EAAEV,YAAY,CAACb,WAAW,CAAC,CAAC,CAAC;MAEpE;MACAmB,QAAQ,CAACK,IAAI,CAACD,cAAc,CAAC,CAAC,EAAEV,YAAY,CAACb,WAAW,CAAC;MACzDmB,QAAQ,CAACK,IAAI,CAACC,uBAAuB,CACnC,GAAG,EACHZ,YAAY,CAACb,WAAW,GAAG,IAAI,CAChC;MACDmB,QAAQ,CAACK,IAAI,CAACC,uBAAuB,CAAC,CAAC,EAAEZ,YAAY,CAACb,WAAW,GAAG,GAAG,CAAC;MAExE;MACAiB,UAAU,CAACS,OAAO,CAACP,QAAQ,CAAC;MAC5BA,QAAQ,CAACO,OAAO,CAACb,YAAY,CAACc,WAAW,CAAC;MAE1C;MACAV,UAAU,CAACW,KAAK,CAACf,YAAY,CAACb,WAAW,CAAC;MAC1CiB,UAAU,CAACf,IAAI,CAACW,YAAY,CAACb,WAAW,GAAG,GAAG,CAAC;MAE/CW,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;KACtE,CAAC,OAAOnC,KAAK,EAAE;MACdkC,OAAO,CAAClC,KAAK,CACX,sDAAsD,EACtDA,KAAK,CACN;MAED;MACA,IAAI;QACF,MAAMgB,KAAK,GAAG,IAAIC,KAAK,CAAC,gCAAgC,CAAC;QACzDD,KAAK,CAACoC,MAAM,GAAG,GAAG,CAAC,CAAC;QACpBpC,KAAK,CAACJ,IAAI,EAAE,CAACY,KAAK,CAAE6B,GAAG,IAAI;UACzBnB,OAAO,CAAClC,KAAK,CACX,2DAA2D,EAC3DqD,GAAG,CACJ;QACH,CAAC,CAAC;OACH,CAAC,OAAOC,UAAU,EAAE;QACnBpB,OAAO,CAAClC,KAAK,CACX,8DAA8D,EAC9DsD,UAAU,CACX;;;EAGP;EACA;EACA;EACA;EAEA;;;;;;;;EAQAC,gBAAgBA,CACdC,UAAkB,EAClBC,SAAe,EACfC,cAAuB,EACvBC,QAAiB;IAEjB,IAAI,CAAC9H,MAAM,CAACuD,KAAK,CACf,kDAAkDoE,UAAU,eAAeG,QAAQ,GAAG,CACvF;IAED;IACA,IAAI,CAACF,SAAS,IAAIA,SAAS,CAAC1D,IAAI,KAAK,CAAC,EAAE;MACtC,IAAI,CAAClE,MAAM,CAACmE,KAAK,CAAC,qCAAqC,CAAC;MACxD,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,oBAAoB,CAAC,CAAC;;IAG1D;IACA,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,EAAE;IAC5B,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAACR,SAAS,CAAC,EAAE,iBAAiBI,SAAS,OAAO,EAAE;MACzEjB,IAAI,EAAE,YAAY;MAClBsB,YAAY,EAAEL;KACf,CAAC;IAEF;IACA,IAAI,CAACG,SAAS,IAAIA,SAAS,CAACjE,IAAI,KAAK,CAAC,EAAE;MACtC,IAAI,CAAClE,MAAM,CAACmE,KAAK,CAAC,8CAA8C,CAAC;MACjE,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,6BAA6B,CAAC,CAAC;;IAGnE,IAAI,CAAC/H,MAAM,CAACuD,KAAK,CACf,wCAAwC4E,SAAS,CAAClD,IAAI,WAAWkD,SAAS,CAACjE,IAAI,QAAQ,CACxF;IAED;IACA,MAAMoE,QAAQ,GAAG;MACfR,QAAQ,EAAEA,QAAQ,IAAI,CAAC;MACvBS,cAAc,EAAE,IAAI;MACpBP,SAAS,EAAEA;KACZ;IAED;IACA;IACA,OAAO,IAAI,CAACQ,WAAW,CACrBb,UAAU,EACV,GAAG;IAAE;IACLQ,SAAS,EACT3L,WAAW,CAACiM,aAAa,EACzBZ,cAAc,EACdjF,SAAS,EACT0F,QAAQ,CACT;EACH;EAEA;;;;;EAKAI,SAASA,CAACC,QAAgB;IACxB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAM3D,KAAK,GAAG,IAAIC,KAAK,CAACuD,QAAQ,CAAC;MAEjCxD,KAAK,CAAC4D,OAAO,GAAG,MAAK;QACnBF,OAAO,EAAE;MACX,CAAC;MAED1D,KAAK,CAAC6D,OAAO,GAAI7E,KAAK,IAAI;QACxB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QACjE2E,MAAM,CAAC3E,KAAK,CAAC;MACf,CAAC;MAEDgB,KAAK,CAACJ,IAAI,EAAE,CAACY,KAAK,CAAExB,KAAK,IAAI;QAC3B,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QACjE2E,MAAM,CAAC3E,KAAK,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;;;;EAIA8E,gBAAgBA,CAAA;IACd,IAAI,CAACjJ,MAAM,CAACuD,KAAK,CAAC,yCAAyC,CAAC;IAE5D,OAAO,IAAI,CAACxD,MAAM,CACfmJ,UAAU,CAA+B;MACxCxE,KAAK,EAAE9E,wBAAwB;MAC/BuJ,WAAW,EAAE,cAAc,CAAE;KAC9B,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBzI,GAAG,CAAEmN,MAAM,IAAI;MACb,MAAMC,aAAa,GAAGD,MAAM,CAACzE,IAAI,EAAEqE,gBAAgB,IAAI,EAAE;MACzD,IAAI,CAACjJ,MAAM,CAACuD,KAAK,CACf,8BAA8B+F,aAAa,CAAC9F,MAAM,iBAAiB,CACpE;MACD,OAAO8F,aAAa;IACtB,CAAC,CAAC,EACFnN,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,iDAAiD,EACjDA,KAAK,CACN;MACD,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtE,CAAC,CAAC,CACH;EACL;EACA;EACAwB,WAAWA,CACTC,QAAgB,EAChB7B,UAAkB,EAClBE,cAAsB,EACtB4B,IAAA,GAAe,CAAC,EAChBjH,KAAA,GAAgB,EAAE;IAElB,OAAO,IAAI,CAACzC,MAAM,CACfmJ,UAAU,CAA6B;MACtCxE,KAAK,EAAEjG,kBAAkB;MACzBiL,SAAS,EAAE;QAAEF,QAAQ;QAAE7B,UAAU;QAAEE,cAAc;QAAErF,KAAK;QAAEiH;MAAI,CAAE;MAChEN,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBzI,GAAG,CAAEmN,MAAM,IAAI;MACb,MAAMM,QAAQ,GAAGN,MAAM,CAACzE,IAAI,EAAE2E,WAAW,IAAI,EAAE;MAC/C,OAAOI,QAAQ,CAACzN,GAAG,CAAE0N,GAAG,IAAK,IAAI,CAACC,gBAAgB,CAACD,GAAG,CAAC,CAAC;IAC1D,CAAC,CAAC,EACFzN,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MACpD,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAChE,CAAC,CAAC,CACH;EACL;EACA+B,WAAWA,CAACC,SAAiB,EAAEC,UAAkB;IAC/C,OAAO,IAAI,CAACjK,MAAM,CACfkK,MAAM,CAA2B;MAChCC,QAAQ,EAAE3L,qBAAqB;MAC/BmL,SAAS,EAAE;QAAEK,SAAS;QAAEC;MAAU;KACnC,CAAC,CACDrF,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAEkF,WAAW,EAAE;QAC7B,MAAM,IAAI/B,KAAK,CAAC,wBAAwB,CAAC;;MAE3C,OAAO,IAAI,CAAC8B,gBAAgB,CAACR,MAAM,CAACzE,IAAI,CAACkF,WAAW,CAAC;IACvD,CAAC,CAAC,EACF3N,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAClD,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC,CAAC,CACH;EACL;EAEAoC,aAAaA,CAACJ,SAAiB;IAC7B,OAAO,IAAI,CAAChK,MAAM,CACfkK,MAAM,CAA6B;MAClCC,QAAQ,EAAE1L,uBAAuB;MACjCkL,SAAS,EAAE;QAAEK;MAAS;KACvB,CAAC,CACDpF,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAEuF,aAAa,EAAE;QAC/B,MAAM,IAAIpC,KAAK,CAAC,0BAA0B,CAAC;;MAE7C,OAAO,IAAI,CAAC8B,gBAAgB,CAACR,MAAM,CAACzE,IAAI,CAACuF,aAAa,CAAC;IACzD,CAAC,CAAC,EACFhO,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACnD,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAChE,CAAC,CAAC,CACH;EACL;EAEAS,WAAWA,CACTb,UAAkB,EAClByC,OAAe,EACfC,IAAW,EACXC,WAAA,GAA2B9N,WAAW,CAAC+N,IAAI,EAC3C1C,cAAuB,EACvB2C,OAAgB,EAChBlC,QAAc;IAEd,IAAI,CAACtI,MAAM,CAACkG,IAAI,CACd,wCAAwCyB,UAAU,cAAc,CAAC,CAAC0C,IAAI,EAAE,CACzE;IACD,IAAI,CAACrK,MAAM,CAACuD,KAAK,CACf,sCAAsC6G,OAAO,EAAEK,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAC7DL,OAAO,EAAE5G,MAAM,GAAG,EAAE,GAAG,KAAK,GAAG,EACjC,GAAG,CACJ;IAED;IACA,MAAMkH,KAAK,GAAGvH,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACpD,MAAM,CAACuD,KAAK,CACf,uEAAuE,CAAC,CAACmH,KAAK,EAAE,CACjF;IAED;IACA,IAAIC,gBAAgB,GAAGL,WAAW;IAElC;IACA,IAAID,IAAI,EAAE;MACR;MACA,IAAIC,WAAW,KAAK9N,WAAW,CAACiM,aAAa,EAAE;QAC7CkC,gBAAgB,GAAGnO,WAAW,CAACiM,aAAa;QAC5C,IAAI,CAACzI,MAAM,CAACuD,KAAK,CAAC,oDAAoD,CAAC;;MAEzE;MAAA,KACK,IAAI+G,WAAW,KAAK9N,WAAW,CAAC+N,IAAI,EAAE;QACzC,IAAIF,IAAI,CAACtD,IAAI,CAAC6D,UAAU,CAAC,QAAQ,CAAC,EAAE;UAClCD,gBAAgB,GAAGnO,WAAW,CAACqO,KAAK;SACrC,MAAM,IAAIR,IAAI,CAACtD,IAAI,CAAC6D,UAAU,CAAC,QAAQ,CAAC,EAAE;UACzCD,gBAAgB,GAAGnO,WAAW,CAACsO,KAAK;SACrC,MAAM,IAAIT,IAAI,CAACtD,IAAI,CAAC6D,UAAU,CAAC,QAAQ,CAAC,EAAE;UACzC;UACA,IAAItC,QAAQ,IAAIA,QAAQ,CAACC,cAAc,EAAE;YACvCoC,gBAAgB,GAAGnO,WAAW,CAACiM,aAAa;WAC7C,MAAM;YACLkC,gBAAgB,GAAGnO,WAAW,CAACuO,KAAK;;SAEvC,MAAM;UACLJ,gBAAgB,GAAGnO,WAAW,CAACwO,IAAI;;;;IAKzC,IAAI,CAAChL,MAAM,CAACuD,KAAK,CACf,6CAA6CoH,gBAAgB,EAAE,CAChE;IAED;IACA;IACA,MAAMjB,SAAS,GAAQ;MACrB/B,UAAU;MACVyC,OAAO;MACPrD,IAAI,EAAE4D,gBAAgB,CAAE;KACzB;IAED;IACA;IACA,IAAIjB,SAAS,CAAC3C,IAAI,EAAE;MAClBhB,MAAM,CAACkF,cAAc,CAACvB,SAAS,EAAE,MAAM,EAAE;QACvCwB,KAAK,EAAEP,gBAAgB;QACvBQ,UAAU,EAAE,IAAI;QAChBC,QAAQ,EAAE;OACX,CAAC;;IAGJ;IACA,IAAI9C,QAAQ,EAAE;MACZoB,SAAS,CAACpB,QAAQ,GAAGA,QAAQ;MAC7B,IAAI,CAACtI,MAAM,CAACuD,KAAK,CAAC,qCAAqC,EAAE+E,QAAQ,CAAC;;IAGpE,IAAI+B,IAAI,EAAE;MACRX,SAAS,CAACW,IAAI,GAAGA,IAAI;MACrB,IAAI,CAACrK,MAAM,CAACuD,KAAK,CACf,mCAAmC8G,IAAI,CAACpF,IAAI,WAAWoF,IAAI,CAACnG,IAAI,WAAWmG,IAAI,CAACtD,IAAI,kBAAkB4D,gBAAgB,EAAE,CACzH;;IAEH,IAAI9C,cAAc,EAAE;MAClB6B,SAAS,CAAC7B,cAAc,GAAGA,cAAc;MACzC,IAAI,CAAC7H,MAAM,CAACuD,KAAK,CACf,iDAAiDsE,cAAc,EAAE,CAClE;;IAEH,IAAI2C,OAAO,EAAE;MACXd,SAAS,CAACc,OAAO,GAAGA,OAAO;MAC3B,IAAI,CAACxK,MAAM,CAACuD,KAAK,CAAC,yCAAyCiH,OAAO,EAAE,CAAC;;IAGvE,MAAMa,OAAO,GAAGhB,IAAI,GAAG;MAAEiB,YAAY,EAAE,IAAI;MAAEjB;IAAI,CAAE,GAAGzH,SAAS;IAE/D,IAAI,CAAC5C,MAAM,CAACuD,KAAK,CACf,2DAA2D,EAC3DmG,SAAS,CACV;IAED,OAAO,IAAI,CAAC3J,MAAM,CACfkK,MAAM,CAAsB;MAC3BC,QAAQ,EAAEnN,qBAAqB;MAC/B2M,SAAS;MACT2B;KACD,CAAC,CACD1G,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAI;MACb,IAAI,CAACrJ,MAAM,CAACuD,KAAK,CAAC,yCAAyC,EAAE8F,MAAM,CAAC;MAEpE,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAE4D,WAAW,EAAE;QAC7B,IAAI,CAACxI,MAAM,CAACmE,KAAK,CACf,2DAA2D,CAC5D;QACD,MAAM,IAAI4D,KAAK,CAAC,wBAAwB,CAAC;;MAG3C,IAAI;QACF,IAAI,CAAC/H,MAAM,CAACuD,KAAK,CACf,2CAA2C,EAC3C8F,MAAM,CAACzE,IAAI,CAAC4D,WAAW,CACxB;QACD,MAAM+C,iBAAiB,GAAG,IAAI,CAAC1B,gBAAgB,CAC7CR,MAAM,CAACzE,IAAI,CAAC4D,WAAW,CACxB;QAED,IAAI,CAACxI,MAAM,CAACkG,IAAI,CACd,+CAA+CqF,iBAAiB,CAAC3H,EAAE,EAAE,CACtE;QACD,OAAO2H,iBAAiB;OACzB,CAAC,OAAOC,kBAAkB,EAAE;QAC3B,IAAI,CAACxL,MAAM,CAACmE,KAAK,CACf,6CAA6C,EAC7CqH,kBAAkB,CACnB;QAED;QACA,MAAMC,cAAc,GAAY;UAC9B7H,EAAE,EAAEyF,MAAM,CAACzE,IAAI,CAAC4D,WAAW,CAAC5E,EAAE,IAAI,OAAO,GAAGqE,IAAI,CAACC,GAAG,EAAE;UACtDkC,OAAO,EAAEf,MAAM,CAACzE,IAAI,CAAC4D,WAAW,CAAC4B,OAAO,IAAI,EAAE;UAC9CrD,IAAI,EAAEsC,MAAM,CAACzE,IAAI,CAAC4D,WAAW,CAACzB,IAAI,IAAIvK,WAAW,CAAC+N,IAAI;UACtDvC,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrByD,MAAM,EAAE,KAAK;UACbC,MAAM,EAAE;YACN/H,EAAE,EAAE,IAAI,CAACgI,gBAAgB,EAAE;YAC3BC,QAAQ,EAAE;;SAEb;QAED,IAAI,CAAC7L,MAAM,CAACkG,IAAI,CACd,+CAA+CuF,cAAc,CAAC7H,EAAE,EAAE,CACnE;QACD,OAAO6H,cAAc;;IAEzB,CAAC,CAAC,EACFtP,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MACnE,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC,CAAC,CACH;EACL;EAEA+D,iBAAiBA,CAAC/B,SAAiB;IACjC,OAAO,IAAI,CAAChK,MAAM,CACfkK,MAAM,CAAqB;MAC1BC,QAAQ,EAAElN,qBAAqB;MAC/B0M,SAAS,EAAE;QAAEK;MAAS;KACvB,CAAC,CACDpF,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAEkH,iBAAiB,EACjC,MAAM,IAAI/D,KAAK,CAAC,gCAAgC,CAAC;MACnD,OAAO;QACL,GAAGsB,MAAM,CAACzE,IAAI,CAACkH,iBAAiB;QAChCC,MAAM,EAAE,IAAI9D,IAAI;OACjB;IACH,CAAC,CAAC,EACF9L,UAAU,CAAEgI,KAAK,IAAI;MACnBkC,OAAO,CAAClC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtE,CAAC,CAAC,CACH;EACL;EAEAiE,cAAcA,CAACjC,SAAiB,EAAEkC,KAAa;IAC7C,OAAO,IAAI,CAAClM,MAAM,CACfkK,MAAM,CAAyB;MAC9BC,QAAQ,EAAEpM,yBAAyB;MACnC4L,SAAS,EAAE;QAAEK,SAAS;QAAEkC;MAAK;KAC9B,CAAC,CACDtH,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAEoH,cAAc,EAC9B,MAAM,IAAIjE,KAAK,CAAC,4BAA4B,CAAC;MAC/C,OAAOsB,MAAM,CAACzE,IAAI,CAACoH,cAAc;IACnC,CAAC,CAAC,EACF7P,UAAU,CAAEgI,KAAK,IAAI;MACnBkC,OAAO,CAAClC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEAmE,cAAcA,CACZnC,SAAiB,EACjBoC,eAAyB;IAEzB,OAAO,IAAI,CAACpM,MAAM,CACfkK,MAAM,CAAyB;MAC9BC,QAAQ,EAAEnM,wBAAwB;MAClC2L,SAAS,EAAE;QAAEK,SAAS;QAAEoC;MAAe;KACxC,CAAC,CACDxH,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAEsH,cAAc,EAC9B,MAAM,IAAInE,KAAK,CAAC,2BAA2B,CAAC;MAC9C,OAAOsB,MAAM,CAACzE,IAAI,CAACsH,cAAc,CAAChQ,GAAG,CAAE0N,GAAG,KAAM;QAC9C,GAAGA,GAAG;QACN5B,SAAS,EAAE4B,GAAG,CAAC5B,SAAS,GACpB,IAAI,CAACoE,aAAa,CAACxC,GAAG,CAAC5B,SAAS,CAAC,GACjC,IAAIC,IAAI;OACb,CAAC,CAAC;IACL,CAAC,CAAC,EACF9L,UAAU,CAAEgI,KAAK,IAAI;MACnBkC,OAAO,CAAClC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACjE,CAAC,CAAC,CACH;EACL;EAEAsE,UAAUA,CAACtC,SAAiB,EAAElC,cAAsB;IAClD,OAAO,IAAI,CAAC9H,MAAM,CACfkK,MAAM,CAAqB;MAC1BC,QAAQ,EAAElM,oBAAoB;MAC9B0L,SAAS,EAAE;QAAEK,SAAS;QAAElC;MAAc;KACvC,CAAC,CACDlD,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAEyH,UAAU,EAC1B,MAAM,IAAItE,KAAK,CAAC,uBAAuB,CAAC;MAC1C,OAAO;QACL,GAAGsB,MAAM,CAACzE,IAAI,CAACyH,UAAU;QACzBC,QAAQ,EAAE,IAAIrE,IAAI;OACnB;IACH,CAAC,CAAC,EACF9L,UAAU,CAAEgI,KAAK,IAAI;MACnBkC,OAAO,CAAClC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC7D,CAAC,CAAC,CACH;EACL;EAEAwE,cAAcA,CACZ7H,KAAa,EACbmD,cAAuB,EACvB2E,OAAA,GAAyB,EAAE;IAE3B,OAAO,IAAI,CAACzM,MAAM,CACfmJ,UAAU,CAAyB;MAClCxE,KAAK,EAAEpH,qBAAqB;MAC5BoM,SAAS,EAAE;QACThF,KAAK;QACLmD,cAAc;QACd,GAAG2E,OAAO;QACVC,QAAQ,EAAE,IAAI,CAAC/J,eAAe,CAAC8J,OAAO,CAACC,QAAQ,CAAC;QAChDC,MAAM,EAAE,IAAI,CAAChK,eAAe,CAAC8J,OAAO,CAACE,MAAM;OAC5C;MACDvD,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBzI,GAAG,CACAmN,MAAM,IACLA,MAAM,CAACzE,IAAI,EAAE2H,cAAc,EAAErQ,GAAG,CAAE0N,GAAG,KAAM;MACzC,GAAGA,GAAG;MACN5B,SAAS,EAAE,IAAI,CAAC2E,QAAQ,CAAC/C,GAAG,CAAC5B,SAAS,CAAC;MACvC2D,MAAM,EAAE,IAAI,CAACiB,aAAa,CAAChD,GAAG,CAAC+B,MAAM;KACtC,CAAC,CAAC,IAAI,EAAE,CACZ,EACDxP,UAAU,CAAEgI,KAAK,IAAI;MACnBkC,OAAO,CAAClC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACjE,CAAC,CAAC,CACH;EACL;EAEA8E,iBAAiBA,CAACC,MAAc;IAC9B,OAAO,IAAI,CAAC/M,MAAM,CACfmJ,UAAU,CAA4B;MACrCxE,KAAK,EAAEnH,yBAAyB;MAChCmM,SAAS,EAAE;QAAEoD;MAAM,CAAE;MACrB3D,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBzI,GAAG,CACAmN,MAAM,IACLA,MAAM,CAACzE,IAAI,EAAEiI,iBAAiB,EAAE3Q,GAAG,CAAE0N,GAAG,KAAM;MAC5C,GAAGA,GAAG;MACN5B,SAAS,EAAE,IAAI,CAAC2E,QAAQ,CAAC/C,GAAG,CAAC5B,SAAS,CAAC;MACvC2D,MAAM,EAAE,IAAI,CAACiB,aAAa,CAAChD,GAAG,CAAC+B,MAAM;KACtC,CAAC,CAAC,IAAI,EAAE,CACZ,EACDxP,UAAU,CAAEgI,KAAK,IAAI;MACnBkC,OAAO,CAAClC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEAgF,qBAAqBA,CAAClF,cAAsB;IAC1C,IAAI,CAAC3H,kBAAkB,CAAC4D,IAAI,CAAC+D,cAAc,CAAC;EAC9C;EAEAmF,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACjN,MAAM,CACfmJ,UAAU,CAA2B;MACpCxE,KAAK,EAAE/H,uBAAuB;MAC9BwM,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBzI,GAAG,CAAEmN,MAAM,IAAI;MACb,MAAM4D,aAAa,GAAG5D,MAAM,CAACzE,IAAI,EAAEoI,gBAAgB,IAAI,EAAE;MACzD,OAAOC,aAAa,CAAC/Q,GAAG,CAAEgR,IAAI,IAAK,IAAI,CAACC,qBAAqB,CAACD,IAAI,CAAC,CAAC;IACtE,CAAC,CAAC,EACF/Q,UAAU,CAAEgI,KAAK,IAAI;MACnBkC,OAAO,CAAClC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEAqF,eAAeA,CACbvF,cAAsB,EACtBrF,KAAc,EACdiH,IAAa;IAEb,IAAI,CAACzJ,MAAM,CAACkG,IAAI,CACd,0CAA0C2B,cAAc,YAAYrF,KAAK,WAAWiH,IAAI,EAAE,CAC3F;IAED,MAAMC,SAAS,GAAQ;MAAE7B;IAAc,CAAE;IAEzC;IACA,IAAIrF,KAAK,KAAKI,SAAS,EAAE;MACvB8G,SAAS,CAAClH,KAAK,GAAGA,KAAK;KACxB,MAAM;MACLkH,SAAS,CAAClH,KAAK,GAAG,EAAE,CAAC,CAAC;;IAGxB;IACA,IAAIiH,IAAI,KAAK7G,SAAS,EAAE;MACtB;MACA,MAAMyK,MAAM,GAAG,CAAC5D,IAAI,GAAG,CAAC,IAAIC,SAAS,CAAClH,KAAK;MAC3CkH,SAAS,CAAC2D,MAAM,GAAGA,MAAM;MACzB,IAAI,CAACrN,MAAM,CAACuD,KAAK,CACf,uCAAuC8J,MAAM,eAAe5D,IAAI,eAAeC,SAAS,CAAClH,KAAK,EAAE,CACjG;KACF,MAAM;MACLkH,SAAS,CAAC2D,MAAM,GAAG,CAAC,CAAC,CAAC;;;IAGxB,IAAI,CAACrN,MAAM,CAACuD,KAAK,CACf,uDAAuDmG,SAAS,CAAClH,KAAK,YAAYkH,SAAS,CAAC2D,MAAM,EAAE,CACrG;IAED,OAAO,IAAI,CAACtN,MAAM,CACfmJ,UAAU,CAA0B;MACnCxE,KAAK,EAAE5H,sBAAsB;MAC7B4M,SAAS,EAAEA,SAAS;MACpBP,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBzI,GAAG,CAAEmN,MAAM,IAAI;MACb,IAAI,CAACrJ,MAAM,CAACuD,KAAK,CACf,kDAAkD,EAClD8F,MAAM,CACP;MAED,MAAM6D,IAAI,GAAG7D,MAAM,CAACzE,IAAI,EAAEwI,eAAe;MACzC,IAAI,CAACF,IAAI,EAAE;QACT,IAAI,CAAClN,MAAM,CAACmE,KAAK,CACf,4CAA4C0D,cAAc,EAAE,CAC7D;QACD,MAAM,IAAIE,KAAK,CAAC,wBAAwB,CAAC;;MAG3C,IAAI,CAAC/H,MAAM,CAACuD,KAAK,CACf,8CAA8CsE,cAAc,EAAE,CAC/D;MACD,MAAMyF,sBAAsB,GAAG,IAAI,CAACH,qBAAqB,CAACD,IAAI,CAAC;MAE/D,IAAI,CAAClN,MAAM,CAACkG,IAAI,CACd,sDAAsD2B,cAAc,mBAClEyF,sBAAsB,CAACC,YAAY,EAAE/J,MAAM,IAAI,CACjD,eAAe8J,sBAAsB,CAAC3D,QAAQ,EAAEnG,MAAM,IAAI,CAAC,EAAE,CAC9D;MACD,OAAO8J,sBAAsB;IAC/B,CAAC,CAAC,EACFnR,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,+CAA+C,EAC/CA,KAAK,CACN;MACD,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACnE,CAAC,CAAC,CACH;EACL;EAEAyF,kBAAkBA,CAACV,MAAc;IAC/B,IAAI,CAAC9M,MAAM,CAACkG,IAAI,CACd,qDAAqD4G,MAAM,EAAE,CAC9D;IAED,IAAI,CAACA,MAAM,EAAE;MACX,IAAI,CAAC9M,MAAM,CAACmE,KAAK,CACf,kEAAkE,CACnE;MACD,OAAOpI,UAAU,CACf,MAAM,IAAIgM,KAAK,CAAC,8CAA8C,CAAC,CAChE;;IAGH,OAAO,IAAI,CAAChI,MAAM,CACfkK,MAAM,CAAuC;MAC5CC,QAAQ,EAAErL,4BAA4B;MACtC6K,SAAS,EAAE;QAAEoD;MAAM;KACpB,CAAC,CACDnI,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAI;MACb,IAAI,CAACrJ,MAAM,CAACuD,KAAK,CACf,kDAAkD,EAClD8F,MAAM,CACP;MAED,MAAMoE,YAAY,GAAGpE,MAAM,CAACzE,IAAI,EAAE4I,kBAAkB;MACpD,IAAI,CAACC,YAAY,EAAE;QACjB,IAAI,CAACzN,MAAM,CAACmE,KAAK,CACf,6DAA6D2I,MAAM,EAAE,CACtE;QACD,MAAM,IAAI/E,KAAK,CAAC,+BAA+B,CAAC;;MAGlD,IAAI;QACF,MAAMuF,sBAAsB,GAC1B,IAAI,CAACH,qBAAqB,CAACM,YAAY,CAAC;QAC1C,IAAI,CAACzN,MAAM,CAACkG,IAAI,CACd,uDAAuDoH,sBAAsB,CAAC1J,EAAE,EAAE,CACnF;QACD,OAAO0J,sBAAsB;OAC9B,CAAC,OAAOnJ,KAAK,EAAE;QACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,0DAA0D,EAC1DA,KAAK,CACN;QACD,MAAM,IAAI4D,KAAK,CAAC,uCAAuC,CAAC;;IAE5D,CAAC,CAAC,EACF5L,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,0DAA0D2I,MAAM,GAAG,EACnE3I,KAAK,CACN;MACD,OAAOpI,UAAU,CACf,MAAM,IAAIgM,KAAK,CAAC,kCAAkC5D,KAAK,CAACuJ,OAAO,EAAE,CAAC,CACnE;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKAC,uBAAuBA,CAACb,MAAc;IACpC,IAAI,CAAC9M,MAAM,CAACkG,IAAI,CACd,gEAAgE4G,MAAM,EAAE,CACzE;IAED,IAAI,CAACA,MAAM,EAAE;MACX,IAAI,CAAC9M,MAAM,CAACmE,KAAK,CACf,sEAAsE,CACvE;MACD,OAAOpI,UAAU,CACf,MAAM,IAAIgM,KAAK,CAAC,kDAAkD,CAAC,CACpE;;IAGH;IACA,OAAO,IAAI,CAACiF,gBAAgB,EAAE,CAACrI,IAAI,CACjCzI,GAAG,CAAE+Q,aAAa,IAAI;MACpB;MACA,MAAMW,aAAa,GAAG,IAAI,CAAChC,gBAAgB,EAAE;MAE7C;MACA,MAAMiC,oBAAoB,GAAGZ,aAAa,CAACa,IAAI,CAAEZ,IAAI,IAAI;QACvD,IAAIA,IAAI,CAACa,OAAO,EAAE,OAAO,KAAK;QAE9B;QACA,MAAMC,cAAc,GAClBd,IAAI,CAACK,YAAY,EAAErR,GAAG,CAAE+R,CAAC,IAAKA,CAAC,CAACrK,EAAE,IAAIqK,CAAC,CAACC,GAAG,CAAC,IAAI,EAAE;QACpD,OACEF,cAAc,CAACG,QAAQ,CAACrB,MAAM,CAAC,IAC/BkB,cAAc,CAACG,QAAQ,CAACP,aAAa,CAAC;MAE1C,CAAC,CAAC;MAEF,IAAIC,oBAAoB,EAAE;QACxB,IAAI,CAAC7N,MAAM,CAACkG,IAAI,CACd,iDAAiD2H,oBAAoB,CAACjK,EAAE,EAAE,CAC3E;QACD,OAAOiK,oBAAoB;;MAG7B;MACA,MAAM,IAAI9F,KAAK,CAAC,gCAAgC,CAAC;IACnD,CAAC,CAAC,EACF5L,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACkG,IAAI,CACd,sEAAsE/B,KAAK,CAACuJ,OAAO,EAAE,CACtF;MACD,OAAO,IAAI,CAACF,kBAAkB,CAACV,MAAM,CAAC;IACxC,CAAC,CAAC,CACH;EACH;EAYAsB,gBAAgBA,CACdC,OAAO,GAAG,KAAK,EACf5E,IAAI,GAAG,CAAC,EACRjH,KAAK,GAAG,EAAE;IAEV,IAAI,CAACxC,MAAM,CAACkG,IAAI,CACd,gBAAgB,EAChB,oCAAoCmI,OAAO,WAAW5E,IAAI,YAAYjH,KAAK,EAAE,CAC9E;IACD,IAAI,CAACxC,MAAM,CAACuD,KAAK,CAAC,gBAAgB,EAAE,aAAa,EAAE;MACjDmB,KAAK,EAAE9H;KACR,CAAC;IAEF;IACA;IACA,IAAIyR,OAAO,EAAE;MACX,IAAI,CAACrO,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,qCAAqC,CACtC;MACD,IAAI,CAAChB,sBAAsB,CAACT,WAAW,GAAG,CAAC;MAC3C,IAAI,CAACS,sBAAsB,CAACE,oBAAoB,GAAG,IAAI;;IAGzD;IACA,IAAI,CAACF,sBAAsB,CAACT,WAAW,GAAG2H,IAAI;IAC9C,IAAI,CAAClH,sBAAsB,CAACC,KAAK,GAAGA,KAAK;IAEzC;IACA,MAAM8L,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAC/D,IAAI,CAACvO,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,SAAS+K,sBAAsB,CAACpK,IAAI,2CAA2C,CAChF;IAED,OAAO,IAAI,CAACnE,MAAM,CACfmJ,UAAU,CAA+B;MACxCxE,KAAK,EAAE9H,uBAAuB;MAC9B8M,SAAS,EAAE;QACTD,IAAI,EAAEA,IAAI;QACVjH,KAAK,EAAEA;OACR;MACD2G,WAAW,EAAEkF,OAAO,GAAG,cAAc,GAAG;KACzC,CAAC,CACDjF,YAAY,CAACzE,IAAI,CAChBzI,GAAG,CAAEmN,MAAM,IAAI;MACb,IAAI,CAACrJ,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,iCAAiC,CAClC;MAED,IAAI8F,MAAM,CAACmF,MAAM,EAAE;QACjB,IAAI,CAACxO,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjBkF,MAAM,CAACmF,MAAM,CACd;QACD,MAAM,IAAIzG,KAAK,CAACsB,MAAM,CAACmF,MAAM,CAACtS,GAAG,CAAEuS,CAAC,IAAKA,CAAC,CAACf,OAAO,CAAC,CAACgB,IAAI,CAAC,IAAI,CAAC,CAAC;;MAGjE,MAAMvO,aAAa,GAAGkJ,MAAM,CAACzE,IAAI,EAAE+J,oBAAoB,IAAI,EAAE;MAC7D,IAAI,CAAC3O,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,YAAYpD,aAAa,CAACqD,MAAM,uCAAuCiG,IAAI,EAAE,CAC9E;MAED;MACA,IAAI,CAAClH,sBAAsB,CAACE,oBAAoB,GAC9CtC,aAAa,CAACqD,MAAM,IAAIhB,KAAK;MAE/B,IAAIrC,aAAa,CAACqD,MAAM,KAAK,CAAC,EAAE;QAC9B,IAAI,CAACxD,MAAM,CAACkG,IAAI,CACd,gBAAgB,EAChB,uCAAuC,CACxC;QACD,IAAI,CAAC3D,sBAAsB,CAACE,oBAAoB,GAAG,KAAK;;MAG1D;MACA,MAAMmM,qBAAqB,GAAGzO,aAAa,CAAC9D,MAAM,CAC/CwS,KAAK,IAAK,CAACP,sBAAsB,CAACQ,GAAG,CAACD,KAAK,CAACjL,EAAE,CAAC,CACjD;MAED,IAAI,CAAC5D,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,gBACEpD,aAAa,CAACqD,MAAM,GAAGoL,qBAAqB,CAACpL,MAC/C,wBAAwB,CACzB;MAED;MACAoL,qBAAqB,CAAClL,OAAO,CAAC,CAACmL,KAAK,EAAEE,KAAK,KAAI;QAC7C1I,OAAO,CAACC,GAAG,CAAC,gBAAgByI,KAAK,GAAG,CAAC,UAAUtF,IAAI,IAAI,EAAE;UACvD7F,EAAE,EAAEiL,KAAK,CAACjL,EAAE,IAAKiL,KAAa,CAACX,GAAG;UAClCnH,IAAI,EAAE8H,KAAK,CAAC9H,IAAI;UAChBqD,OAAO,EAAEyE,KAAK,CAACzE,OAAO;UACtBsB,MAAM,EAAEmD,KAAK,CAACnD;SACf,CAAC;MACJ,CAAC,CAAC;MAEF;MACA;MACA,IAAI,CAACsD,WAAW,CAACJ,qBAAqB,CAAC;MAEvC;MACA,MAAMK,mBAAmB,GAAGlL,KAAK,CAACxH,IAAI,CACpC,IAAI,CAAC6D,iBAAiB,CAAC4D,MAAM,EAAE,CAChC;MAEDqC,OAAO,CAACC,GAAG,CACT,8CAA8C2I,mBAAmB,CAACzL,MAAM,EAAE,CAC3E;MAED;MACA,IAAI,CAACrD,aAAa,CAAC2D,IAAI,CAACmL,mBAAmB,CAAC;MAE5C;MACA,IAAI,CAAChL,iBAAiB,EAAE;MAExB;MACA,IAAI,CAACiL,+BAA+B,EAAE;MAEtC,OAAOD,mBAAmB;IAC5B,CAAC,CAAC,EACF9S,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,8BAA8B,EAC9BA,KAAK,CACN;MAED,IAAIA,KAAK,CAACgL,aAAa,EAAE;QACvB,IAAI,CAACnP,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjBA,KAAK,CAACgL,aAAa,CACpB;;MAGH,IAAIhL,KAAK,CAACiL,YAAY,EAAE;QACtB,IAAI,CAACpP,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,gBAAgB,EAChBA,KAAK,CAACiL,YAAY,CACnB;;MAGH,OAAOrT,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKQwG,yBAAyBA,CAAA;IAC/B,IAAI;MACF,MAAMc,UAAU,GAAG,IAAIC,GAAG,EAAU;MACpC,MAAMpM,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;MAEhE;MACA,IAAI,CAACF,kBAAkB,EAAE;QACvB,OAAOmM,UAAU;;MAGnB;MACA,MAAME,oBAAoB,GAAG,IAAID,GAAG,CAClCjM,IAAI,CAACC,KAAK,CAACJ,kBAAkB,CAAC,CAAChH,GAAG,CAAEsT,CAAe,IAAKA,CAAC,CAAC5L,EAAE,CAAC,CAC9D;MAED;MACA,MAAM6L,mBAAmB,GACvB,IAAI,CAAC1P,MAAM,CAAC2P,MAAM,CAACC,SAAS,CAA+B;QACzDjL,KAAK,EAAE9H;OACR,CAAC,EAAE+R,oBAAoB,IAAI,EAAE;MAEhC;MACAc,mBAAmB,CAAC/L,OAAO,CAAEC,YAAY,IAAI;QAC3C,IAAI,CAAC4L,oBAAoB,CAACT,GAAG,CAACnL,YAAY,CAACC,EAAE,CAAC,EAAE;UAC9CyL,UAAU,CAACO,GAAG,CAACjM,YAAY,CAACC,EAAE,CAAC;;MAEnC,CAAC,CAAC;MAEF,OAAOyL,UAAU;KAClB,CAAC,OAAOlL,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,qEAAqE,EACrEA,KAAK,CACN;MACD,OAAO,IAAImL,GAAG,EAAU;;EAE5B;EAEA;EACA7M,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACF,sBAAsB,CAACE,oBAAoB;EACzD;EAEA;EACAoN,qBAAqBA,CAAA;IACnB,MAAMC,QAAQ,GAAG,IAAI,CAACvN,sBAAsB,CAACT,WAAW,GAAG,CAAC;IAC5D,OAAO,IAAI,CAACsM,gBAAgB,CAC1B,KAAK,EACL0B,QAAQ,EACR,IAAI,CAACvN,sBAAsB,CAACC,KAAK,CAClC;EACH;EACAuN,mBAAmBA,CAACnM,EAAU;IAC5B,OAAO,IAAI,CAAC1B,cAAc,CAACyC,IAAI,CAC7BzI,GAAG,CAAEiE,aAAa,IAAKA,aAAa,CAAC2N,IAAI,CAAE0B,CAAC,IAAKA,CAAC,CAAC5L,EAAE,KAAKA,EAAE,CAAC,CAAC,EAC9DzH,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACvD,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACnE,CAAC,CAAC,CACH;EACH;EACAiI,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC7P,aAAa,CAAC+K,KAAK,EAAE1H,MAAM,IAAI,CAAC;EAC9C;EACAyM,0BAA0BA,CAACC,cAAsB;IAC/C,OAAO,IAAI,CAACnQ,MAAM,CACf2E,KAAK,CAAkC;MACtCA,KAAK,EAAEhG,8BAA8B;MACrCgL,SAAS,EAAE;QAAE9F,EAAE,EAAEsM;MAAc,CAAE;MACjC/G,WAAW,EAAE;KACd,CAAC,CACDxE,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAKA,MAAM,CAACzE,IAAI,EAAEqL,0BAA0B,IAAI,EAAE,CAAC,EAC9D9T,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MACpE,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACnE,CAAC,CAAC,CACH;EACL;EACAoI,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAACjO,cAAc,CAACyC,IAAI,CAC7BzI,GAAG,CAAEiE,aAAa,IAAKA,aAAa,CAAC9D,MAAM,CAAEmT,CAAC,IAAK,CAACA,CAAC,CAAC9D,MAAM,CAAC,CAAC,CAC/D;EACH;EAEA;;;;;EAKA0E,kBAAkBA,CAChBF,cAAsB;IAEtB,IAAI,CAAClQ,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,kCAAkC2M,cAAc,EAAE,CACnD;IAED,IAAI,CAACA,cAAc,EAAE;MACnB,IAAI,CAAClQ,MAAM,CAACyF,IAAI,CAAC,gBAAgB,EAAE,6BAA6B,CAAC;MACjE,OAAO1J,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,6BAA6B,CAAC,CAAC;;IAGnE;IACA,IAAI,CAAC3H,iBAAiB,CAACiQ,MAAM,CAACH,cAAc,CAAC;IAC7C,IAAI,CAAC/P,aAAa,CAAC2D,IAAI,CAACC,KAAK,CAACxH,IAAI,CAAC,IAAI,CAAC6D,iBAAiB,CAAC4D,MAAM,EAAE,CAAC,CAAC;IACpE,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACiL,+BAA+B,EAAE;IAEtC;IACA,OAAO,IAAI,CAACnP,MAAM,CACfkK,MAAM,CAAgE;MACrEC,QAAQ,EAAEpL,4BAA4B;MACtC4K,SAAS,EAAE;QAAEwG;MAAc;KAC5B,CAAC,CACDvL,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAI;MACb,MAAMiH,QAAQ,GAAGjH,MAAM,CAACzE,IAAI,EAAEwL,kBAAkB;MAChD,IAAI,CAACE,QAAQ,EAAE;QACb,MAAM,IAAIvI,KAAK,CAAC,iCAAiC,CAAC;;MAGpD,IAAI,CAAC/H,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,6BAA6B,EAC7B+M,QAAQ,CACT;MAED,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACFnU,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,mDAAmD,EACnDA,KAAK,CACN;MAED;MACA,OAAOrI,EAAE,CAAC;QACRyU,OAAO,EAAE,IAAI;QACb7C,OAAO,EAAE;OACV,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EAEA;;;;EAIQwB,+BAA+BA,CAAA;IACrC,IAAI;MACF,MAAM/O,aAAa,GAAG4D,KAAK,CAACxH,IAAI,CAAC,IAAI,CAAC6D,iBAAiB,CAAC4D,MAAM,EAAE,CAAC;MACjEb,YAAY,CAACqN,OAAO,CAAC,eAAe,EAAEnN,IAAI,CAACoN,SAAS,CAACtQ,aAAa,CAAC,CAAC;MACpE,IAAI,CAACH,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,uCAAuC,CACxC;KACF,CAAC,OAAOY,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iDAAiD,EACjDA,KAAK,CACN;;EAEL;EAEA;;;;EAIAuM,sBAAsBA,CAAA;IAKpB,IAAI,CAAC1Q,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,yCAAyC,CAC1C;IAED;IACA,MAAMoN,KAAK,GAAG,IAAI,CAACvQ,iBAAiB,CAAC8D,IAAI;IACzC,IAAI,CAAC9D,iBAAiB,CAACqD,KAAK,EAAE;IAC9B,IAAI,CAACtD,aAAa,CAAC2D,IAAI,CAAC,EAAE,CAAC;IAC3B,IAAI,CAACxD,iBAAiB,CAACwD,IAAI,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACoL,+BAA+B,EAAE;IAEtC;IACA,OAAO,IAAI,CAACnP,MAAM,CACfkK,MAAM,CAMJ;MACDC,QAAQ,EAAElL;KACX,CAAC,CACD2F,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAI;MACb,MAAMiH,QAAQ,GAAGjH,MAAM,CAACzE,IAAI,EAAE8L,sBAAsB;MACpD,IAAI,CAACJ,QAAQ,EAAE;QACb,MAAM,IAAIvI,KAAK,CAAC,iCAAiC,CAAC;;MAGpD,IAAI,CAAC/H,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,yDAAyD,EACzD+M,QAAQ,CACT;MAED,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACFnU,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,4DAA4D,EAC5DA,KAAK,CACN;MAED;MACA,OAAOrI,EAAE,CAAC;QACRyU,OAAO,EAAE,IAAI;QACbI,KAAK;QACLjD,OAAO,EAAE,GAAGiD,KAAK;OAClB,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKAC,2BAA2BA,CACzBC,eAAyB;IAEzB,IAAI,CAAC7Q,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,kBAAkBsN,eAAe,CAACrN,MAAM,gBAAgB,CACzD;IAED,IAAI,CAACqN,eAAe,IAAIA,eAAe,CAACrN,MAAM,KAAK,CAAC,EAAE;MACpD,IAAI,CAACxD,MAAM,CAACyF,IAAI,CAAC,gBAAgB,EAAE,iCAAiC,CAAC;MACrE,OAAO1J,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,iCAAiC,CAAC,CAAC;;IAGvE;IACA,IAAI4I,KAAK,GAAG,CAAC;IACbE,eAAe,CAACnN,OAAO,CAAEE,EAAE,IAAI;MAC7B,IAAI,IAAI,CAACxD,iBAAiB,CAAC0O,GAAG,CAAClL,EAAE,CAAC,EAAE;QAClC,IAAI,CAACxD,iBAAiB,CAACiQ,MAAM,CAACzM,EAAE,CAAC;QACjC+M,KAAK,EAAE;;IAEX,CAAC,CAAC;IAEF,IAAI,CAACxQ,aAAa,CAAC2D,IAAI,CAACC,KAAK,CAACxH,IAAI,CAAC,IAAI,CAAC6D,iBAAiB,CAAC4D,MAAM,EAAE,CAAC,CAAC;IACpE,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACiL,+BAA+B,EAAE;IAEtC;IACA,OAAO,IAAI,CAACnP,MAAM,CACfkK,MAAM,CAMJ;MACDC,QAAQ,EAAEnL,sCAAsC;MAChD2K,SAAS,EAAE;QAAEmH;MAAe;KAC7B,CAAC,CACDlM,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAI;MACb,MAAMiH,QAAQ,GAAGjH,MAAM,CAACzE,IAAI,EAAEgM,2BAA2B;MACzD,IAAI,CAACN,QAAQ,EAAE;QACb,MAAM,IAAIvI,KAAK,CAAC,iCAAiC,CAAC;;MAGpD,IAAI,CAAC/H,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,sCAAsC,EACtC+M,QAAQ,CACT;MAED,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACFnU,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,0DAA0D,EAC1DA,KAAK,CACN;MAED;MACA,OAAOrI,EAAE,CAAC;QACRyU,OAAO,EAAEI,KAAK,GAAG,CAAC;QAClBA,KAAK;QACLjD,OAAO,EAAE,GAAGiD,KAAK;OAClB,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EACAG,wBAAwBA,CAAA;IAGtB,OAAO,IAAI,CAAC5O,cAAc,CAACyC,IAAI,CAC7BzI,GAAG,CAAEiE,aAAa,IAAI;MACpB,MAAM4Q,MAAM,GAAG,IAAI1Q,GAAG,EAAoC;MAC1DF,aAAa,CAACuD,OAAO,CAAEmL,KAAK,IAAI;QAC9B,IAAI,CAACkC,MAAM,CAACjC,GAAG,CAACD,KAAK,CAAC9H,IAAI,CAAC,EAAE;UAC3BgK,MAAM,CAAClN,GAAG,CAACgL,KAAK,CAAC9H,IAAI,EAAE,EAAE,CAAC;;QAE5BgK,MAAM,CAACC,GAAG,CAACnC,KAAK,CAAC9H,IAAI,CAAC,EAAEkK,IAAI,CAACpC,KAAK,CAAC;MACrC,CAAC,CAAC;MACF,OAAOkC,MAAM;IACf,CAAC,CAAC,CACH;EACH;EACAG,UAAUA,CAACL,eAAyB;IAKlC,IAAI,CAAC7Q,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,kCAAkCsN,eAAe,EAAEnC,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE,CAC1E;IAED,IAAI,CAACmC,eAAe,IAAIA,eAAe,CAACrN,MAAM,KAAK,CAAC,EAAE;MACpD,IAAI,CAACxD,MAAM,CAACyF,IAAI,CAAC,gBAAgB,EAAE,8BAA8B,CAAC;MAClE,OAAO3J,EAAE,CAAC;QACRyU,OAAO,EAAE,KAAK;QACdY,SAAS,EAAE,CAAC;QACZC,cAAc,EAAE,IAAI,CAAC9Q,iBAAiB,CAAC4K;OACxC,CAAC;;IAGJ;IACA,MAAMmG,QAAQ,GAAGR,eAAe,CAACxU,MAAM,CACpCuH,EAAE,IAAKA,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,IAAIA,EAAE,CAAC0N,IAAI,EAAE,KAAK,EAAE,CACzD;IAED,IAAID,QAAQ,CAAC7N,MAAM,KAAKqN,eAAe,CAACrN,MAAM,EAAE;MAC9C,IAAI,CAACxD,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,mCAAmC,EAAE;QACvEoN,QAAQ,EAAEV,eAAe;QACzBW,KAAK,EAAEH;OACR,CAAC;MACF,OAAOtV,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,mCAAmC,CAAC,CAAC;;IAGzE,IAAI,CAAC/H,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,gDAAgD,EAChD8N,QAAQ,CACT;IAED;IACA,IAAI,CAACI,wBAAwB,CAACJ,QAAQ,EAAE,IAAI,CAAC;IAE7C;IACA,MAAMK,kBAAkB,GAAG;MACzBC,uBAAuB,EAAE;QACvBpB,OAAO,EAAE,IAAI;QACbY,SAAS,EAAEE,QAAQ,CAAC7N,MAAM;QAC1B4N,cAAc,EAAEQ,IAAI,CAACC,GAAG,CACtB,CAAC,EACD,IAAI,CAACvR,iBAAiB,CAAC4K,KAAK,GAAGmG,QAAQ,CAAC7N,MAAM;;KAGnD;IAED;IACA6C,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAE;MACtEuK,eAAe,EAAEQ;KAClB,CAAC;IACFhL,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE3H,+BAA+B,CAAC;IAE/D,OAAO,IAAI,CAACoB,MAAM,CACfkK,MAAM,CAAkC;MACvCC,QAAQ,EAAEvL,+BAA+B;MACzC+K,SAAS,EAAE;QAAEmH,eAAe,EAAEQ;MAAQ,CAAE;MACxCK,kBAAkB,EAAEA,kBAAkB;MACtCI,WAAW,EAAE,KAAK;MAClB3I,WAAW,EAAE,UAAU,CAAE;KAC1B,CAAC,CACDxE,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAI;MACb,IAAI,CAACrJ,MAAM,CAACuD,KAAK,CAAC,gBAAgB,EAAE,iBAAiB,EAAE8F,MAAM,CAAC;MAC9DhD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE+C,MAAM,CAAC;MAEvC;MACA,IAAIA,MAAM,CAACmF,MAAM,EAAE;QACjB,IAAI,CAACxO,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjBkF,MAAM,CAACmF,MAAM,CACd;QACDnI,OAAO,CAAClC,KAAK,CAAC,iBAAiB,EAAEkF,MAAM,CAACmF,MAAM,CAAC;;MAGjD;MACA,MAAM8B,QAAQ,GACZjH,MAAM,CAACzE,IAAI,EAAE+M,uBAAuB,IACpCD,kBAAkB,CAACC,uBAAuB;MAE5C,OAAOrB,QAAQ;IACjB,CAAC,CAAC,EACFnU,UAAU,CAAEgI,KAAY,IAAI;MAC1B,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,sCAAsC,EACtCA,KAAK,CACN;MACDkC,OAAO,CAAClC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAE5C;MACA;MACA,OAAOrI,EAAE,CAAC;QACRyU,OAAO,EAAE,IAAI;QACbY,SAAS,EAAEE,QAAQ,CAAC7N,MAAM;QAC1B4N,cAAc,EAAEQ,IAAI,CAACC,GAAG,CACtB,CAAC,EACD,IAAI,CAACvR,iBAAiB,CAAC4K,KAAK,GAAGmG,QAAQ,CAAC7N,MAAM;OAEjD,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EACA;EACA;EACA;EAEA;;;;;;;;EAQAuO,YAAYA,CACVC,WAAmB,EACnBC,QAAkB,EAClBpK,cAAuB,EACvBqK,OAAqB;IAErB,OAAO,IAAI,CAACC,iBAAiB,CAACF,QAAQ,CAAC,CAACtN,IAAI,CAC1CrI,SAAS,CAAE8V,MAAM,IAAI;MACnB,IAAI,CAACtR,WAAW,GAAGsR,MAAM;MACzB,IAAI,CAAC/Q,YAAY,CAACyC,IAAI,CAACsO,MAAM,CAAC;MAE9B;MACA,IAAI,CAACpR,cAAc,GAAG,IAAIqR,iBAAiB,CAAC,IAAI,CAAC9Q,SAAS,CAAC;MAE3D;MACA6Q,MAAM,CAACE,SAAS,EAAE,CAAC5O,OAAO,CAAE6O,KAAK,IAAI;QACnC,IAAI,CAACvR,cAAe,CAACwR,QAAQ,CAACD,KAAK,EAAEH,MAAM,CAAC;MAC9C,CAAC,CAAC;MAEF;MACA,IAAI,CAACpR,cAAc,CAACyR,cAAc,GAAIC,KAAK,IAAI;QAC7C,IAAIA,KAAK,CAACC,SAAS,EAAE;UACnB,IAAI,CAACC,cAAc,CACjB,IAAI,CAACC,cAAc,EAAE,EACrB,eAAe,EACfxP,IAAI,CAACoN,SAAS,CAACiC,KAAK,CAACC,SAAS,CAAC,CAChC;;MAEL,CAAC;MAED;MACA,IAAI,CAAC3R,cAAc,CAAC8R,OAAO,GAAIJ,KAAK,IAAI;QACtC,IAAI,CAAC,IAAI,CAAC3R,YAAY,EAAE;UACtB,IAAI,CAACA,YAAY,GAAG,IAAIgS,WAAW,EAAE;UACrC,IAAI,CAACzR,aAAa,CAACwC,IAAI,CAAC,IAAI,CAAC/C,YAAY,CAAC;;QAE5C2R,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAACV,SAAS,EAAE,CAAC5O,OAAO,CAAE6O,KAAK,IAAI;UAC7C,IAAI,CAACxR,YAAa,CAACyR,QAAQ,CAACD,KAAK,CAAC;QACpC,CAAC,CAAC;MACJ,CAAC;MAED;MACA,OAAOhW,IAAI,CAAC,IAAI,CAACyE,cAAc,CAACiS,WAAW,EAAE,CAAC,CAACtO,IAAI,CACjDrI,SAAS,CAAE4W,KAAK,IAAI;QAClB,OAAO3W,IAAI,CAAC,IAAI,CAACyE,cAAe,CAACmS,mBAAmB,CAACD,KAAK,CAAC,CAAC,CAACvO,IAAI,CAC/DzI,GAAG,CAAC,MAAMgX,KAAK,CAAC,CACjB;MACH,CAAC,CAAC,CACH;IACH,CAAC,CAAC,EACF5W,SAAS,CAAE4W,KAAK,IAAI;MAClB;MACA,MAAME,MAAM,GAAG,IAAI,CAACP,cAAc,EAAE;MAEpC;MACA,OAAO,IAAI,CAAC9S,MAAM,CACfkK,MAAM,CAAyB;QAC9BC,QAAQ,EAAE9K,sBAAsB;QAChCsK,SAAS,EAAE;UACTsI,WAAW;UACXC,QAAQ;UACRmB,MAAM;UACNF,KAAK,EAAE7P,IAAI,CAACoN,SAAS,CAACyC,KAAK,CAAC;UAC5BrL,cAAc;UACdqK;;OAEH,CAAC,CACDvN,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAI;QACb,MAAMvE,IAAI,GAAGuE,MAAM,CAACzE,IAAI,EAAEmN,YAAY;QACtC,IAAI,CAACjN,IAAI,EAAE;UACT,MAAM,IAAIiD,KAAK,CAAC,yBAAyB,CAAC;;QAG5C;QACA,IAAI,CAACpH,UAAU,CAACmD,IAAI,CAACgB,IAAI,CAAC;QAE1B;QACA,MAAMuO,SAAS,GAAG,IAAI,CAACC,sBAAsB,CAC3CxO,IAAI,CAAClB,EAAE,CACR,CAACU,SAAS,EAAE;QACb,IAAI,CAAC9D,aAAa,CAACyQ,IAAI,CAACoC,SAAS,CAAC;QAElC,OAAOvO,IAAI;MACb,CAAC,CAAC,CACH;IACL,CAAC,CAAC,EACF3I,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,IAAI,CAACoP,WAAW,EAAE;MAClB,OAAOxX,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC/D,CAAC,CAAC,CACH;EACH;EAEA;;;;;EAKAyL,UAAUA,CAAC5S,YAA0B;IACnC,IAAI,CAACgF,IAAI,CAAC,UAAU,CAAC;IAErB,OAAO,IAAI,CAACuM,iBAAiB,CAACvR,YAAY,CAACmG,IAAI,CAAC,CAACpC,IAAI,CACnDrI,SAAS,CAAE8V,MAAM,IAAI;MACnB,IAAI,CAACtR,WAAW,GAAGsR,MAAM;MACzB,IAAI,CAAC/Q,YAAY,CAACyC,IAAI,CAACsO,MAAM,CAAC;MAE9B;MACA,IAAI,CAACpR,cAAc,GAAG,IAAIqR,iBAAiB,CAAC,IAAI,CAAC9Q,SAAS,CAAC;MAE3D;MACA6Q,MAAM,CAACE,SAAS,EAAE,CAAC5O,OAAO,CAAE6O,KAAK,IAAI;QACnC,IAAI,CAACvR,cAAe,CAACwR,QAAQ,CAACD,KAAK,EAAEH,MAAM,CAAC;MAC9C,CAAC,CAAC;MAEF;MACA,IAAI,CAACpR,cAAc,CAACyR,cAAc,GAAIC,KAAK,IAAI;QAC7C,IAAIA,KAAK,CAACC,SAAS,EAAE;UACnB,IAAI,CAACC,cAAc,CACjBhS,YAAY,CAACgD,EAAE,EACf,eAAe,EACfP,IAAI,CAACoN,SAAS,CAACiC,KAAK,CAACC,SAAS,CAAC,CAChC;;MAEL,CAAC;MAED;MACA,IAAI,CAAC3R,cAAc,CAAC8R,OAAO,GAAIJ,KAAK,IAAI;QACtC,IAAI,CAAC,IAAI,CAAC3R,YAAY,EAAE;UACtB,IAAI,CAACA,YAAY,GAAG,IAAIgS,WAAW,EAAE;UACrC,IAAI,CAACzR,aAAa,CAACwC,IAAI,CAAC,IAAI,CAAC/C,YAAY,CAAC;;QAE5C2R,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAACV,SAAS,EAAE,CAAC5O,OAAO,CAAE6O,KAAK,IAAI;UAC7C,IAAI,CAACxR,YAAa,CAACyR,QAAQ,CAACD,KAAK,CAAC;QACpC,CAAC,CAAC;MACJ,CAAC;MAED;MACA,MAAMW,KAAK,GAAG7P,IAAI,CAACC,KAAK,CAAC1C,YAAY,CAACsS,KAAK,CAAC;MAC5C,OAAO3W,IAAI,CACT,IAAI,CAACyE,cAAc,CAACyS,oBAAoB,CACtC,IAAIC,qBAAqB,CAACR,KAAK,CAAC,CACjC,CACF,CAACvO,IAAI,CACJrI,SAAS,CAAC,MAAMC,IAAI,CAAC,IAAI,CAACyE,cAAe,CAAC2S,YAAY,EAAE,CAAC,CAAC,EAC1DrX,SAAS,CAAEsX,MAAM,IAAI;QACnB,OAAOrX,IAAI,CAAC,IAAI,CAACyE,cAAe,CAACmS,mBAAmB,CAACS,MAAM,CAAC,CAAC,CAACjP,IAAI,CAChEzI,GAAG,CAAC,MAAM0X,MAAM,CAAC,CAClB;MACH,CAAC,CAAC,CACH;IACH,CAAC,CAAC,EACFtX,SAAS,CAAEsX,MAAM,IAAI;MACnB;MACA,OAAO,IAAI,CAAC7T,MAAM,CACfkK,MAAM,CAAuB;QAC5BC,QAAQ,EAAE5K,oBAAoB;QAC9BoK,SAAS,EAAE;UACT0J,MAAM,EAAExS,YAAY,CAACgD,EAAE;UACvBgQ,MAAM,EAAEvQ,IAAI,CAACoN,SAAS,CAACmD,MAAM;;OAEhC,CAAC,CACDjP,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAI;QACb,MAAMvE,IAAI,GAAGuE,MAAM,CAACzE,IAAI,EAAE4O,UAAU;QACpC,IAAI,CAAC1O,IAAI,EAAE;UACT,MAAM,IAAIiD,KAAK,CAAC,uBAAuB,CAAC;;QAG1C;QACA,IAAI,CAAChD,IAAI,CAAC,gBAAgB,CAAC;QAE3B;QACA,IAAI,CAACpE,UAAU,CAACmD,IAAI,CAAC;UACnB,GAAGgB,IAAI;UACP+O,MAAM,EAAEjT,YAAY,CAACiT,MAAM;UAC3B9M,IAAI,EAAEnG,YAAY,CAACmG,IAAI;UACvBc,cAAc,EAAEjH,YAAY,CAACiH;SAC9B,CAAC;QAEF;QACA,MAAMwL,SAAS,GAAG,IAAI,CAACC,sBAAsB,CAC3C1S,YAAY,CAACgD,EAAE,CAChB,CAACU,SAAS,EAAE;QACb,IAAI,CAAC9D,aAAa,CAACyQ,IAAI,CAACoC,SAAS,CAAC;QAElC;QACA,IAAI,CAACzS,YAAY,CAACkD,IAAI,CAAC,IAAI,CAAC;QAE5B,OAAOgB,IAAI;MACb,CAAC,CAAC,CACH;IACL,CAAC,CAAC,EACF3I,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAChD,IAAI,CAACoP,WAAW,EAAE;MAClB,OAAOxX,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC7D,CAAC,CAAC,CACH;EACH;EAEA;;;;;;EAMA+L,UAAUA,CAACV,MAAc,EAAEW,MAAe;IACxC,IAAI,CAACnO,IAAI,CAAC,UAAU,CAAC;IAErB,OAAO,IAAI,CAAC7F,MAAM,CACfkK,MAAM,CAAuB;MAC5BC,QAAQ,EAAE3K,oBAAoB;MAC9BmK,SAAS,EAAE;QACT0J,MAAM;QACNW;;KAEH,CAAC,CACDpP,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAI;MACb,MAAMvE,IAAI,GAAGuE,MAAM,CAACzE,IAAI,EAAEkP,UAAU;MACpC,IAAI,CAAChP,IAAI,EAAE;QACT,MAAM,IAAIiD,KAAK,CAAC,uBAAuB,CAAC;;MAG1C;MACA,IAAI,CAACnH,YAAY,CAACkD,IAAI,CAAC,IAAI,CAAC;MAE5B,OAAOgB,IAAI;IACb,CAAC,CAAC,EACF3I,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAChD,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC7D,CAAC,CAAC,CACH;EACL;EAEA;;;;;;EAMAiM,OAAOA,CAACZ,MAAc,EAAEa,QAAuB;IAC7C,IAAI,CAACrO,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACb,IAAI,CAAC,UAAU,CAAC;IAErB,OAAO,IAAI,CAAChF,MAAM,CACfkK,MAAM,CAAoB;MACzBC,QAAQ,EAAE1K,iBAAiB;MAC3BkK,SAAS,EAAE;QACT0J,MAAM;QACNa;;KAEH,CAAC,CACDtP,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAI;MACb,MAAMvE,IAAI,GAAGuE,MAAM,CAACzE,IAAI,EAAEoP,OAAO;MACjC,IAAI,CAAClP,IAAI,EAAE;QACT,MAAM,IAAIiD,KAAK,CAAC,oBAAoB,CAAC;;MAGvC;MACA,IAAI,CAACwL,WAAW,EAAE;MAElB;MACA,IAAI,CAAC5S,UAAU,CAACmD,IAAI,CAAC,IAAI,CAAC;MAE1B,OAAOgB,IAAI;IACb,CAAC,CAAC,EACF3I,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MAC7C,IAAI,CAACoP,WAAW,EAAE;MAClB,OAAOxX,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,oBAAoB,CAAC,CAAC;IAC1D,CAAC,CAAC,CACH;EACL;EAEA;;;;;;;EAOAmM,WAAWA,CACTd,MAAc,EACde,KAAe,EACfhP,KAAe;IAEf,IAAI,IAAI,CAACrE,WAAW,EAAE;MACpB;MACA,IAAIqT,KAAK,KAAKvR,SAAS,EAAE;QACvB,IAAI,CAAC9B,WAAW,CAACsT,cAAc,EAAE,CAAC1Q,OAAO,CAAE6O,KAAK,IAAI;UAClDA,KAAK,CAAC8B,OAAO,GAAGF,KAAK;QACvB,CAAC,CAAC;;MAGJ,IAAIhP,KAAK,KAAKvC,SAAS,EAAE;QACvB,IAAI,CAAC9B,WAAW,CAACwT,cAAc,EAAE,CAAC5Q,OAAO,CAAE6O,KAAK,IAAI;UAClDA,KAAK,CAAC8B,OAAO,GAAGlP,KAAK;QACvB,CAAC,CAAC;;;IAIN,OAAO,IAAI,CAACpF,MAAM,CACfkK,MAAM,CAAmC;MACxCC,QAAQ,EAAEzK,0BAA0B;MACpCiK,SAAS,EAAE;QACT0J,MAAM;QACNe,KAAK;QACLhP;;KAEH,CAAC,CACDR,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAI;MACb,MAAMkH,OAAO,GAAGlH,MAAM,CAACzE,IAAI,EAAE2P,eAAe;MAC5C,IAAI,CAAChE,OAAO,EAAE;QACZ,MAAM,IAAIxI,KAAK,CAAC,wBAAwB,CAAC;;MAE3C,OAAOwI,OAAO;IAChB,CAAC,CAAC,EACFpU,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAChD,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKAuL,sBAAsBA,CAACF,MAAc;IACnC,OAAO,IAAI,CAACrT,MAAM,CACfuE,SAAS,CAA6B;MACrCI,KAAK,EAAEhF,wBAAwB;MAC/BgK,SAAS,EAAE;QAAE0J;MAAM;KACpB,CAAC,CACDzO,IAAI,CACHzI,GAAG,CAAC,CAAC;MAAE0I;IAAI,CAAE,KAAI;MACf,IAAI,CAACA,IAAI,EAAE4P,UAAU,EAAE;QACrB,MAAM,IAAIzM,KAAK,CAAC,yBAAyB,CAAC;;MAE5C,OAAOnD,IAAI,CAAC4P,UAAU;IACxB,CAAC,CAAC,EACFpY,GAAG,CAAEqY,MAAM,IAAI;MACb,IAAI,CAAC5T,WAAW,CAACiD,IAAI,CAAC2Q,MAAM,CAAC;MAC7B,IAAI,CAACC,gBAAgB,CAACD,MAAM,CAAC;IAC/B,CAAC,CAAC,EACFtY,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC7D,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;;;;;;;EAOA6K,cAAcA,CACZQ,MAAc,EACduB,UAAkB,EAClBC,UAAkB;IAElB,OAAO,IAAI,CAAC7U,MAAM,CACfkK,MAAM,CAAkC;MACvCC,QAAQ,EAAE7K,yBAAyB;MACnCqK,SAAS,EAAE;QACT0J,MAAM;QACNuB,UAAU;QACVC;;KAEH,CAAC,CACDjQ,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAI;MACb,MAAMkH,OAAO,GAAGlH,MAAM,CAACzE,IAAI,EAAEgO,cAAc;MAC3C,IAAI,CAACrC,OAAO,EAAE;QACZ,MAAM,IAAIxI,KAAK,CAAC,4BAA4B,CAAC;;MAE/C,OAAOwI,OAAO;IAChB,CAAC,CAAC,EACFpU,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACrD,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEA;;;;;;;;;;EAUA8M,cAAcA,CACZrS,KAAA,GAAgB,EAAE,EAClB6K,MAAA,GAAiB,CAAC,EAClByH,MAAiB,EACjB/N,IAAe,EACfgO,SAAyB,EACzBC,OAAuB;IAEvB,OAAO,IAAI,CAACjV,MAAM,CACfmJ,UAAU,CAA0B;MACnCxE,KAAK,EAAEzF,kBAAkB;MACzByK,SAAS,EAAE;QACTlH,KAAK;QACL6K,MAAM;QACNyH,MAAM;QACN/N,IAAI;QACJgO,SAAS;QACTC;OACD;MACD7L,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBzI,GAAG,CAAEmN,MAAM,IAAI;MACb,MAAM4L,OAAO,GAAG5L,MAAM,CAACzE,IAAI,EAAEsQ,WAAW,IAAI,EAAE;MAC9C,IAAI,CAAClV,MAAM,CAACuD,KAAK,CAAC,aAAa0R,OAAO,CAACzR,MAAM,qBAAqB,CAAC;MACnE,OAAOyR,OAAO;IAChB,CAAC,CAAC,EACF9Y,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACxD,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKAoN,cAAcA,CAAC/B,MAAc;IAC3B,OAAO,IAAI,CAACrT,MAAM,CACfmJ,UAAU,CAAwB;MACjCxE,KAAK,EAAExF,kBAAkB;MACzBwK,SAAS,EAAE;QAAE0J;MAAM,CAAE;MACrBjK,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBzI,GAAG,CAAEmN,MAAM,IAAI;MACb,MAAM+L,OAAO,GAAG/L,MAAM,CAACzE,IAAI,EAAEyQ,WAAW;MACxC,IAAI,CAACD,OAAO,EAAE;QACZ,MAAM,IAAIrN,KAAK,CAAC,wBAAwB,CAAC;;MAE3C,IAAI,CAAC/H,MAAM,CAACuD,KAAK,CAAC,+BAA+B6P,MAAM,EAAE,CAAC;MAC1D,OAAOgC,OAAO;IAChB,CAAC,CAAC,EACFjZ,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACxD,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;;EAIAuN,YAAYA,CAAA;IACV,OAAO,IAAI,CAACvV,MAAM,CACfmJ,UAAU,CAAqB;MAC9BxE,KAAK,EAAEvF,gBAAgB;MACvBgK,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBzI,GAAG,CAAEmN,MAAM,IAAI;MACb,MAAMkM,KAAK,GAAGlM,MAAM,CAACzE,IAAI,EAAE4Q,SAAS;MACpC,IAAI,CAACD,KAAK,EAAE;QACV,MAAM,IAAIxN,KAAK,CAAC,sBAAsB,CAAC;;MAEzC,IAAI,CAAC/H,MAAM,CAACuD,KAAK,CAAC,uBAAuB,EAAEgS,KAAK,CAAC;MACjD,OAAOA,KAAK;IACd,CAAC,CAAC,EACFpZ,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACtD,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEA;;;;EAIQ2M,gBAAgBA,CAACD,MAAkB;IACzC,QAAQA,MAAM,CAAC1N,IAAI;MACjB,KAAK,eAAe;QAClB,IAAI,CAAC0O,kBAAkB,CAAChB,MAAM,CAAC;QAC/B;MACF,KAAK,QAAQ;QACX,IAAI,CAACiB,YAAY,CAACjB,MAAM,CAAC;QACzB;MACF,KAAK,UAAU;QACb,IAAI,CAACkB,aAAa,CAAClB,MAAM,CAAC;QAC1B;MACF,KAAK,QAAQ;QACX,IAAI,CAACmB,gBAAgB,CAACnB,MAAM,CAAC;QAC7B;MACF;QACE,IAAI,CAACzU,MAAM,CAACuD,KAAK,CAAC,0BAA0BkR,MAAM,CAAC1N,IAAI,EAAE,EAAE0N,MAAM,CAAC;;EAExE;EAEA;;;;EAIQgB,kBAAkBA,CAAChB,MAAkB;IAC3C,IAAI,CAAC,IAAI,CAACzT,cAAc,EAAE;MACxB,IAAI,CAAChB,MAAM,CAACmE,KAAK,CAAC,gDAAgD,CAAC;MACnE;;IAGF,IAAI;MACF,MAAMwO,SAAS,GAAGtP,IAAI,CAACC,KAAK,CAACmR,MAAM,CAAC7P,IAAI,CAAC;MACzC,IAAI,CAAC5D,cAAc,CAChB6U,eAAe,CAAC,IAAIC,eAAe,CAACnD,SAAS,CAAC,CAAC,CAC/ChN,KAAK,CAAExB,KAAK,IAAI;QACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,4BAA4B,EAAEA,KAAc,CAAC;MACjE,CAAC,CAAC;KACL,CAAC,OAAOA,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,6BAA6B,EAAEA,KAAc,CAAC;;EAEpE;EAEA;;;;EAIQuR,YAAYA,CAACjB,MAAkB;IACrC,IAAI,CAAC,IAAI,CAACzT,cAAc,EAAE;MACxB,IAAI,CAAChB,MAAM,CAACmE,KAAK,CAAC,yCAAyC,CAAC;MAC5D;;IAGF,IAAI;MACF,MAAMyP,MAAM,GAAGvQ,IAAI,CAACC,KAAK,CAACmR,MAAM,CAAC7P,IAAI,CAAC;MACtC,IAAI,CAAC5D,cAAc,CAChByS,oBAAoB,CAAC,IAAIC,qBAAqB,CAACE,MAAM,CAAC,CAAC,CACvDjO,KAAK,CAAExB,KAAK,IAAI;QACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,kCAAkC,EAAEA,KAAc,CAAC;MACvE,CAAC,CAAC;KACL,CAAC,OAAOA,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,sBAAsB,EAAEA,KAAc,CAAC;;EAE7D;EAEA;;;;EAIQwR,aAAaA,CAAClB,MAAkB;IACtC,IAAI,CAAC7O,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAAC2N,WAAW,EAAE;IAElB;IACA,MAAMwC,WAAW,GAAG,IAAI,CAACpV,UAAU,CAACuK,KAAK;IACzC,IAAI6K,WAAW,IAAIA,WAAW,CAACnS,EAAE,KAAK6Q,MAAM,CAACrB,MAAM,EAAE;MACnD,IAAI,CAACzS,UAAU,CAACmD,IAAI,CAAC;QACnB,GAAGiS,WAAW;QACdjB,MAAM,EAAEpY,UAAU,CAACsZ,KAAK;QACxBC,OAAO,EAAE,IAAIhO,IAAI,EAAE,CAACpF,WAAW;OAChC,CAAC;;EAEN;EAEA;;;;EAIQ+S,gBAAgBA,CAACnB,MAAkB;IACzC,IAAI,CAAC7O,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAAC2N,WAAW,EAAE;IAElB;IACA,MAAMwC,WAAW,GAAG,IAAI,CAACpV,UAAU,CAACuK,KAAK;IACzC,IAAI6K,WAAW,IAAIA,WAAW,CAACnS,EAAE,KAAK6Q,MAAM,CAACrB,MAAM,EAAE;MACnD,IAAI,CAACzS,UAAU,CAACmD,IAAI,CAAC;QACnB,GAAGiS,WAAW;QACdjB,MAAM,EAAEpY,UAAU,CAACwZ,QAAQ;QAC3BD,OAAO,EAAE,IAAIhO,IAAI,EAAE,CAACpF,WAAW;OAChC,CAAC;;EAEN;EAEA;;;EAGQ0Q,WAAWA,CAAA;IACjB,IAAI,IAAI,CAACzS,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACwR,SAAS,EAAE,CAAC5O,OAAO,CAAE6O,KAAK,IAAKA,KAAK,CAAC3M,IAAI,EAAE,CAAC;MAC7D,IAAI,CAAC9E,WAAW,GAAG,IAAI;MACvB,IAAI,CAACO,YAAY,CAACyC,IAAI,CAAC,IAAI,CAAC;;IAG9B,IAAI,IAAI,CAAC9C,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAACmV,KAAK,EAAE;MAC3B,IAAI,CAACnV,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACD,YAAY,GAAG,IAAI;IACxB,IAAI,CAACO,aAAa,CAACwC,IAAI,CAAC,IAAI,CAAC;EAC/B;EAEA;;;;;EAKQqO,iBAAiBA,CAACF,QAAkB;IAC1C,MAAMmE,WAAW,GAA2B;MAC1CjR,KAAK,EAAE,IAAI;MACXgP,KAAK,EACHlC,QAAQ,KAAKxV,QAAQ,CAACsO,KAAK,GACvB;QACEsL,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QACtBC,MAAM,EAAE;UAAED,KAAK,EAAE;QAAG;OACrB,GACD;KACP;IAED,OAAO,IAAIza,UAAU,CAAe2a,QAAQ,IAAI;MAC9CC,SAAS,CAACC,YAAY,CACnBC,YAAY,CAACP,WAAW,CAAC,CACzBQ,IAAI,CAAExE,MAAM,IAAI;QACfoE,QAAQ,CAAC1S,IAAI,CAACsO,MAAM,CAAC;QACrBoE,QAAQ,CAACK,QAAQ,EAAE;MACrB,CAAC,CAAC,CACDlR,KAAK,CAAExB,KAAK,IAAI;QACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACzDqS,QAAQ,CAACrS,KAAK,CAAC,IAAI4D,KAAK,CAAC,gCAAgC,CAAC,CAAC;MAC7D,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;EAEA;;;;EAIQ8K,cAAcA,CAAA;IACpB,OAAO5K,IAAI,CAACC,GAAG,EAAE,CAAC4O,QAAQ,EAAE,GAAGlF,IAAI,CAACmF,MAAM,EAAE,CAACD,QAAQ,CAAC,EAAE,CAAC,CAACrM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;EAC3E;EAEA;EACA;EACA;EACA;EACAuM,WAAWA,CACTC,YAAY,GAAG,KAAK,EACpBC,MAAe,EACfzN,IAAA,GAAe,CAAC,EAChBjH,KAAA,GAAgB,EAAE,EAClB2U,MAAA,GAAiB,UAAU,EAC3BC,SAAA,GAAoB,KAAK,EACzBC,QAAkB;IAElB,IAAI,CAACrX,MAAM,CAACkG,IAAI,CACd,gBAAgB,EAChB,2CAA2C+Q,YAAY,YACrDC,MAAM,IAAI,SACZ,UAAUzN,IAAI,WAAWjH,KAAK,YAAY2U,MAAM,eAAeC,SAAS,cAAcC,QAAQ,EAAE,CACjG;IAED,MAAMnP,GAAG,GAAGD,IAAI,CAACC,GAAG,EAAE;IACtB,MAAMoP,UAAU,GACd,CAACL,YAAY,IACb,IAAI,CAACvV,UAAU,CAAC8B,MAAM,GAAG,CAAC,IAC1B0E,GAAG,GAAG,IAAI,CAACxH,aAAa,IAAI,IAAI,CAACD,cAAc,IAC/C,CAACyW,MAAM,IACPzN,IAAI,KAAK,CAAC,IACVjH,KAAK,IAAI,IAAI,CAACd,UAAU,CAAC8B,MAAM;IAEjC;IACA,IAAI8T,UAAU,EAAE;MACd,IAAI,CAACtX,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,uBAAuB,IAAI,CAAC7B,UAAU,CAAC8B,MAAM,SAAS,CACvD;MACD,OAAO1H,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC4F,UAAU,CAAC,CAAC;;IAGjC,IAAI,CAAC1B,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,2DACE0T,YAAY,GAAG,cAAc,GAAG,aAClC,EAAE,CACH;IAED,OAAO,IAAI,CAAClX,MAAM,CACfmJ,UAAU,CAAM;MACfxE,KAAK,EAAEtH,kBAAkB;MACzBsM,SAAS,EAAE;QACTwN,MAAM;QACNzN,IAAI;QACJjH,KAAK;QACL2U,MAAM;QACNC,SAAS;QACTC,QAAQ,EAAEA,QAAQ,KAAKzU,SAAS,GAAGyU,QAAQ,GAAG;OAC/C;MACDlO,WAAW,EAAE8N,YAAY,GAAG,cAAc,GAAG;KAC9C,CAAC,CACD7N,YAAY,CAACzE,IAAI,CAChBzI,GAAG,CAAEmN,MAAM,IAAI;MACb,IAAI,CAACrJ,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,yBAAyB,EACzB8F,MAAM,CACP;MAED,IAAIA,MAAM,CAACmF,MAAM,EAAE;QACjB,IAAI,CAACxO,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,gCAAgC,EAChCkF,MAAM,CAACmF,MAAM,CACd;QACD,MAAM,IAAIzG,KAAK,CAACsB,MAAM,CAACmF,MAAM,CAACtS,GAAG,CAAEuS,CAAC,IAAKA,CAAC,CAACf,OAAO,CAAC,CAACgB,IAAI,CAAC,IAAI,CAAC,CAAC;;MAGjE,IAAI,CAACrF,MAAM,CAACzE,IAAI,EAAEoS,WAAW,EAAE;QAC7B,IAAI,CAAChX,MAAM,CAACyF,IAAI,CACd,gBAAgB,EAChB,oCAAoC,CACrC;QACD,OAAO,EAAE;;MAGX,MAAM8R,iBAAiB,GAAGlO,MAAM,CAACzE,IAAI,CAACoS,WAAW;MAEjD;MACA,IAAI,CAAChX,MAAM,CAACuD,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAE;QAC1D3B,UAAU,EAAE2V,iBAAiB,CAAC3V,UAAU;QACxCC,UAAU,EAAE0V,iBAAiB,CAAC1V,UAAU;QACxCC,WAAW,EAAEyV,iBAAiB,CAACzV,WAAW;QAC1CC,WAAW,EAAEwV,iBAAiB,CAACxV,WAAW;QAC1CC,eAAe,EAAEuV,iBAAiB,CAACvV;OACpC,CAAC;MAEF;MACA,MAAMwV,KAAK,GAAW,EAAE;MACxB,KAAK,MAAMC,IAAI,IAAIF,iBAAiB,CAACC,KAAK,EAAE;QAC1C,IAAI;UACF,IAAIC,IAAI,EAAE;YACRD,KAAK,CAACvG,IAAI,CAAC,IAAI,CAACrE,aAAa,CAAC6K,IAAI,CAAC,CAAC;;SAEvC,CAAC,OAAOtT,KAAK,EAAE;UACd,IAAI,CAACnE,MAAM,CAACyF,IAAI,CACd,gBAAgB,EAChB,mCAAmC,EACnCtB,KAAK,CACN;;;MAIL,IAAI,CAACnE,MAAM,CAACkG,IAAI,CACd,gBAAgB,EAChB,YAAYsR,KAAK,CAAChU,MAAM,4BAA4B+T,iBAAiB,CAACzV,WAAW,OAAOyV,iBAAiB,CAAC1V,UAAU,GAAG,CACxH;MAED;MACA,IAAI,CAACqV,MAAM,IAAIzN,IAAI,KAAK,CAAC,IAAI,CAAC4N,QAAQ,EAAE;QACtC,IAAI,CAAC3V,UAAU,GAAG,CAAC,GAAG8V,KAAK,CAAC;QAC5B,IAAI,CAAC9W,aAAa,GAAGuH,IAAI,CAACC,GAAG,EAAE;QAC/B,IAAI,CAAClI,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,2BAA2BiU,KAAK,CAAChU,MAAM,QAAQ,CAChD;;MAGH;MACA,IAAI,CAAC7B,qBAAqB,GAAG;QAC3BC,UAAU,EAAE2V,iBAAiB,CAAC3V,UAAU;QACxCC,UAAU,EAAE0V,iBAAiB,CAAC1V,UAAU;QACxCC,WAAW,EAAEyV,iBAAiB,CAACzV,WAAW;QAC1CC,WAAW,EAAEwV,iBAAiB,CAACxV,WAAW;QAC1CC,eAAe,EAAEuV,iBAAiB,CAACvV;OACpC;MAED,OAAOwV,KAAK;IACd,CAAC,CAAC,EACFrb,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MAEnE,IAAIA,KAAK,CAACgL,aAAa,EAAE;QACvB,IAAI,CAACnP,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjBA,KAAK,CAACgL,aAAa,CACpB;;MAGH,IAAIhL,KAAK,CAACiL,YAAY,EAAE;QACtB,IAAI,CAACpP,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,gBAAgB,EAChBA,KAAK,CAACiL,YAAY,CACnB;;MAGH;MACA,IACE,IAAI,CAAC1N,UAAU,CAAC8B,MAAM,GAAG,CAAC,IAC1BiG,IAAI,KAAK,CAAC,IACV,CAACyN,MAAM,IACP,CAACG,QAAQ,EACT;QACA,IAAI,CAACrX,MAAM,CAACyF,IAAI,CACd,gBAAgB,EAChB,aAAa,IAAI,CAAC/D,UAAU,CAAC8B,MAAM,kCAAkC,CACtE;QACD,OAAO1H,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC4F,UAAU,CAAC,CAAC;;MAGjC,OAAO3F,UAAU,CACf,MACE,IAAIgM,KAAK,CACP,0BAA0B5D,KAAK,CAACuJ,OAAO,IAAI,eAAe,EAAE,CAC7D,CACJ;IACH,CAAC,CAAC,CACH;EACL;EACAgK,UAAUA,CAAC5K,MAAc;IACvB,OAAO,IAAI,CAAC/M,MAAM,CACfmJ,UAAU,CAAqB;MAC9BxE,KAAK,EAAEvH,cAAc;MACrBuM,SAAS,EAAE;QAAE9F,EAAE,EAAEkJ;MAAM,CAAE;MACzB3D,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBzI,GAAG,CAAEmN,MAAM,IAAK,IAAI,CAACuD,aAAa,CAACvD,MAAM,CAACzE,IAAI,EAAE8S,UAAU,CAAC,CAAC,EAC5Dvb,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAEA,KAAK,CAAC;MAClE,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAC5D,CAAC,CAAC,CACH;EACL;EACA4P,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAC5X,MAAM,CACfmJ,UAAU,CAAyB;MAClCxE,KAAK,EAAE7G,sBAAsB;MAC7BsL,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBzI,GAAG,CAAEmN,MAAM,IAAK,IAAI,CAACuD,aAAa,CAACvD,MAAM,CAACzE,IAAI,EAAE+S,cAAc,CAAC,CAAC,EAChExb,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,8BAA8B,EAC9BA,KAAK,CACN;MACD,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EACA6P,aAAaA,CAAC9K,MAAc;IAC1B,OAAO,IAAI,CAAC/M,MAAM,CACfkK,MAAM,CAAwB;MAC7BC,QAAQ,EAAE1M,wBAAwB;MAClCkM,SAAS,EAAE;QAAEoD;MAAM;KACpB,CAAC,CACDnI,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAEgT,aAAa,EAC7B,MAAM,IAAI7P,KAAK,CAAC,2BAA2B,CAAC;MAC9C,OAAO,IAAI,CAAC6E,aAAa,CAACvD,MAAM,CAACzE,IAAI,CAACgT,aAAa,CAAC;IACtD,CAAC,CAAC,EACFzb,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,4BAA4B,EAC5BA,KAAK,CACN;MACD,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACjE,CAAC,CAAC,CACH;EACL;EACA8P,cAAcA,CAAC/K,MAAc;IAC3B,OAAO,IAAI,CAAC/M,MAAM,CACfkK,MAAM,CAAyB;MAC9BC,QAAQ,EAAEzM,yBAAyB;MACnCiM,SAAS,EAAE;QAAEoD;MAAM;KACpB,CAAC,CACDnI,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAEiT,cAAc,EAC9B,MAAM,IAAI9P,KAAK,CAAC,4BAA4B,CAAC;MAC/C,OAAO,IAAI,CAAC6E,aAAa,CAACvD,MAAM,CAACzE,IAAI,CAACiT,cAAc,CAAC;IACvD,CAAC,CAAC,EACF1b,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,6BAA6B,EAC7BA,KAAK,CACN;MACD,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEA;EACA;EACA;EAEA;;;EAGA+P,WAAWA,CACT7S,IAAY,EACZ+I,cAAwB,EACxB+J,KAAY,EACZC,WAAoB;IAEpB,IAAI,CAAChY,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,mBAAmB0B,IAAI,SAAS+I,cAAc,CAACxK,MAAM,eAAe,CACrE;IAED,IAAI,CAACyB,IAAI,IAAI,CAAC+I,cAAc,IAAIA,cAAc,CAACxK,MAAM,KAAK,CAAC,EAAE;MAC3D,OAAOzH,UAAU,CACf,MAAM,IAAIgM,KAAK,CAAC,sCAAsC,CAAC,CACxD;;IAGH,OAAO,IAAI,CAAChI,MAAM,CACfkK,MAAM,CAAC;MACNC,QAAQ,EAAEjM,qBAAqB;MAC/ByL,SAAS,EAAE;QAAEzE,IAAI;QAAE+I,cAAc;QAAE+J,KAAK;QAAEC;MAAW;KACtD,CAAC,CACDrT,IAAI,CACHzI,GAAG,CAAEmN,MAAW,IAAI;MAClB,MAAM4O,KAAK,GAAG5O,MAAM,CAACzE,IAAI,EAAEkT,WAAW;MACtC,IAAI,CAACG,KAAK,EAAE;QACV,MAAM,IAAIlQ,KAAK,CAAC,gCAAgC,CAAC;;MAEnD,IAAI,CAAC/H,MAAM,CAACkG,IAAI,CACd,gBAAgB,EAChB,+BAA+B+R,KAAK,CAACrU,EAAE,EAAE,CAC1C;MACD,OAAOqU,KAAK;IACd,CAAC,CAAC,EACF9b,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MACnE,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtE,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAmQ,WAAWA,CAACC,OAAe,EAAEC,KAAU;IACrC,IAAI,CAACpY,MAAM,CAACuD,KAAK,CAAC,gBAAgB,EAAE,mBAAmB4U,OAAO,EAAE,CAAC;IAEjE,IAAI,CAACA,OAAO,EAAE;MACZ,OAAOpc,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,OAAO,IAAI,CAAChI,MAAM,CACfkK,MAAM,CAAC;MACNC,QAAQ,EAAEhM,qBAAqB;MAC/BwL,SAAS,EAAE;QAAE9F,EAAE,EAAEuU,OAAO;QAAEC;MAAK;KAChC,CAAC,CACDzT,IAAI,CACHzI,GAAG,CAAEmN,MAAW,IAAI;MAClB,MAAM4O,KAAK,GAAG5O,MAAM,CAACzE,IAAI,EAAEsT,WAAW;MACtC,IAAI,CAACD,KAAK,EAAE;QACV,MAAM,IAAIlQ,KAAK,CAAC,mCAAmC,CAAC;;MAEtD,IAAI,CAAC/H,MAAM,CAACkG,IAAI,CACd,gBAAgB,EAChB,+BAA+B+R,KAAK,CAACrU,EAAE,EAAE,CAC1C;MACD,OAAOqU,KAAK;IACd,CAAC,CAAC,EACF9b,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MACnE,OAAOpI,UAAU,CACf,MAAM,IAAIgM,KAAK,CAAC,mCAAmC,CAAC,CACrD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAsQ,WAAWA,CACTF,OAAe;IAEf,IAAI,CAACnY,MAAM,CAACuD,KAAK,CAAC,gBAAgB,EAAE,mBAAmB4U,OAAO,EAAE,CAAC;IAEjE,IAAI,CAACA,OAAO,EAAE;MACZ,OAAOpc,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,OAAO,IAAI,CAAChI,MAAM,CACfkK,MAAM,CAAC;MACNC,QAAQ,EAAE/L,qBAAqB;MAC/BuL,SAAS,EAAE;QAAE9F,EAAE,EAAEuU;MAAO;KACzB,CAAC,CACDxT,IAAI,CACHzI,GAAG,CAAEmN,MAAW,IAAI;MAClB,MAAMiH,QAAQ,GAAGjH,MAAM,CAACzE,IAAI,EAAEyT,WAAW;MACzC,IAAI,CAAC/H,QAAQ,EAAE;QACb,MAAM,IAAIvI,KAAK,CAAC,mCAAmC,CAAC;;MAEtD,IAAI,CAAC/H,MAAM,CAACkG,IAAI,CACd,gBAAgB,EAChB,+BAA+BiS,OAAO,EAAE,CACzC;MACD,OAAO7H,QAAQ;IACjB,CAAC,CAAC,EACFnU,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MACnE,OAAOpI,UAAU,CACf,MAAM,IAAIgM,KAAK,CAAC,mCAAmC,CAAC,CACrD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAuQ,UAAUA,CACRH,OAAe;IAEf,IAAI,CAACnY,MAAM,CAACuD,KAAK,CAAC,gBAAgB,EAAE,kBAAkB4U,OAAO,EAAE,CAAC;IAEhE,IAAI,CAACA,OAAO,EAAE;MACZ,OAAOpc,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,OAAO,IAAI,CAAChI,MAAM,CACfkK,MAAM,CAAC;MACNC,QAAQ,EAAE9L,oBAAoB;MAC9BsL,SAAS,EAAE;QAAEyO;MAAO;KACrB,CAAC,CACDxT,IAAI,CACHzI,GAAG,CAAEmN,MAAW,IAAI;MAClB,MAAMiH,QAAQ,GAAGjH,MAAM,CAACzE,IAAI,EAAE0T,UAAU;MACxC,IAAI,CAAChI,QAAQ,EAAE;QACb,MAAM,IAAIvI,KAAK,CAAC,8BAA8B,CAAC;;MAEjD,IAAI,CAAC/H,MAAM,CAACkG,IAAI,CACd,gBAAgB,EAChB,4BAA4BiS,OAAO,EAAE,CACtC;MACD,OAAO7H,QAAQ;IACjB,CAAC,CAAC,EACFnU,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAEA,KAAK,CAAC;MAClE,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAwQ,QAAQA,CAACJ,OAAe;IACtB,IAAI,CAACnY,MAAM,CAACuD,KAAK,CAAC,gBAAgB,EAAE,kBAAkB4U,OAAO,EAAE,CAAC;IAEhE,IAAI,CAACA,OAAO,EAAE;MACZ,OAAOpc,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,OAAO,IAAI,CAAChI,MAAM,CACf2E,KAAK,CAAC;MACLA,KAAK,EAAErG,eAAe;MACtBqL,SAAS,EAAE;QAAE9F,EAAE,EAAEuU;MAAO,CAAE;MAC1BhP,WAAW,EAAE;KACd,CAAC,CACDxE,IAAI,CACHzI,GAAG,CAAEmN,MAAW,IAAI;MAClB,MAAM4O,KAAK,GAAG5O,MAAM,CAACzE,IAAI,EAAE2T,QAAQ;MACnC,IAAI,CAACN,KAAK,EAAE;QACV,MAAM,IAAIlQ,KAAK,CAAC,mBAAmB,CAAC;;MAEtC,IAAI,CAAC/H,MAAM,CAACkG,IAAI,CACd,gBAAgB,EAChB,iCAAiCiS,OAAO,EAAE,CAC3C;MACD,OAAOF,KAAK;IACd,CAAC,CAAC,EACF9b,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAEA,KAAK,CAAC;MAClE,OAAOpI,UAAU,CACf,MAAM,IAAIgM,KAAK,CAAC,oCAAoC,CAAC,CACtD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAyQ,aAAaA,CAAC1L,MAAc;IAC1B,IAAI,CAAC9M,MAAM,CAACuD,KAAK,CAAC,gBAAgB,EAAE,4BAA4BuJ,MAAM,EAAE,CAAC;IAEzE,IAAI,CAACA,MAAM,EAAE;MACX,OAAO/Q,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,4BAA4B,CAAC,CAAC;;IAGlE,OAAO,IAAI,CAAChI,MAAM,CACf2E,KAAK,CAAC;MACLA,KAAK,EAAEpG,qBAAqB;MAC5BoL,SAAS,EAAE;QAAEoD;MAAM,CAAE;MACrB3D,WAAW,EAAE;KACd,CAAC,CACDxE,IAAI,CACHzI,GAAG,CAAEmN,MAAW,IAAI;MAClB,MAAM0H,MAAM,GAAG1H,MAAM,CAACzE,IAAI,EAAE4T,aAAa,IAAI,EAAE;MAC/C,IAAI,CAACxY,MAAM,CAACkG,IAAI,CACd,gBAAgB,EAChB,aAAa6K,MAAM,CAACvN,MAAM,qBAAqBsJ,MAAM,EAAE,CACxD;MACD,OAAOiE,MAAM;IACf,CAAC,CAAC,EACF5U,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,4BAA4B,EAC5BA,KAAK,CACN;MACD,OAAOpI,UAAU,CACf,MAAM,IAAIgM,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;EACL;EAEA;EACA;EACA;EACA0Q,sBAAsBA,CAAC5Q,cAAsB;IAC3C;IACA,IAAI,CAAC,IAAI,CAAC6Q,YAAY,EAAE,EAAE;MACxB,IAAI,CAAC1Y,MAAM,CAACyF,IAAI,CACd,sEAAsE,CACvE;MACD,OAAO3J,EAAE,CAAC,IAA0B,CAAC;;IAGvC,IAAI,CAACkE,MAAM,CAACuD,KAAK,CACf,yEAAyEsE,cAAc,EAAE,CAC1F;IAED,MAAM8Q,IAAI,GAAG,IAAI,CAAC5Y,MAAM,CACrBuE,SAAS,CAA2B;MACnCI,KAAK,EAAEzH,yBAAyB;MAChCyM,SAAS,EAAE;QAAE7B;MAAc;KAC5B,CAAC,CACDlD,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAI;MACb,MAAMO,GAAG,GAAGP,MAAM,CAACzE,IAAI,EAAEgU,WAAW;MACpC,IAAI,CAAChP,GAAG,EAAE;QACR,IAAI,CAAC5J,MAAM,CAACyF,IAAI,CAAC,6BAA6B,CAAC;QAC/C,MAAM,IAAIsC,KAAK,CAAC,6BAA6B,CAAC;;MAGhD;MACA,IAAI,CAAC6B,GAAG,CAAChG,EAAE,IAAI,CAACgG,GAAG,CAACsE,GAAG,EAAE;QACvB,IAAI,CAAClO,MAAM,CAACyF,IAAI,CAAC,8BAA8B,EAAEmE,GAAG,CAAC;QACrD;QACAA,GAAG,CAAChG,EAAE,GAAG,QAAQqE,IAAI,CAACC,GAAG,EAAE,EAAE;;MAG/B,IAAI;QACF;QACA,MAAMqD,iBAAiB,GAAG,IAAI,CAAC1B,gBAAgB,CAACD,GAAG,CAAC;QAEpD;QACA,IACE2B,iBAAiB,CAACxE,IAAI,KAAKvK,WAAW,CAACuO,KAAK,IAC3CQ,iBAAiB,CAACsN,WAAW,IAC5BtN,iBAAiB,CAACsN,WAAW,CAACC,IAAI,CAC/BC,GAAG,IAAKA,GAAG,CAAChS,IAAI,KAAK,OAAO,CAC7B,EACJ;UACA,IAAI,CAAC/G,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,qCAAqC,EACrCgI,iBAAiB,CAClB;UAED;UACA,IAAI,CAACyN,gCAAgC,CACnCnR,cAAc,EACd0D,iBAAiB,CAClB;;QAGH,OAAOA,iBAAiB;OACzB,CAAC,OAAO/D,GAAG,EAAE;QACZ,IAAI,CAACxH,MAAM,CAACmE,KAAK,CAAC,4BAA4B,EAAEqD,GAAG,CAAC;QAEpD;QACA,MAAMiE,cAAc,GAAY;UAC9B7H,EAAE,EAAEgG,GAAG,CAAChG,EAAE,IAAIgG,GAAG,CAACsE,GAAG,IAAI,QAAQjG,IAAI,CAACC,GAAG,EAAE,EAAE;UAC7CkC,OAAO,EAAER,GAAG,CAACQ,OAAO,IAAI,EAAE;UAC1BrD,IAAI,EAAE6C,GAAG,CAAC7C,IAAI,IAAIvK,WAAW,CAAC+N,IAAI;UAClCvC,SAAS,EAAE,IAAI,CAAC2E,QAAQ,CAAC/C,GAAG,CAAC5B,SAAS,CAAC;UACvC0D,MAAM,EAAE,KAAK;UACbC,MAAM,EAAE/B,GAAG,CAAC+B,MAAM,GACd,IAAI,CAACiB,aAAa,CAAChD,GAAG,CAAC+B,MAAM,CAAC,GAC9B;YACE/H,EAAE,EAAE,IAAI,CAACgI,gBAAgB,EAAE;YAC3BC,QAAQ,EAAE;;SAEjB;QAED,OAAOJ,cAAc;;IAEzB,CAAC,CAAC,EACFtP,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,6BAA6B,EAC7BA,KAAK,CACN;MACD;MACA,OAAOlI,KAAK;IACd,CAAC,CAAC;IACF;IACAI,MAAM,CAAEqR,OAAO,IAAK,CAAC,CAACA,OAAO,CAAC;IAC9B;IACA1R,KAAK,CAAC,CAAC,CAAC,CACT;IAEH,MAAMid,GAAG,GAAGN,IAAI,CAACrU,SAAS,CAAC;MACzBR,IAAI,EAAG4J,OAAO,IAAI;QAChB;QACA,IAAI,CAAC1N,MAAM,CAACuD,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEmK,OAAO,CAAC;QAErE;QACA,IAAI,CAACsL,gCAAgC,CAACnR,cAAc,EAAE6F,OAAO,CAAC;MAChE,CAAC;MACDvJ,KAAK,EAAGqD,GAAG,IAAI;QACb,IAAI,CAACxH,MAAM,CAACmE,KAAK,CAAC,gCAAgC,EAAEqD,GAAG,CAAC;MAC1D;KACD,CAAC;IAEF,IAAI,CAAChH,aAAa,CAACyQ,IAAI,CAACgI,GAAG,CAAC;IAC5B,OAAON,IAAI;EACb;EAEA;;;;;EAKQK,gCAAgCA,CACtCnR,cAAsB,EACtB6F,OAAgB;IAEhB;IACA,IAAI,CAACN,eAAe,CAACvF,cAAc,CAAC,CAACvD,SAAS,CAAC;MAC7CR,IAAI,EAAG2J,YAAY,IAAI;QACrB,IAAI,CAACzN,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,gBAAgBsE,cAAc,+BAC5B6F,OAAO,CAAC9J,EACV,SAAS6J,YAAY,EAAE9D,QAAQ,EAAEnG,MAAM,IAAI,CAAC,WAAW,CACxD;QAED;QACA,IAAI,CAACtD,kBAAkB,CAAC4D,IAAI,CAAC+D,cAAc,CAAC;MAC9C,CAAC;MACD1D,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iCAAiC0D,cAAc,GAAG,EAClD1D,KAAK,CACN;MACH;KACD,CAAC;EACJ;EACAM,qBAAqBA,CAAA;IACnB;IACA,IAAI,CAAC,IAAI,CAACiU,YAAY,EAAE,EAAE;MACxB,IAAI,CAAC1Y,MAAM,CAACyF,IAAI,CACd,+EAA+E,CAChF;MACD,OAAO1J,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,0BAA0B,CAAC,CAAC;;IAGhE,IAAI,CAAC/H,MAAM,CAACuD,KAAK,CAAC,iDAAiD,CAAC;IAEpE,MAAMoV,IAAI,GAAG,IAAI,CAAC5Y,MAAM,CACrBuE,SAAS,CAA8B;MACtCI,KAAK,EAAExH;KACR,CAAC,CACDyH,IAAI,CACHvI,GAAG,CAAEiN,MAAM,IACT,IAAI,CAACrJ,MAAM,CAACuD,KAAK,CACf,uDAAuD,EACvD8F,MAAM,CACP,CACF,EACDnN,GAAG,CAAEmN,MAAM,IAAI;MACb,MAAMoO,IAAI,GAAGpO,MAAM,CAACzE,IAAI,EAAEsU,iBAAiB;MAC3C,IAAI,CAACzB,IAAI,EAAE;QACT,IAAI,CAACzX,MAAM,CAACmE,KAAK,CAAC,4BAA4B,CAAC;QAC/C,MAAM,IAAI4D,KAAK,CAAC,4BAA4B,CAAC;;MAE/C,OAAO,IAAI,CAAC6E,aAAa,CAAC6K,IAAI,CAAC;IACjC,CAAC,CAAC,EACFtb,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,4BAA4B,EAAEA,KAAc,CAAC;MAC/D,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,EACF/L,KAAK,CAAC,CAAC,CAAC,CAAC;KACV;;IAEH,MAAMid,GAAG,GAAGN,IAAI,CAACrU,SAAS,EAAE;IAC5B,IAAI,CAAC9D,aAAa,CAACyQ,IAAI,CAACgI,GAAG,CAAC;IAC5B,OAAON,IAAI;EACb;EACAQ,8BAA8BA,CAC5BtR,cAAsB;IAEtB,MAAM8Q,IAAI,GAAG,IAAI,CAAC5Y,MAAM,CACrBuE,SAAS,CAAwC;MAChDI,KAAK,EAAErH,iCAAiC;MACxCqM,SAAS,EAAE;QAAE7B;MAAc;KAC5B,CAAC,CACDlD,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAI;MACb,MAAM6D,IAAI,GAAG7D,MAAM,CAACzE,IAAI,EAAEwU,mBAAmB;MAC7C,IAAI,CAAClM,IAAI,EAAE,MAAM,IAAInF,KAAK,CAAC,kCAAkC,CAAC;MAE9D,MAAMuF,sBAAsB,GAAiB;QAC3C,GAAGJ,IAAI;QACPK,YAAY,EACVL,IAAI,CAACK,YAAY,EAAErR,GAAG,CAAE+R,CAAC,IAAK,IAAI,CAACrB,aAAa,CAACqB,CAAC,CAAC,CAAC,IAAI,EAAE;QAC5DoL,WAAW,EAAEnM,IAAI,CAACmM,WAAW,GACzB;UACE,GAAGnM,IAAI,CAACmM,WAAW;UACnB1N,MAAM,EAAE,IAAI,CAACiB,aAAa,CAACM,IAAI,CAACmM,WAAW,CAAC1N,MAAM,CAAC;UACnD3D,SAAS,EAAE,IAAI,CAAC2E,QAAQ,CAACO,IAAI,CAACmM,WAAW,CAACrR,SAAS,CAAC;UACpD+D,MAAM,EAAEmB,IAAI,CAACmM,WAAW,CAACtN,MAAM,GAC3B,IAAI,CAACY,QAAQ,CAACO,IAAI,CAACmM,WAAW,CAACtN,MAAM,CAAC,GACtCnJ,SAAS;UACb;UACAgB,EAAE,EAAEsJ,IAAI,CAACmM,WAAW,CAACzV,EAAE;UACvBwG,OAAO,EAAE8C,IAAI,CAACmM,WAAW,CAACjP,OAAO;UACjCrD,IAAI,EAAEmG,IAAI,CAACmM,WAAW,CAACtS,IAAI;UAC3B2E,MAAM,EAAEwB,IAAI,CAACmM,WAAW,CAAC3N;UACzB;SACD,GACD,IAAI,CAAE;OACX;;MAED,OAAO4B,sBAAsC,CAAC,CAAC;IACjD,CAAC,CAAC,EACFnR,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,kCAAkC,EAClCA,KAAK,CACN;MACD,OAAOpI,UAAU,CACf,MAAM,IAAIgM,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;IAEH,MAAMkR,GAAG,GAAGN,IAAI,CAACrU,SAAS,EAAE;IAC5B,IAAI,CAAC9D,aAAa,CAACyQ,IAAI,CAACgI,GAAG,CAAC;IAC5B,OAAON,IAAI;EACb;EACAW,0BAA0BA,CACxBzR,cAAsB;IAEtB,MAAM8Q,IAAI,GAAG,IAAI,CAAC5Y,MAAM,CACrBuE,SAAS,CAAwB;MAChCI,KAAK,EAAE9G,6BAA6B;MACpC8L,SAAS,EAAE;QAAE7B;MAAc;KAC5B,CAAC,CACDlD,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAKA,MAAM,CAACzE,IAAI,EAAE2U,eAAe,CAAC,EAC7Cld,MAAM,CAACmd,OAAO,CAAC,EACfrd,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,sCAAsC,EACtCA,KAAK,CACN;MACD,OAAOpI,UAAU,CACf,MAAM,IAAIgM,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;IAEH,MAAMkR,GAAG,GAAGN,IAAI,CAACrU,SAAS,EAAE;IAC5B,IAAI,CAAC9D,aAAa,CAACyQ,IAAI,CAACgI,GAAG,CAAC;IAC5B,OAAON,IAAI;EACb;EACQD,YAAYA,CAAA;IAClB,MAAMhO,KAAK,GAAGvH,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACsH,KAAK,EAAE;MACV,IAAI,CAAC1K,MAAM,CAACyF,IAAI,CAAC,oBAAoB,CAAC;MACtC,OAAO,KAAK;;IAGd,IAAI;MACF;MACA,MAAMgU,KAAK,GAAG/O,KAAK,CAACgP,KAAK,CAAC,GAAG,CAAC;MAC9B,IAAID,KAAK,CAACjW,MAAM,KAAK,CAAC,EAAE;QACtB,IAAI,CAACxD,MAAM,CAACyF,IAAI,CAAC,0BAA0B,CAAC;QAC5C,OAAO,KAAK;;MAGd;MACA,MAAMkU,OAAO,GAAGtW,IAAI,CAACC,KAAK,CAACsW,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MAE1C;MACA,IAAI,CAACE,OAAO,CAACE,GAAG,EAAE;QAChB,IAAI,CAAC7Z,MAAM,CAACyF,IAAI,CAAC,8BAA8B,CAAC;QAChD,OAAO,KAAK;;MAGd,MAAMqU,cAAc,GAAG,IAAI7R,IAAI,CAAC0R,OAAO,CAACE,GAAG,GAAG,IAAI,CAAC;MACnD,MAAM3R,GAAG,GAAG,IAAID,IAAI,EAAE;MAEtB,IAAI6R,cAAc,GAAG5R,GAAG,EAAE;QACxB,IAAI,CAAClI,MAAM,CAACyF,IAAI,CAAC,cAAc,EAAE;UAC/BsU,UAAU,EAAED,cAAc,CAACjX,WAAW,EAAE;UACxCqF,GAAG,EAAEA,GAAG,CAACrF,WAAW;SACrB,CAAC;QACF,OAAO,KAAK;;MAGd,OAAO,IAAI;KACZ,CAAC,OAAOsB,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,0CAA0C,EAC1CA,KAAc,CACf;MACD,OAAO,KAAK;;EAEhB;EAEAI,4BAA4BA,CAAA;IAC1B;IACA,IAAI,CAAC,IAAI,CAACmU,YAAY,EAAE,EAAE;MACxB,IAAI,CAAC1Y,MAAM,CAACyF,IAAI,CACd,2EAA2E,CAC5E;MACD,OAAO3J,EAAE,CAAC,EAAE,CAAC;;IAGf,IAAI,CAACkE,MAAM,CAACuD,KAAK,CAAC,kDAAkD,CAAC;IAErE,MAAMoV,IAAI,GAAG,IAAI,CAAC5Y,MAAM,CACrBuE,SAAS,CAAyB;MACjCI,KAAK,EAAE9F;KACR,CAAC,CACD+F,IAAI,CACHvI,GAAG,CAAEiN,MAAM,IACT,IAAI,CAACrJ,MAAM,CAACuD,KAAK,CACf,wDAAwD,EACxD8F,MAAM,CACP,CACF,EACDnN,GAAG,CAAEmN,MAAM,IAAI;MACb,MAAMwH,eAAe,GAAGxH,MAAM,CAACzE,IAAI,EAAEoV,iBAAiB,IAAI,EAAE;MAC5D,IAAI,CAACha,MAAM,CAACuD,KAAK,CACf,oCAAoC,EACpCsN,eAAe,CAChB;MACD,IAAI,CAACY,wBAAwB,CAACZ,eAAe,EAAE,IAAI,CAAC;MACpD,OAAOA,eAAe;IACxB,CAAC,CAAC,EACF1U,UAAU,CAAEqL,GAAG,IAAI;MACjB,IAAI,CAACxH,MAAM,CAACmE,KAAK,CACf,wCAAwC,EACxCqD,GAAY,CACb;MACD;MACA,OAAO1L,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC;IACF;IACAE,KAAK,CAAC,CAAC,CAAC,CAAC;KACV;;IAEH,MAAMid,GAAG,GAAGN,IAAI,CAACrU,SAAS,EAAE;IAC5B,IAAI,CAAC9D,aAAa,CAACyQ,IAAI,CAACgI,GAAG,CAAC;IAC5B,OAAON,IAAI;EACb;EACAtU,2BAA2BA,CAAA;IACzB;IACA,MAAMqG,KAAK,GAAGvH,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACsH,KAAK,EAAE;MACV,IAAI,CAAC1K,MAAM,CAACyF,IAAI,CACd,6DAA6D,CAC9D;MACD;MACA,OAAOxJ,KAAK;;IAGd,MAAMge,OAAO,GAAG,IAAI,CAACla,MAAM,CAACuE,SAAS,CAA4B;MAC/DI,KAAK,EAAE7H;KACR,CAAC;IAEF,MAAMqd,UAAU,GAAGD,OAAO,CAACtV,IAAI,CAC7BzI,GAAG,CAAEmN,MAAM,IAAI;MACb,MAAM1F,YAAY,GAAG0F,MAAM,CAACzE,IAAI,EAAEuV,oBAAoB;MACtD,IAAI,CAACxW,YAAY,EAAE;QACjB,MAAM,IAAIoE,KAAK,CAAC,kCAAkC,CAAC;;MAGrD,MAAMqS,UAAU,GAAG,IAAI,CAACC,qBAAqB,CAAC1W,YAAY,CAAC;MAE3D;MACA,IAAI,IAAI,CAACvD,iBAAiB,CAAC0O,GAAG,CAACsL,UAAU,CAACxW,EAAE,CAAC,EAAE;QAC7C,IAAI,CAAC5D,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,gBAAgB6W,UAAU,CAACxW,EAAE,oCAAoC,CAClE;QACD;QACA,MAAM,IAAImE,KAAK,CAAC,sCAAsC,CAAC;;MAGzD;MACA,IAAI,CAAC3B,qBAAqB,EAAE;MAE5B;MACA,IAAI,CAACkU,uBAAuB,CAACF,UAAU,CAAC;MAExC,IAAI,CAACpa,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,yCAAyC,EACzC6W,UAAU,CACX;MAED,OAAOA,UAAU;IACnB,CAAC,CAAC;IACF;IACAje,UAAU,CAAEqL,GAAG,IAAI;MACjB;MACA,IACEA,GAAG,YAAYO,KAAK,IACpBP,GAAG,CAACkG,OAAO,KAAK,sCAAsC,EACtD;QACA,OAAOzR,KAAK;;MAGd,IAAI,CAAC+D,MAAM,CAACmE,KAAK,CAAC,sCAAsC,EAAEqD,GAAY,CAAC;MACvE;MACA,OAAOvL,KAAK;IACd,CAAC,CAAC,CACH;IAED,MAAMgd,GAAG,GAAGiB,UAAU,CAAC5V,SAAS,CAAC;MAC/BR,IAAI,EAAGH,YAAY,IAAI;QACrB,IAAI,CAAC3D,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,wCAAwC,EACxCI,YAAY,CACb;MACH,CAAC;MACDQ,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,oCAAoC,EACpCA,KAAK,CACN;MACH;KACD,CAAC;IAEF,IAAI,CAAC3D,aAAa,CAACyQ,IAAI,CAACgI,GAAG,CAAC;IAC5B,OAAOiB,UAAU;EACnB;EACA;EACA;EACA;EAEQlX,oBAAoBA,CAAA;IAC1B,IAAI,CAACuX,eAAe,GAAGC,WAAW,CAAC,MAAK;MACtC,IAAI,CAACC,2BAA2B,EAAE;IACpC,CAAC,EAAE,OAAO,CAAC;EACb;EACQA,2BAA2BA,CAAA;IACjC,MAAMvS,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAMyS,aAAa,GAAG,IAAIzS,IAAI,CAACC,GAAG,CAACyS,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAExE,IAAIC,YAAY,GAAG,CAAC;IAEpB,IAAI,CAACxa,iBAAiB,CAACsD,OAAO,CAAC,CAACC,YAAY,EAAEC,EAAE,KAAI;MAClD,MAAMiX,gBAAgB,GAAG,IAAI5S,IAAI,CAACtE,YAAY,CAACqE,SAAS,CAAC;MACzD,IAAI6S,gBAAgB,GAAGH,aAAa,EAAE;QACpC,IAAI,CAACta,iBAAiB,CAACiQ,MAAM,CAACzM,EAAE,CAAC;QACjCgX,YAAY,EAAE;;IAElB,CAAC,CAAC;IAEF,IAAIA,YAAY,GAAG,CAAC,EAAE;MACpB,IAAI,CAAC5a,MAAM,CAACuD,KAAK,CAAC,cAAcqX,YAAY,wBAAwB,CAAC;MACrE,IAAI,CAACza,aAAa,CAAC2D,IAAI,CAACC,KAAK,CAACxH,IAAI,CAAC,IAAI,CAAC6D,iBAAiB,CAAC4D,MAAM,EAAE,CAAC,CAAC;MACpE,IAAI,CAACC,iBAAiB,EAAE;;EAE5B;EACQ2H,gBAAgBA,CAAA;IACtB,OAAOzI,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE;EAC7C;EACQyG,gBAAgBA,CAAC6D,OAAgB;IACvC,IAAI,CAACA,OAAO,EAAE;MACZ,IAAI,CAAC1N,MAAM,CAACmE,KAAK,CACf,6DAA6D,CAC9D;MACD,MAAM,IAAI4D,KAAK,CAAC,4BAA4B,CAAC;;IAG/C,IAAI;MACF;MACA,IAAI,CAAC2F,OAAO,CAAC9J,EAAE,IAAI,CAAC8J,OAAO,CAACQ,GAAG,EAAE;QAC/B,IAAI,CAAClO,MAAM,CAACmE,KAAK,CACf,wCAAwC,EACxCvB,SAAS,EACT8K,OAAO,CACR;QACD,MAAM,IAAI3F,KAAK,CAAC,wBAAwB,CAAC;;MAG3C;MACA,IAAI+S,gBAAgB;MACpB,IAAI;QACFA,gBAAgB,GAAGpN,OAAO,CAAC/B,MAAM,GAC7B,IAAI,CAACiB,aAAa,CAACc,OAAO,CAAC/B,MAAM,CAAC,GAClC/I,SAAS;OACd,CAAC,OAAOuB,KAAK,EAAE;QACd,IAAI,CAACnE,MAAM,CAACyF,IAAI,CACd,yEAAyE,EACzEtB,KAAK,CACN;QACD2W,gBAAgB,GAAG;UACjB5M,GAAG,EAAER,OAAO,CAAClE,QAAQ,IAAI,SAAS;UAClC5F,EAAE,EAAE8J,OAAO,CAAClE,QAAQ,IAAI,SAAS;UACjCqC,QAAQ,EAAE,cAAc;UACxBkP,KAAK,EAAE,qBAAqB;UAC5BC,IAAI,EAAE,MAAM;UACZC,QAAQ,EAAE;SACX;;MAGH;MACA,IAAIC,kBAAkB;MACtB,IAAIxN,OAAO,CAACyN,QAAQ,EAAE;QACpB,IAAI;UACFD,kBAAkB,GAAG,IAAI,CAACtO,aAAa,CAACc,OAAO,CAACyN,QAAQ,CAAC;SAC1D,CAAC,OAAOhX,KAAK,EAAE;UACd,IAAI,CAACnE,MAAM,CAACyF,IAAI,CACd,2EAA2E,EAC3EtB,KAAK,CACN;UACD+W,kBAAkB,GAAG;YACnBhN,GAAG,EAAER,OAAO,CAAC/F,UAAU,IAAI,SAAS;YACpC/D,EAAE,EAAE8J,OAAO,CAAC/F,UAAU,IAAI,SAAS;YACnCkE,QAAQ,EAAE,cAAc;YACxBkP,KAAK,EAAE,qBAAqB;YAC5BC,IAAI,EAAE,MAAM;YACZC,QAAQ,EAAE;WACX;;;MAIL;MACA,MAAMG,qBAAqB,GACzB1N,OAAO,CAACmL,WAAW,EAAE3c,GAAG,CAAE6c,GAAG,KAAM;QACjCnV,EAAE,EAAEmV,GAAG,CAACnV,EAAE,IAAImV,GAAG,CAAC7K,GAAG,IAAI,cAAcjG,IAAI,CAACC,GAAG,EAAE,EAAE;QACnDmT,GAAG,EAAEtC,GAAG,CAACsC,GAAG,IAAI,EAAE;QAClBtU,IAAI,EAAEgS,GAAG,CAAChS,IAAI,IAAI,SAAS;QAC3B9B,IAAI,EAAE8T,GAAG,CAAC9T,IAAI,IAAI,YAAY;QAC9Bf,IAAI,EAAE6U,GAAG,CAAC7U,IAAI,IAAI,CAAC;QACnB4D,QAAQ,EAAEiR,GAAG,CAACjR,QAAQ,IAAI;OAC3B,CAAC,CAAC,IAAI,EAAE;MAEX;MACA,MAAMyD,iBAAiB,GAAG;QACxB,GAAGmC,OAAO;QACVQ,GAAG,EAAER,OAAO,CAAC9J,EAAE,IAAI8J,OAAO,CAACQ,GAAG;QAC9BtK,EAAE,EAAE8J,OAAO,CAAC9J,EAAE,IAAI8J,OAAO,CAACQ,GAAG;QAC7B9D,OAAO,EAAEsD,OAAO,CAACtD,OAAO,IAAI,EAAE;QAC9BuB,MAAM,EAAEmP,gBAAgB;QACxB9S,SAAS,EAAE,IAAI,CAACoE,aAAa,CAACsB,OAAO,CAAC1F,SAAS,CAAC;QAChD+D,MAAM,EAAE2B,OAAO,CAAC3B,MAAM,GAAG,IAAI,CAACK,aAAa,CAACsB,OAAO,CAAC3B,MAAM,CAAC,GAAGnJ,SAAS;QACvEiW,WAAW,EAAEuC,qBAAqB;QAClC9S,QAAQ,EAAEoF,OAAO,CAACpF,QAAQ,IAAI;OAC/B;MAED;MACA,IAAI4S,kBAAkB,EAAE;QACtB3P,iBAAiB,CAAC4P,QAAQ,GAAGD,kBAAkB;;MAGjD,IAAI,CAAClb,MAAM,CAACuD,KAAK,CAAC,kDAAkD,EAAE;QACpEwG,SAAS,EAAEwB,iBAAiB,CAAC3H,EAAE;QAC/B4F,QAAQ,EAAE+B,iBAAiB,CAACI,MAAM,EAAE/H;OACrC,CAAC;MAEF,OAAO2H,iBAAiB;KACzB,CAAC,OAAOpH,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,6CAA6C,EAC7CA,KAAK,YAAY4D,KAAK,GAAG5D,KAAK,GAAG,IAAI4D,KAAK,CAACuT,MAAM,CAACnX,KAAK,CAAC,CAAC,EACzDuJ,OAAO,CACR;MACD,MAAM,IAAI3F,KAAK,CACb,gCACE5D,KAAK,YAAY4D,KAAK,GAAG5D,KAAK,CAACuJ,OAAO,GAAG4N,MAAM,CAACnX,KAAK,CACvD,EAAE,CACH;;EAEL;EACQoX,mBAAmBA,CAAC7N,OAAY;IACtC,OAAO;MACL,GAAGA,OAAO;MACV,IAAIA,OAAO,CAACmL,WAAW,IAAI;QACzBA,WAAW,EAAEnL,OAAO,CAACmL,WAAW,CAAC3c,GAAG,CAAE6c,GAAQ,KAAM;UAClDsC,GAAG,EAAEtC,GAAG,CAACsC,GAAG;UACZtU,IAAI,EAAEgS,GAAG,CAAChS,IAAI;UACd,IAAIgS,GAAG,CAAC9T,IAAI,IAAI;YAAEA,IAAI,EAAE8T,GAAG,CAAC9T;UAAI,CAAE,CAAC;UACnC,IAAI8T,GAAG,CAAC7U,IAAI,IAAI;YAAEA,IAAI,EAAE6U,GAAG,CAAC7U;UAAI,CAAE;SACnC,CAAC;OACH;KACF;EACH;EACO0I,aAAaA,CAAC6K,IAAS;IAC5B,IAAI,CAACA,IAAI,EAAE;MACT,MAAM,IAAI1P,KAAK,CAAC,yBAAyB,CAAC;;IAG5C;IACA,MAAM+E,MAAM,GAAG2K,IAAI,CAAC7T,EAAE,IAAI6T,IAAI,CAACvJ,GAAG;IAClC,IAAI,CAACpB,MAAM,EAAE;MACX,MAAM,IAAI/E,KAAK,CAAC,qBAAqB,CAAC;;IAGxC;IACA,MAAM8D,QAAQ,GAAG4L,IAAI,CAAC5L,QAAQ,IAAI,cAAc;IAChD,MAAMkP,KAAK,GAAGtD,IAAI,CAACsD,KAAK,IAAI,QAAQjO,MAAM,cAAc;IACxD,MAAMmO,QAAQ,GACZxD,IAAI,CAACwD,QAAQ,KAAKrY,SAAS,IAAI6U,IAAI,CAACwD,QAAQ,KAAK,IAAI,GACjDxD,IAAI,CAACwD,QAAQ,GACb,IAAI;IACV,MAAMD,IAAI,GAAGvD,IAAI,CAACuD,IAAI,IAAI,MAAM;IAEhC;IACA,OAAO;MACL9M,GAAG,EAAEpB,MAAM;MACXlJ,EAAE,EAAEkJ,MAAM;MACVjB,QAAQ,EAAEA,QAAQ;MAClBkP,KAAK,EAAEA,KAAK;MACZC,IAAI,EAAEA,IAAI;MACVC,QAAQ,EAAEA,QAAQ;MAClB;MACAO,KAAK,EAAE/D,IAAI,CAAC+D,KAAK,IAAI,IAAI;MACzBC,GAAG,EAAEhE,IAAI,CAACgE,GAAG;MACbpE,QAAQ,EAAEI,IAAI,CAACJ,QAAQ,IAAI,KAAK;MAChCqE,UAAU,EAAEjE,IAAI,CAACiE,UAAU,GAAG,IAAIzT,IAAI,CAACwP,IAAI,CAACiE,UAAU,CAAC,GAAG9Y,SAAS;MACnE+Y,SAAS,EAAElE,IAAI,CAACkE,SAAS,GAAG,IAAI1T,IAAI,CAACwP,IAAI,CAACkE,SAAS,CAAC,GAAG/Y,SAAS;MAChEgZ,SAAS,EAAEnE,IAAI,CAACmE,SAAS,GAAG,IAAI3T,IAAI,CAACwP,IAAI,CAACmE,SAAS,CAAC,GAAGhZ,SAAS;MAChEiZ,cAAc,EAAEpE,IAAI,CAACoE,cAAc;MACnCC,cAAc,EAAErE,IAAI,CAACqE,cAAc;MACnCC,SAAS,EAAEtE,IAAI,CAACsE;KACjB;EACH;EACQ5O,qBAAqBA,CAACD,IAAkB;IAC9C,IAAI,CAACA,IAAI,EAAE;MACT,IAAI,CAAClN,MAAM,CAACmE,KAAK,CACf,kEAAkE,CACnE;MACD,MAAM,IAAI4D,KAAK,CAAC,iCAAiC,CAAC;;IAGpD,IAAI;MACF;MACA,IAAI,CAACmF,IAAI,CAACtJ,EAAE,IAAI,CAACsJ,IAAI,CAACgB,GAAG,EAAE;QACzB,IAAI,CAAClO,MAAM,CAACmE,KAAK,CACf,6CAA6C,EAC7CvB,SAAS,EACTsK,IAAI,CACL;QACD,MAAM,IAAInF,KAAK,CAAC,6BAA6B,CAAC;;MAGhD;MACA,MAAMiU,sBAAsB,GAAG,EAAE;MACjC,IAAI9O,IAAI,CAACK,YAAY,IAAIxJ,KAAK,CAACkY,OAAO,CAAC/O,IAAI,CAACK,YAAY,CAAC,EAAE;QACzD,KAAK,MAAM2O,WAAW,IAAIhP,IAAI,CAACK,YAAY,EAAE;UAC3C,IAAI;YACF,IAAI2O,WAAW,EAAE;cACfF,sBAAsB,CAAC/K,IAAI,CAAC,IAAI,CAACrE,aAAa,CAACsP,WAAW,CAAC,CAAC;;WAE/D,CAAC,OAAO/X,KAAK,EAAE;YACd,IAAI,CAACnE,MAAM,CAACyF,IAAI,CACd,0DAA0D,EAC1DtB,KAAK,CACN;;;OAGN,MAAM;QACL,IAAI,CAACnE,MAAM,CAACyF,IAAI,CACd,iFAAiF,EACjFyH,IAAI,CACL;;MAGH;MACA,MAAMiP,kBAAkB,GAAG,EAAE;MAC7B,IAAIjP,IAAI,CAACvD,QAAQ,IAAI5F,KAAK,CAACkY,OAAO,CAAC/O,IAAI,CAACvD,QAAQ,CAAC,EAAE;QACjD,IAAI,CAAC3J,MAAM,CAACuD,KAAK,CAAC,mDAAmD,EAAE;UACrEoN,KAAK,EAAEzD,IAAI,CAACvD,QAAQ,CAACnG;SACtB,CAAC;QAEF,KAAK,MAAMkK,OAAO,IAAIR,IAAI,CAACvD,QAAQ,EAAE;UACnC,IAAI;YACF,IAAI+D,OAAO,EAAE;cACX,MAAMnC,iBAAiB,GAAG,IAAI,CAAC1B,gBAAgB,CAAC6D,OAAO,CAAC;cACxD,IAAI,CAAC1N,MAAM,CAACuD,KAAK,CACf,kDAAkD,EAClD;gBACEwG,SAAS,EAAEwB,iBAAiB,CAAC3H,EAAE;gBAC/BwG,OAAO,EAAEmB,iBAAiB,CAACnB,OAAO,EAAEK,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;gBACpDkB,MAAM,EAAEJ,iBAAiB,CAACI,MAAM,EAAEE;eACnC,CACF;cACDsQ,kBAAkB,CAAClL,IAAI,CAAC1F,iBAAiB,CAAC;;WAE7C,CAAC,OAAOpH,KAAK,EAAE;YACd,IAAI,CAACnE,MAAM,CAACyF,IAAI,CACd,sEAAsE,EACtEtB,KAAK,CACN;;;OAGN,MAAM;QACL,IAAI,CAACnE,MAAM,CAACuD,KAAK,CACf,8EAA8E,CAC/E;;MAGH;MACA,IAAI6Y,qBAAqB,GAAG,IAAI;MAChC,IAAIlP,IAAI,CAACmM,WAAW,EAAE;QACpB,IAAI;UACF+C,qBAAqB,GAAG,IAAI,CAACvS,gBAAgB,CAACqD,IAAI,CAACmM,WAAW,CAAC;SAChE,CAAC,OAAOlV,KAAK,EAAE;UACd,IAAI,CAACnE,MAAM,CAACyF,IAAI,CACd,6DAA6D,EAC7DtB,KAAK,CACN;;;MAIL;MACA,MAAMmJ,sBAAsB,GAAG;QAC7B,GAAGJ,IAAI;QACPgB,GAAG,EAAEhB,IAAI,CAACtJ,EAAE,IAAIsJ,IAAI,CAACgB,GAAG;QACxBtK,EAAE,EAAEsJ,IAAI,CAACtJ,EAAE,IAAIsJ,IAAI,CAACgB,GAAG;QACvBX,YAAY,EAAEyO,sBAAsB;QACpCrS,QAAQ,EAAEwS,kBAAkB;QAC5B9C,WAAW,EAAE+C,qBAAqB;QAClCC,WAAW,EAAEnP,IAAI,CAACmP,WAAW,IAAI,CAAC;QAClCtO,OAAO,EAAE,CAAC,CAACb,IAAI,CAACa,OAAO;QACvB4N,SAAS,EAAE,IAAI,CAACvP,aAAa,CAACc,IAAI,CAACyO,SAAS,CAAC;QAC7CC,SAAS,EAAE,IAAI,CAACxP,aAAa,CAACc,IAAI,CAAC0O,SAAS;OAC7C;MAED,IAAI,CAAC5b,MAAM,CAACuD,KAAK,CACf,uDAAuD,EACvD;QACEsE,cAAc,EAAEyF,sBAAsB,CAAC1J,EAAE;QACzC0Y,gBAAgB,EAAEN,sBAAsB,CAACxY,MAAM;QAC/C+Y,YAAY,EAAEJ,kBAAkB,CAAC3Y;OAClC,CACF;MAED,OAAO8J,sBAAsB;KAC9B,CAAC,OAAOnJ,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,kDAAkD,EAClDA,KAAK,YAAY4D,KAAK,GAAG5D,KAAK,GAAG,IAAI4D,KAAK,CAACuT,MAAM,CAACnX,KAAK,CAAC,CAAC,EACzD+I,IAAI,CACL;MACD,MAAM,IAAInF,KAAK,CACb,qCACE5D,KAAK,YAAY4D,KAAK,GAAG5D,KAAK,CAACuJ,OAAO,GAAG4N,MAAM,CAACnX,KAAK,CACvD,EAAE,CACH;;EAEL;EACQiI,aAAaA,CAACzJ,IAA+B;IACnD,IAAI,CAACA,IAAI,EAAE,OAAO,IAAIsF,IAAI,EAAE;IAC5B,IAAI;MACF,OAAO,OAAOtF,IAAI,KAAK,QAAQ,GAAG,IAAIsF,IAAI,CAACtF,IAAI,CAAC,GAAGA,IAAI;KACxD,CAAC,OAAOwB,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACyF,IAAI,CAAC,yBAAyB9C,IAAI,EAAE,EAAEwB,KAAK,CAAC;MACxD,OAAO,IAAI8D,IAAI,EAAE;;EAErB;EAEA;EACQ0E,QAAQA,CAAChK,IAA+B;IAC9C,IAAI,CAACA,IAAI,EAAE,OAAO,IAAIsF,IAAI,EAAE;IAC5B,IAAI;MACF,OAAO,OAAOtF,IAAI,KAAK,QAAQ,GAAG,IAAIsF,IAAI,CAACtF,IAAI,CAAC,GAAGA,IAAI;KACxD,CAAC,OAAOwB,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACyF,IAAI,CAAC,+BAA+B9C,IAAI,EAAE,EAAEwB,KAAK,CAAC;MAC9D,OAAO,IAAI8D,IAAI,EAAE;;EAErB;EAOQoS,qBAAqBA,CAAC1W,YAA0B;IACtD,IAAI,CAAC3D,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,0BAA0B,EAC1BI,YAAY,CACb;IAED,IAAI,CAACA,YAAY,EAAE;MACjB,IAAI,CAAC3D,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,mCAAmC,CAAC;MACxE,MAAM,IAAI4D,KAAK,CAAC,0BAA0B,CAAC;;IAG7C;IACA,MAAMmI,cAAc,GAAGvM,YAAY,CAACC,EAAE,IAAKD,YAAoB,CAACuK,GAAG;IACnE,IAAI,CAACgC,cAAc,EAAE;MACnB,IAAI,CAAClQ,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,4BAA4B,EAC5BR,YAAY,CACb;MACD,MAAM,IAAIoE,KAAK,CAAC,6BAA6B,CAAC;;IAGhD,IAAI,CAACpE,YAAY,CAACqE,SAAS,EAAE;MAC3B,IAAI,CAAChI,MAAM,CAACyF,IAAI,CACd,gBAAgB,EAChB,uDAAuD,EACvD9B,YAAY,CACb;MACDA,YAAY,CAACqE,SAAS,GAAG,IAAIC,IAAI,EAAE;;IAGrC,IAAI;MACF,MAAMmS,UAAU,GAAG;QACjB,GAAGzW,YAAY;QACfuK,GAAG,EAAEgC,cAAc;QACnBtM,EAAE,EAAEsM,cAAc;QAClBlI,SAAS,EAAE,IAAIC,IAAI,CAACtE,YAAY,CAACqE,SAAS,CAAC;QAC3C,IAAIrE,YAAY,CAAC6F,QAAQ,IAAI;UAC3BA,QAAQ,EAAE,IAAI,CAACgT,eAAe,CAAC7Y,YAAY,CAAC6F,QAAQ;SACrD,CAAC;QACF,IAAI7F,YAAY,CAAC+J,OAAO,IAAI;UAC1BA,OAAO,EAAE,IAAI,CAAC6N,mBAAmB,CAAC5X,YAAY,CAAC+J,OAAO;SACvD;OACF;MAED,IAAI,CAAC1N,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,gCAAgC,EAChC6W,UAAU,CACX;MACD,OAAOA,UAAU;KAClB,CAAC,OAAOjW,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,gCAAgC,EAChCA,KAAK,CACN;MACD,MAAMA,KAAK;;EAEf;EACQqY,eAAeA,CAAC7Q,MAAW;IACjC,OAAO;MACL/H,EAAE,EAAE+H,MAAM,CAAC/H,EAAE;MACbiI,QAAQ,EAAEF,MAAM,CAACE,QAAQ;MACzB,IAAIF,MAAM,CAAC6P,KAAK,IAAI;QAAEA,KAAK,EAAE7P,MAAM,CAAC6P;MAAK,CAAE;KAC5C;EACH;EAEA;;;;;EAKQD,mBAAmBA,CAAC7N,OAAY;IACtC,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IAEzB,OAAO;MACL9J,EAAE,EAAE8J,OAAO,CAAC9J,EAAE,IAAI8J,OAAO,CAACQ,GAAG;MAC7B9D,OAAO,EAAEsD,OAAO,CAACtD,OAAO,IAAI,EAAE;MAC9BrD,IAAI,EAAE2G,OAAO,CAAC3G,IAAI,IAAI,MAAM;MAC5BiB,SAAS,EAAE,IAAI,CAAC2E,QAAQ,CAACe,OAAO,CAAC1F,SAAS,CAAC;MAC3C6Q,WAAW,EAAEnL,OAAO,CAACmL,WAAW,IAAI,EAAE;MACtC,IAAInL,OAAO,CAAC/B,MAAM,IAAI;QAAEA,MAAM,EAAE,IAAI,CAAC6Q,eAAe,CAAC9O,OAAO,CAAC/B,MAAM;MAAC,CAAE;KACvE;EACH;EACQqD,WAAWA,CAAC7O,aAA6B;IAC/C,IAAI,CAACH,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,oCAAoCpD,aAAa,CAACqD,MAAM,gBAAgB,CACzE;IAED,IAAIrD,aAAa,CAACqD,MAAM,KAAK,CAAC,EAAE;MAC9B,IAAI,CAACxD,MAAM,CAACyF,IAAI,CAAC,gBAAgB,EAAE,qCAAqC,CAAC;MACzE;;IAGFY,OAAO,CAACC,GAAG,CACT,iCAAiCnG,aAAa,CAACqD,MAAM,gBAAgB,CACtE;IAED;IACA,MAAMiZ,kBAAkB,GAAGtc,aAAa,CAAC9D,MAAM,CAC5CwS,KAAK,IAAKA,KAAK,KAAKA,KAAK,CAACjL,EAAE,IAAKiL,KAAa,CAACX,GAAG,CAAC,CACrD;IAED,IAAIuO,kBAAkB,CAACjZ,MAAM,KAAKrD,aAAa,CAACqD,MAAM,EAAE;MACtD6C,OAAO,CAACZ,IAAI,CACV,SACEtF,aAAa,CAACqD,MAAM,GAAGiZ,kBAAkB,CAACjZ,MAC5C,kCAAkC,CACnC;;IAGH;IACAiZ,kBAAkB,CAAC/Y,OAAO,CAAC,CAACmL,KAAK,EAAEE,KAAK,KAAI;MAC1C,IAAI;QACF;QACA,MAAM2N,OAAO,GAAG7N,KAAK,CAACjL,EAAE,IAAKiL,KAAa,CAACX,GAAG;QAC9C,IAAI,CAACwO,OAAO,EAAE;UACZrW,OAAO,CAAClC,KAAK,CAAC,0BAA0B,EAAE0K,KAAK,CAAC;UAChD;;QAGF;QACA,MAAMuL,UAAU,GAAG,IAAI,CAACC,qBAAqB,CAACxL,KAAK,CAAC;QAEpD;QACA,IAAI,IAAI,CAACzO,iBAAiB,CAAC0O,GAAG,CAACsL,UAAU,CAACxW,EAAE,CAAC,EAAE;UAC7CyC,OAAO,CAACC,GAAG,CACT,gBAAgB8T,UAAU,CAACxW,EAAE,oCAAoC,CAClE;UACD;;QAGF;QACA,IAAI,CAACxD,iBAAiB,CAACyD,GAAG,CAACuW,UAAU,CAACxW,EAAE,EAAEwW,UAAU,CAAC;QAErD/T,OAAO,CAACC,GAAG,CAAC,sBAAsB8T,UAAU,CAACxW,EAAE,WAAW,CAAC;OAC5D,CAAC,OAAOO,KAAK,EAAE;QACdkC,OAAO,CAAClC,KAAK,CAAC,iCAAiC4K,KAAK,GAAG,CAAC,GAAG,EAAE5K,KAAK,CAAC;QACnEkC,OAAO,CAAClC,KAAK,CAAC,2BAA2B,EAAE0K,KAAK,CAAC;;IAErD,CAAC,CAAC;IAEFxI,OAAO,CAACC,GAAG,CACT,4CAA4C,IAAI,CAAClG,iBAAiB,CAAC8D,IAAI,gBAAgB,CACxF;IAED;IACA,IAAI,CAACgL,+BAA+B,EAAE;EACxC;EACQjL,iBAAiBA,CAAA;IACvB,MAAM0M,KAAK,GAAG5M,KAAK,CAACxH,IAAI,CAAC,IAAI,CAAC6D,iBAAiB,CAAC4D,MAAM,EAAE,CAAC,CAAC3H,MAAM,CAC7DmT,CAAC,IAAK,CAACA,CAAC,CAAC9D,MAAM,CACjB,CAAClI,MAAM;IACR,IAAI,CAAClD,iBAAiB,CAACwD,IAAI,CAAC6M,KAAK,CAAC;EACpC;EACQ2J,uBAAuBA,CAAC3W,YAA0B;IACxD;IACA,IAAI,CAAC,IAAI,CAACvD,iBAAiB,CAAC0O,GAAG,CAACnL,YAAY,CAACC,EAAE,CAAC,EAAE;MAChD,IAAI,CAACxD,iBAAiB,CAACyD,GAAG,CAACF,YAAY,CAACC,EAAE,EAAED,YAAY,CAAC;MACzD,IAAI,CAACxD,aAAa,CAAC2D,IAAI,CAACC,KAAK,CAACxH,IAAI,CAAC,IAAI,CAAC6D,iBAAiB,CAAC4D,MAAM,EAAE,CAAC,CAAC;MACpE,IAAI,CAACC,iBAAiB,EAAE;MACxB;MACA,IAAI,CAACiL,+BAA+B,EAAE;KACvC,MAAM;MACL,IAAI,CAAClP,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,gBAAgBI,YAAY,CAACC,EAAE,oCAAoC,CACpE;;EAEL;EACQ6N,wBAAwBA,CAACkL,GAAa,EAAEjR,MAAe;IAC7DiR,GAAG,CAACjZ,OAAO,CAAEE,EAAE,IAAI;MACjB,MAAMiL,KAAK,GAAG,IAAI,CAACzO,iBAAiB,CAAC4Q,GAAG,CAACpN,EAAE,CAAC;MAC5C,IAAIiL,KAAK,EAAE;QACT,IAAI,CAACzO,iBAAiB,CAACyD,GAAG,CAACD,EAAE,EAAE;UAAE,GAAGiL,KAAK;UAAEnD;QAAM,CAAE,CAAC;;IAExD,CAAC,CAAC;IACF,IAAI,CAACvL,aAAa,CAAC2D,IAAI,CAACC,KAAK,CAACxH,IAAI,CAAC,IAAI,CAAC6D,iBAAiB,CAAC4D,MAAM,EAAE,CAAC,CAAC;IACpE,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EACA;EACA2Y,WAAWA,CAAC/U,cAAsB;IAChC,MAAMiF,MAAM,GAAG,IAAI,CAAClB,gBAAgB,EAAE;IACtC,IAAI,CAACkB,MAAM,EAAE;MACX,IAAI,CAAC9M,MAAM,CAACyF,IAAI,CAAC,gBAAgB,EAAE,iCAAiC,CAAC;MACrE,OAAO3J,EAAE,CAAC,KAAK,CAAC;;IAGlB,OAAO,IAAI,CAACiE,MAAM,CACfkK,MAAM,CAAsB;MAC3BC,QAAQ,EAAExM,qBAAqB;MAC/BgM,SAAS,EAAE;QACT0O,KAAK,EAAE;UACLvQ,cAAc;UACdiF;;;KAGL,CAAC,CACDnI,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAKA,MAAM,CAACzE,IAAI,EAAEgY,WAAW,IAAI,KAAK,CAAC,EAClDzgB,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iCAAiC,EACjCA,KAAK,CACN;MACD,OAAOpI,UAAU,CACf,MAAM,IAAIgM,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;EACL;EAEA8U,UAAUA,CAAChV,cAAsB;IAC/B,MAAMiF,MAAM,GAAG,IAAI,CAAClB,gBAAgB,EAAE;IACtC,IAAI,CAACkB,MAAM,EAAE;MACX,IAAI,CAAC9M,MAAM,CAACyF,IAAI,CAAC,gBAAgB,EAAE,gCAAgC,CAAC;MACpE,OAAO3J,EAAE,CAAC,KAAK,CAAC;;IAGlB,OAAO,IAAI,CAACiE,MAAM,CACfkK,MAAM,CAAqB;MAC1BC,QAAQ,EAAEvM,oBAAoB;MAC9B+L,SAAS,EAAE;QACT0O,KAAK,EAAE;UACLvQ,cAAc;UACdiF;;;KAGL,CAAC,CACDnI,IAAI,CACHzI,GAAG,CAAEmN,MAAM,IAAKA,MAAM,CAACzE,IAAI,EAAEiY,UAAU,IAAI,KAAK,CAAC,EACjD1gB,UAAU,CAAEgI,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iCAAiC,EACjCA,KAAK,CACN;MACD,OAAOpI,UAAU,CAAC,MAAM,IAAIgM,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;EACA+U,oBAAoBA,CAAA;IAClB,IAAI,CAACtc,aAAa,CAACkD,OAAO,CAAEuV,GAAG,IAAKA,GAAG,CAAC8D,WAAW,EAAE,CAAC;IACtD,IAAI,CAACvc,aAAa,GAAG,EAAE;IACvB,IAAI,IAAI,CAAC+Z,eAAe,EAAE;MACxByC,aAAa,CAAC,IAAI,CAACzC,eAAe,CAAC;;IAErC,IAAI,CAACna,iBAAiB,CAACqD,KAAK,EAAE;IAC9B,IAAI,CAACzD,MAAM,CAACuD,KAAK,CAAC,+BAA+B,CAAC;EACpD;EAEA0Z,WAAWA,CAAA;IACT,IAAI,CAACH,oBAAoB,EAAE;EAC7B;;;uBA9yHWjd,cAAc,EAAAqd,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAD,EAAA,CAAAM,MAAA;IAAA;EAAA;;;aAAd3d,cAAc;MAAA4d,OAAA,EAAd5d,cAAc,CAAA6d,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}