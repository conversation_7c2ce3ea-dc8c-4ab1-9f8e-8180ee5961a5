{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/services/projets.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/dialog\";\nimport * as i4 from \"src/app/services/data.service\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = [\"confirmDialog\"];\nfunction ListProjectComponent_div_11_div_1_div_30_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 45);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 46);\n    i0.ɵɵelement(3, \"path\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"span\", 48);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"a\", 49);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(7, \"svg\", 50);\n    i0.ɵɵelement(8, \"path\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \" T\\u00E9l\\u00E9charger \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r9 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.getFileName(file_r9));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"href\", ctx_r8.getFileUrl(file_r9), i0.ɵɵsanitizeUrl)(\"download\", ctx_r8.getFileName(file_r9));\n  }\n}\nfunction ListProjectComponent_div_11_div_1_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"h4\", 31);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 32);\n    i0.ɵɵelement(3, \"path\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"div\", 42);\n    i0.ɵɵtemplate(6, ListProjectComponent_div_11_div_1_div_30_div_6_Template, 10, 3, \"div\", 43);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const projet_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" Fichiers (\", projet_r5.fichiers.length, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", projet_r5.fichiers);\n  }\n}\nfunction ListProjectComponent_div_11_div_1_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"h4\", 31);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 32);\n    i0.ɵɵelement(3, \"path\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Fichiers \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"div\", 52);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 53);\n    i0.ɵɵelement(7, \"path\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Aucun fichier joint \");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c1 = function (a1) {\n  return [\"/admin/projects/editProjet\", a1];\n};\nconst _c2 = function (a1) {\n  return [\"/admin/projects/details\", a1];\n};\nconst _c3 = function () {\n  return [\"/admin/projects/rendus\"];\n};\nconst _c4 = function (a0) {\n  return {\n    projetId: a0\n  };\n};\nfunction ListProjectComponent_div_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14)(2, \"div\", 15)(3, \"h3\", 16);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 17)(6, \"a\", 18);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(7, \"svg\", 19);\n    i0.ɵɵelement(8, \"path\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(9, \"a\", 21);\n    i0.ɵɵlistener(\"click\", function ListProjectComponent_div_11_div_1_Template_a_click_9_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const projet_r5 = restoredCtx.$implicit;\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(projet_r5._id && ctx_r11.openDeleteDialog(projet_r5._id));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(10, \"svg\", 19);\n    i0.ɵɵelement(11, \"path\", 22);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(12, \"div\", 23)(13, \"span\", 24);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 25);\n    i0.ɵɵtext(16, \"\\u2022\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 26);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(18, \"svg\", 27);\n    i0.ɵɵelement(19, \"path\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(22, \"div\", 29)(23, \"div\", 30)(24, \"h4\", 31);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(25, \"svg\", 32);\n    i0.ɵɵelement(26, \"path\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(27, \" Description \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(28, \"p\", 34);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(30, ListProjectComponent_div_11_div_1_div_30_Template, 7, 2, \"div\", 35);\n    i0.ɵɵtemplate(31, ListProjectComponent_div_11_div_1_div_31_Template, 9, 0, \"div\", 35);\n    i0.ɵɵelementStart(32, \"div\", 36)(33, \"a\", 37);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(34, \"svg\", 38);\n    i0.ɵɵelement(35, \"path\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(36, \" D\\u00E9tails \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(37, \"a\", 39);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(38, \"svg\", 38);\n    i0.ɵɵelement(39, \"path\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(40, \" Rendus \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const projet_r5 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", projet_r5.titre, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(13, _c1, projet_r5._id));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(projet_r5.groupe || \"Tous\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(21, 10, projet_r5.dateLimite, \"dd/MM/yyyy\"), \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", projet_r5.description || \"Aucune description fournie\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", projet_r5.fichiers && projet_r5.fichiers.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !projet_r5.fichiers || projet_r5.fichiers.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(15, _c2, projet_r5._id));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(17, _c3))(\"queryParams\", i0.ɵɵpureFunction1(18, _c4, projet_r5._id));\n  }\n}\nfunction ListProjectComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵtemplate(1, ListProjectComponent_div_11_div_1_Template, 41, 20, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.projets);\n  }\n}\nfunction ListProjectComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵelement(1, \"div\", 55);\n    i0.ɵɵelementStart(2, \"p\", 56);\n    i0.ɵɵtext(3, \"Chargement des projets...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ListProjectComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"div\", 58);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 59);\n    i0.ɵɵelement(3, \"path\", 60);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"h3\", 61);\n    i0.ɵɵtext(5, \" Aucun projet disponible \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 62);\n    i0.ɵɵtext(7, \" Commencez par cr\\u00E9er votre premier projet en cliquant sur le bouton ci-dessous \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"a\", 63);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 64);\n    i0.ɵɵelement(10, \"path\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11, \" Ajouter un projet \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ListProjectComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"div\", 67)(2, \"h2\", 68);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 69);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 70)(7, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function ListProjectComponent_div_14_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.onDeleteCancel());\n    });\n    i0.ɵɵtext(8, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function ListProjectComponent_div_14_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.onDeleteConfirm());\n    });\n    i0.ɵɵtext(10, \" Supprimer \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.dialogData.title, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.dialogData.message);\n  }\n}\nexport class ListProjectComponent {\n  constructor(projetService, router, dialog, authService) {\n    this.projetService = projetService;\n    this.router = router;\n    this.dialog = dialog;\n    this.authService = authService;\n    this.projets = [];\n    this.isLoading = true;\n    this.isAdmin = false;\n    this.showDeleteDialog = false;\n    this.projectIdToDelete = null;\n    this.dialogData = {\n      title: 'Confirmer la suppression',\n      message: 'Êtes-vous sûr de vouloir supprimer ce projet?'\n    };\n  }\n  ngOnInit() {\n    this.loadProjets();\n    this.checkAdminStatus();\n  }\n  loadProjets() {\n    this.isLoading = true;\n    console.log('Chargement des projets...');\n    this.projetService.getProjets().subscribe({\n      next: projets => {\n        console.log('Projets reçus:', projets);\n        this.projets = projets;\n        this.isLoading = false;\n      },\n      error: err => {\n        console.error('Error loading projects', err);\n        this.isLoading = false;\n        // Afficher un message d'erreur à l'utilisateur\n        alert('Erreur lors du chargement des projets: ' + (err.error?.message || err.message || 'Erreur inconnue'));\n      }\n    });\n  }\n  // Alias pour loadProjets pour assurer la compatibilité avec les méthodes existantes\n  loadProjects() {\n    this.loadProjets();\n  }\n  checkAdminStatus() {\n    this.isAdmin = this.authService.isAdmin();\n    console.log('Statut admin:', this.isAdmin);\n  }\n  editProjet(projetId) {\n    if (!projetId) {\n      console.error('ID du projet non défini');\n      return;\n    }\n    console.log('Édition du projet:', projetId);\n    this.router.navigate(['/admin/projects/edit', projetId]);\n  }\n  viewProjetDetails(projetId) {\n    if (!projetId) {\n      console.error('ID du projet non défini');\n      return;\n    }\n    console.log('Affichage des détails du projet:', projetId);\n    this.router.navigate(['/admin/projects/detail', projetId]);\n  }\n  deleteProjet(projetId) {\n    if (!projetId) {\n      console.error('ID du projet non défini');\n      return;\n    }\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {\n      console.log('Suppression du projet confirmée:', projetId);\n      this.projetService.deleteProjet(projetId).subscribe({\n        next: () => {\n          console.log('Projet supprimé avec succès');\n          alert('Projet supprimé avec succès');\n          this.loadProjets(); // Reload the projects after deletion\n        },\n\n        error: err => {\n          console.error('Error deleting project', err);\n          alert('Erreur lors de la suppression du projet: ' + (err.error?.message || err.message || 'Erreur inconnue'));\n        }\n      });\n    }\n  }\n  openDeleteDialog(id) {\n    if (!id) {\n      console.error('ID du projet non défini');\n      return;\n    }\n    console.log('Ouverture de la boîte de dialogue de confirmation pour la suppression du projet:', id);\n    this.showDeleteDialog = true; // Show confirmation dialog\n    this.projectIdToDelete = id; // Store the ID of the project to be deleted\n  }\n\n  onDeleteConfirm() {\n    if (this.projectIdToDelete) {\n      console.log('Suppression du projet confirmée:', this.projectIdToDelete);\n      this.projetService.deleteProjet(this.projectIdToDelete).subscribe({\n        next: () => {\n          console.log('Projet supprimé avec succès');\n          alert('Projet supprimé avec succès');\n          this.loadProjets(); // Reload the projects after deletion\n          this.showDeleteDialog = false; // Close the confirmation dialog\n        },\n\n        error: err => {\n          console.error('Error deleting project', err);\n          alert('Erreur lors de la suppression du projet: ' + (err.error?.message || err.message || 'Erreur inconnue'));\n          this.showDeleteDialog = false;\n        }\n      });\n    }\n  }\n  onDeleteCancel() {\n    console.log('Suppression du projet annulée');\n    this.showDeleteDialog = false; // Close the confirmation dialog without deleting\n  }\n\n  getFileUrl(filePath) {\n    if (!filePath) return '';\n    // Extraire uniquement le nom du fichier\n    let fileName = filePath;\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      fileName = parts[parts.length - 1];\n    }\n    // Utiliser la route spécifique pour le téléchargement\n    return `${environment.urlBackend}projets/telecharger/${fileName}`;\n  }\n  getFileName(filePath) {\n    if (!filePath) return 'Fichier';\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      return parts[parts.length - 1];\n    }\n    return filePath;\n  }\n  static {\n    this.ɵfac = function ListProjectComponent_Factory(t) {\n      return new (t || ListProjectComponent)(i0.ɵɵdirectiveInject(i1.ProjetService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MatDialog), i0.ɵɵdirectiveInject(i4.DataService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ListProjectComponent,\n      selectors: [[\"app-list-project\"]],\n      viewQuery: function ListProjectComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.confirmDialog = _t.first);\n        }\n      },\n      decls: 15,\n      vars: 4,\n      consts: [[1, \"container-fluid\", \"p-4\", \"md:p-6\", \"bg-[#edf1f4]\", \"min-h-screen\"], [1, \"flex\", \"flex-col\", \"md:flex-row\", \"md:items-center\", \"md:justify-between\", \"mb-6\"], [1, \"text-2xl\", \"font-bold\", \"text-[#4f5fad]\", \"mb-2\", \"md:mb-0\"], [1, \"text-[#6d6870]\", \"text-sm\"], [\"routerLink\", \"/admin/projects/new\", 1, \"inline-flex\", \"items-center\", \"bg-[#7826b5]\", \"hover:bg-[#5f1d8f]\", \"text-white\", \"px-4\", \"py-2\", \"rounded-lg\", \"shadow\", \"transition-all\", \"mt-4\", \"md:mt-0\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-2\"], [\"fill-rule\", \"evenodd\", \"d\", \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z\", \"clip-rule\", \"evenodd\"], [\"class\", \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\", 4, \"ngIf\"], [\"class\", \"text-center py-16\", 4, \"ngIf\"], [\"class\", \"bg-white rounded-xl shadow-md p-8 text-center mb-8\", 4, \"ngIf\"], [\"class\", \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"lg:grid-cols-3\", \"gap-6\", \"mb-8\"], [\"class\", \"bg-white rounded-xl shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 group\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-white\", \"rounded-xl\", \"shadow-md\", \"overflow-hidden\", \"hover:shadow-xl\", \"transition-all\", \"duration-300\", \"transform\", \"hover:-translate-y-1\", \"group\"], [1, \"border-t-4\", \"border-[#4f5fad]\", \"p-5\", \"relative\"], [1, \"flex\", \"justify-between\", \"items-start\"], [1, \"text-xl\", \"font-bold\", \"text-[#4f5fad]\", \"truncate\", \"pr-16\"], [1, \"absolute\", \"top-4\", \"right-4\", \"flex\", \"space-x-2\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [1, \"bg-[#edf1f4]\", \"hover:bg-[#dce4ec]\", \"p-1.5\", \"rounded-lg\", \"text-[#4f5fad]\", \"transition-colors\", 3, \"routerLink\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-5\", \"w-5\"], [\"d\", \"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z\"], [1, \"bg-[#edf1f4]\", \"hover:bg-[#dce4ec]\", \"p-1.5\", \"rounded-lg\", \"text-[#ff6b69]\", \"transition-colors\", \"cursor-pointer\", 3, \"click\"], [\"fill-rule\", \"evenodd\", \"d\", \"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm4 0a1 1 0 112 0v6a1 1 0 11-2 0V8z\", \"clip-rule\", \"evenodd\"], [1, \"flex\", \"items-center\", \"mt-3\", \"text-sm\"], [1, \"bg-[#4f5fad]/10\", \"px-2.5\", \"py-1\", \"rounded-full\", \"text-xs\", \"font-medium\", \"text-[#4f5fad]\"], [1, \"mx-2\", \"text-[#bdc6cc]\"], [1, \"text-[#6d6870]\", \"text-xs\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-3.5\", \"w-3.5\", \"inline\", \"mr-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"p-5\", \"border-t\", \"border-[#edf1f4]\"], [1, \"mb-5\"], [1, \"text-xs\", \"font-semibold\", \"text-[#6d6870]\", \"uppercase\", \"tracking-wider\", \"mb-2\", \"flex\", \"items-center\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-1\", \"text-[#4f5fad]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-[#6d6870]\", \"text-sm\", \"line-clamp-3\"], [\"class\", \"mb-5\", 4, \"ngIf\"], [1, \"mt-6\", \"flex\", \"space-x-3\"], [1, \"flex-1\", \"bg-[#edf1f4]\", \"hover:bg-[#dce4ec]\", \"text-[#4f5fad]\", \"py-2\", \"px-4\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"text-center\", \"flex\", \"items-center\", \"justify-center\", \"transition-colors\", 3, \"routerLink\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-1.5\"], [1, \"flex-1\", \"bg-[#7826b5]\", \"hover:bg-[#5f1d8f]\", \"text-white\", \"py-2\", \"px-4\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"text-center\", \"flex\", \"items-center\", \"justify-center\", \"transition-colors\", 3, \"routerLink\", \"queryParams\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"space-y-2\", \"bg-[#edf1f4]\", \"rounded-lg\", \"p-3\"], [\"class\", \"flex items-center justify-between text-sm\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"justify-between\", \"text-sm\"], [1, \"flex\", \"items-center\", \"truncate\", \"max-w-[70%]\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"text-[#7826b5]\", \"mr-2\", \"flex-shrink-0\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"], [1, \"truncate\", \"text-[#6d6870]\"], [1, \"text-[#4f5fad]\", \"hover:text-[#7826b5]\", \"flex\", \"items-center\", 3, \"href\", \"download\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"], [1, \"bg-[#edf1f4]\", \"rounded-lg\", \"p-3\", \"text-sm\", \"text-[#6d6870]\", \"flex\", \"items-center\", \"justify-center\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-1\", \"text-[#bdc6cc]\"], [1, \"text-center\", \"py-16\"], [1, \"animate-spin\", \"rounded-full\", \"h-10\", \"w-10\", \"border-t-2\", \"border-b-2\", \"border-[#4f5fad]\", \"mx-auto\"], [1, \"mt-4\", \"text-[#6d6870]\", \"font-medium\"], [1, \"bg-white\", \"rounded-xl\", \"shadow-md\", \"p-8\", \"text-center\", \"mb-8\"], [1, \"w-20\", \"h-20\", \"mx-auto\", \"mb-6\", \"bg-[#f0e6ff]\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-10\", \"w-10\", \"text-[#7826b5]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-xl\", \"font-bold\", \"text-[#4f5fad]\", \"mb-2\"], [1, \"text-[#6d6870]\", \"mb-6\", \"max-w-md\", \"mx-auto\"], [\"routerLink\", \"/admin/projects/new\", 1, \"inline-flex\", \"items-center\", \"px-4\", \"py-2\", \"bg-[#7826b5]\", \"hover:bg-[#5f1d8f]\", \"text-white\", \"rounded-lg\", \"shadow\", \"transition-all\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-2\"], [\"d\", \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z\"], [1, \"fixed\", \"inset-0\", \"z-50\", \"flex\", \"items-center\", \"justify-center\", \"bg-black\", \"bg-opacity-50\"], [1, \"bg-white\", \"rounded-xl\", \"shadow-lg\", \"w-full\", \"max-w-md\", \"p-6\"], [1, \"text-xl\", \"font-bold\", \"text-[#4f5fad]\", \"mb-4\"], [1, \"text-[#6d6870]\", \"mb-6\"], [1, \"flex\", \"justify-end\", \"space-x-4\"], [1, \"px-4\", \"py-2\", \"bg-[#edf1f4]\", \"text-[#4f5fad]\", \"hover:bg-[#dce4ec]\", \"rounded-lg\", 3, \"click\"], [1, \"px-4\", \"py-2\", \"bg-[#7826b5]\", \"hover:bg-[#5f1d8f]\", \"text-white\", \"rounded-lg\", 3, \"click\"]],\n      template: function ListProjectComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\")(3, \"h1\", 2);\n          i0.ɵɵtext(4, \" Gestion des Projets \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 3);\n          i0.ɵɵtext(6, \" Cr\\u00E9ez, g\\u00E9rez et suivez vos projets acad\\u00E9miques \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"a\", 4);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(8, \"svg\", 5);\n          i0.ɵɵelement(9, \"path\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(10, \" Ajouter un projet \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(11, ListProjectComponent_div_11_Template, 2, 1, \"div\", 7);\n          i0.ɵɵtemplate(12, ListProjectComponent_div_12_Template, 4, 0, \"div\", 8);\n          i0.ɵɵtemplate(13, ListProjectComponent_div_13_Template, 12, 0, \"div\", 9);\n          i0.ɵɵtemplate(14, ListProjectComponent_div_14_Template, 11, 2, \"div\", 10);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.projets && ctx.projets.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && (!ctx.projets || ctx.projets.length === 0));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showDeleteDialog);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i2.RouterLink, i5.DatePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJsaXN0LXByb2plY3QuY29tcG9uZW50LmNzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vcHJvamVjdHMvbGlzdC1wcm9qZWN0L2xpc3QtcHJvamVjdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSx3S0FBd0siLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "i0", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceHTML", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r8", "getFileName", "file_r9", "ɵɵproperty", "getFileUrl", "ɵɵsanitizeUrl", "ɵɵtemplate", "ListProjectComponent_div_11_div_1_div_30_div_6_Template", "ɵɵtextInterpolate1", "projet_r5", "fichiers", "length", "ɵɵlistener", "ListProjectComponent_div_11_div_1_Template_a_click_9_listener", "restoredCtx", "ɵɵrestoreView", "_r12", "$implicit", "ctx_r11", "ɵɵnextContext", "ɵɵresetView", "_id", "openDeleteDialog", "ListProjectComponent_div_11_div_1_div_30_Template", "ListProjectComponent_div_11_div_1_div_31_Template", "titre", "ɵɵpureFunction1", "_c1", "groupe", "ɵɵpipeBind2", "dateLimite", "description", "_c2", "ɵɵpureFunction0", "_c3", "_c4", "ListProjectComponent_div_11_div_1_Template", "ctx_r0", "projets", "ListProjectComponent_div_14_Template_button_click_7_listener", "_r14", "ctx_r13", "onDeleteCancel", "ListProjectComponent_div_14_Template_button_click_9_listener", "ctx_r15", "onDeleteConfirm", "ctx_r3", "dialogData", "title", "message", "ListProjectComponent", "constructor", "projetService", "router", "dialog", "authService", "isLoading", "isAdmin", "showDeleteDialog", "projectIdToDelete", "ngOnInit", "loadProjets", "checkAdminStatus", "console", "log", "getProjets", "subscribe", "next", "error", "err", "alert", "loadProjects", "editProjet", "projetId", "navigate", "viewProjetDetails", "deleteProjet", "confirm", "id", "filePath", "fileName", "includes", "parts", "split", "urlBackend", "ɵɵdirectiveInject", "i1", "ProjetService", "i2", "Router", "i3", "MatDialog", "i4", "DataService", "selectors", "viewQuery", "ListProjectComponent_Query", "rf", "ctx", "ListProjectComponent_div_11_Template", "ListProjectComponent_div_12_Template", "ListProjectComponent_div_13_Template", "ListProjectComponent_div_14_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\projects\\list-project\\list-project.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\projects\\list-project\\list-project.component.html"], "sourcesContent": ["import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { Projet } from 'src/app/models/projet.model';\nimport { MatDialog, MatDialogRef } from '@angular/material/dialog';\nimport { ProjetService } from '@app/services/projets.service';\nimport { DataService } from 'src/app/services/data.service';\nimport { environment } from 'src/environments/environment';\n\n@Component({\n  selector: 'app-list-project',\n  templateUrl: './list-project.component.html',\n  styleUrls: ['./list-project.component.css'],\n})\nexport class ListProjectComponent implements OnInit {\n  projets: Projet[] = [];\n  isLoading = true;\n  isAdmin = false;\n  showDeleteDialog = false;\n  projectIdToDelete: string | null = null;\n\n  // For the confirmation dialog\n  dialogRef?: MatDialogRef<any>;\n  dialogData = {\n    title: 'Confirmer la suppression',\n    message: 'Êtes-vous sûr de vouloir supprimer ce projet?',\n  };\n\n  @ViewChild('confirmDialog') confirmDialog!: TemplateRef<any>;\n\n  constructor(\n    private projetService: ProjetService,\n    private router: Router,\n    private dialog: MatDialog,\n    private authService: DataService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadProjets();\n    this.checkAdminStatus();\n  }\n\n  loadProjets(): void {\n    this.isLoading = true;\n    console.log('Chargement des projets...');\n    this.projetService.getProjets().subscribe({\n      next: (projets) => {\n        console.log('Projets reçus:', projets);\n        this.projets = projets;\n        this.isLoading = false;\n      },\n      error: (err) => {\n        console.error('Error loading projects', err);\n        this.isLoading = false;\n        // Afficher un message d'erreur à l'utilisateur\n        alert(\n          'Erreur lors du chargement des projets: ' +\n            (err.error?.message || err.message || 'Erreur inconnue')\n        );\n      },\n    });\n  }\n\n  // Alias pour loadProjets pour assurer la compatibilité avec les méthodes existantes\n  loadProjects(): void {\n    this.loadProjets();\n  }\n\n  checkAdminStatus(): void {\n    this.isAdmin = this.authService.isAdmin();\n    console.log('Statut admin:', this.isAdmin);\n  }\n\n  editProjet(projetId: string | undefined): void {\n    if (!projetId) {\n      console.error('ID du projet non défini');\n      return;\n    }\n    console.log('Édition du projet:', projetId);\n    this.router.navigate(['/admin/projects/edit', projetId]);\n  }\n\n  viewProjetDetails(projetId: string | undefined): void {\n    if (!projetId) {\n      console.error('ID du projet non défini');\n      return;\n    }\n    console.log('Affichage des détails du projet:', projetId);\n    this.router.navigate(['/admin/projects/detail', projetId]);\n  }\n\n  deleteProjet(projetId: string | undefined): void {\n    if (!projetId) {\n      console.error('ID du projet non défini');\n      return;\n    }\n\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {\n      console.log('Suppression du projet confirmée:', projetId);\n      this.projetService.deleteProjet(projetId).subscribe({\n        next: () => {\n          console.log('Projet supprimé avec succès');\n          alert('Projet supprimé avec succès');\n          this.loadProjets(); // Reload the projects after deletion\n        },\n        error: (err) => {\n          console.error('Error deleting project', err);\n          alert(\n            'Erreur lors de la suppression du projet: ' +\n              (err.error?.message || err.message || 'Erreur inconnue')\n          );\n        },\n      });\n    }\n  }\n\n  openDeleteDialog(id: string): void {\n    if (!id) {\n      console.error('ID du projet non défini');\n      return;\n    }\n    console.log(\n      'Ouverture de la boîte de dialogue de confirmation pour la suppression du projet:',\n      id\n    );\n    this.showDeleteDialog = true; // Show confirmation dialog\n    this.projectIdToDelete = id; // Store the ID of the project to be deleted\n  }\n\n  onDeleteConfirm(): void {\n    if (this.projectIdToDelete) {\n      console.log('Suppression du projet confirmée:', this.projectIdToDelete);\n      this.projetService.deleteProjet(this.projectIdToDelete).subscribe({\n        next: () => {\n          console.log('Projet supprimé avec succès');\n          alert('Projet supprimé avec succès');\n          this.loadProjets(); // Reload the projects after deletion\n          this.showDeleteDialog = false; // Close the confirmation dialog\n        },\n        error: (err) => {\n          console.error('Error deleting project', err);\n          alert(\n            'Erreur lors de la suppression du projet: ' +\n              (err.error?.message || err.message || 'Erreur inconnue')\n          );\n          this.showDeleteDialog = false;\n        },\n      });\n    }\n  }\n\n  onDeleteCancel(): void {\n    console.log('Suppression du projet annulée');\n    this.showDeleteDialog = false; // Close the confirmation dialog without deleting\n  }\n\n  getFileUrl(filePath: string): string {\n    if (!filePath) return '';\n\n    // Extraire uniquement le nom du fichier\n    let fileName = filePath;\n\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      fileName = parts[parts.length - 1];\n    }\n\n    // Utiliser la route spécifique pour le téléchargement\n    return `${environment.urlBackend}projets/telecharger/${fileName}`;\n  }\n\n  getFileName(filePath: string): string {\n    if (!filePath) return 'Fichier';\n\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      return parts[parts.length - 1];\n    }\n\n    return filePath;\n  }\n}\n", "<!-- Begin Page Content -->\n<div class=\"container-fluid p-4 md:p-6 bg-[#edf1f4] min-h-screen\">\n  <!-- Page Heading -->\n  <div\n    class=\"flex flex-col md:flex-row md:items-center md:justify-between mb-6\"\n  >\n    <div>\n      <h1 class=\"text-2xl font-bold text-[#4f5fad] mb-2 md:mb-0\">\n        Gestion des Projets\n      </h1>\n      <p class=\"text-[#6d6870] text-sm\">\n        <PERSON><PERSON><PERSON>, g<PERSON><PERSON> et suivez vos projets académiques\n      </p>\n    </div>\n    <a\n      routerLink=\"/admin/projects/new\"\n      class=\"inline-flex items-center bg-[#7826b5] hover:bg-[#5f1d8f] text-white px-4 py-2 rounded-lg shadow transition-all mt-4 md:mt-0\"\n    >\n      <svg\n        xmlns=\"http://www.w3.org/2000/svg\"\n        class=\"h-4 w-4 mr-2\"\n        viewBox=\"0 0 20 20\"\n        fill=\"currentColor\"\n      >\n        <path\n          fill-rule=\"evenodd\"\n          d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z\"\n          clip-rule=\"evenodd\"\n        />\n      </svg>\n      Ajouter un projet\n    </a>\n  </div>\n\n  <!-- Projects Grid -->\n  <div\n    *ngIf=\"!isLoading && projets && projets.length > 0\"\n    class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\"\n  >\n    <!-- Project Card -->\n    <div\n      *ngFor=\"let projet of projets\"\n      class=\"bg-white rounded-xl shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 group\"\n    >\n      <!-- Project Header with border -->\n      <div class=\"border-t-4 border-[#4f5fad] p-5 relative\">\n        <div class=\"flex justify-between items-start\">\n          <h3 class=\"text-xl font-bold text-[#4f5fad] truncate pr-16\">\n            {{ projet.titre }}\n          </h3>\n          <div\n            class=\"absolute top-4 right-4 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity\"\n          >\n            <!-- Edit Button -->\n            <a\n              [routerLink]=\"['/admin/projects/editProjet', projet._id]\"\n              class=\"bg-[#edf1f4] hover:bg-[#dce4ec] p-1.5 rounded-lg text-[#4f5fad] transition-colors\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                class=\"h-5 w-5\"\n                viewBox=\"0 0 20 20\"\n                fill=\"currentColor\"\n              >\n                <path\n                  d=\"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z\"\n                />\n              </svg>\n            </a>\n\n            <!-- Delete Button -->\n            <a\n              (click)=\"projet._id && openDeleteDialog(projet._id)\"\n              class=\"bg-[#edf1f4] hover:bg-[#dce4ec] p-1.5 rounded-lg text-[#ff6b69] transition-colors cursor-pointer\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                class=\"h-5 w-5\"\n                viewBox=\"0 0 20 20\"\n                fill=\"currentColor\"\n              >\n                <path\n                  fill-rule=\"evenodd\"\n                  d=\"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm4 0a1 1 0 112 0v6a1 1 0 11-2 0V8z\"\n                  clip-rule=\"evenodd\"\n                />\n              </svg>\n            </a>\n          </div>\n        </div>\n        <div class=\"flex items-center mt-3 text-sm\">\n          <span\n            class=\"bg-[#4f5fad]/10 px-2.5 py-1 rounded-full text-xs font-medium text-[#4f5fad]\"\n            >{{ projet.groupe || \"Tous\" }}</span\n          >\n          <span class=\"mx-2 text-[#bdc6cc]\">•</span>\n          <span class=\"text-[#6d6870] text-xs\">\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              class=\"h-3.5 w-3.5 inline mr-1\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n              />\n            </svg>\n            {{ projet.dateLimite | date : \"dd/MM/yyyy\" }}\n          </span>\n        </div>\n      </div>\n\n      <!-- Project Content -->\n      <div class=\"p-5 border-t border-[#edf1f4]\">\n        <!-- Description -->\n        <div class=\"mb-5\">\n          <h4\n            class=\"text-xs font-semibold text-[#6d6870] uppercase tracking-wider mb-2 flex items-center\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              class=\"h-4 w-4 mr-1 text-[#4f5fad]\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n              />\n            </svg>\n            Description\n          </h4>\n          <p class=\"text-[#6d6870] text-sm line-clamp-3\">\n            {{ projet.description || \"Aucune description fournie\" }}\n          </p>\n        </div>\n\n        <!-- Files Section -->\n        <div *ngIf=\"projet.fichiers && projet.fichiers.length > 0\" class=\"mb-5\">\n          <h4\n            class=\"text-xs font-semibold text-[#6d6870] uppercase tracking-wider mb-2 flex items-center\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              class=\"h-4 w-4 mr-1 text-[#4f5fad]\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n              />\n            </svg>\n            Fichiers ({{ projet.fichiers.length }})\n          </h4>\n          <div class=\"space-y-2 bg-[#edf1f4] rounded-lg p-3\">\n            <div\n              *ngFor=\"let file of projet.fichiers\"\n              class=\"flex items-center justify-between text-sm\"\n            >\n              <div class=\"flex items-center truncate max-w-[70%]\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  class=\"h-4 w-4 text-[#7826b5] mr-2 flex-shrink-0\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    stroke-width=\"2\"\n                    d=\"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"\n                  />\n                </svg>\n                <span class=\"truncate text-[#6d6870]\">{{\n                  getFileName(file)\n                }}</span>\n              </div>\n              <a\n                [href]=\"getFileUrl(file)\"\n                [download]=\"getFileName(file)\"\n                class=\"text-[#4f5fad] hover:text-[#7826b5] flex items-center\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  class=\"h-4 w-4 mr-1\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    stroke-width=\"2\"\n                    d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"\n                  />\n                </svg>\n                Télécharger\n              </a>\n            </div>\n          </div>\n        </div>\n\n        <!-- No Files Message -->\n        <div\n          *ngIf=\"!projet.fichiers || projet.fichiers.length === 0\"\n          class=\"mb-5\"\n        >\n          <h4\n            class=\"text-xs font-semibold text-[#6d6870] uppercase tracking-wider mb-2 flex items-center\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              class=\"h-4 w-4 mr-1 text-[#4f5fad]\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n              />\n            </svg>\n            Fichiers\n          </h4>\n          <div\n            class=\"bg-[#edf1f4] rounded-lg p-3 text-sm text-[#6d6870] flex items-center justify-center\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              class=\"h-4 w-4 mr-1 text-[#bdc6cc]\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n              />\n            </svg>\n            Aucun fichier joint\n          </div>\n        </div>\n\n        <!-- Actions -->\n        <div class=\"mt-6 flex space-x-3\">\n          <a\n            [routerLink]=\"['/admin/projects/details', projet._id]\"\n            class=\"flex-1 bg-[#edf1f4] hover:bg-[#dce4ec] text-[#4f5fad] py-2 px-4 rounded-lg text-sm font-medium text-center flex items-center justify-center transition-colors\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              class=\"h-4 w-4 mr-1.5\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n              />\n            </svg>\n            Détails\n          </a>\n          <a\n            [routerLink]=\"['/admin/projects/rendus']\"\n            [queryParams]=\"{ projetId: projet._id }\"\n            class=\"flex-1 bg-[#7826b5] hover:bg-[#5f1d8f] text-white py-2 px-4 rounded-lg text-sm font-medium text-center flex items-center justify-center transition-colors\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              class=\"h-4 w-4 mr-1.5\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n              />\n            </svg>\n            Rendus\n          </a>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"text-center py-16\">\n    <div\n      class=\"animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#4f5fad] mx-auto\"\n    ></div>\n    <p class=\"mt-4 text-[#6d6870] font-medium\">Chargement des projets...</p>\n  </div>\n\n  <!-- Empty State -->\n  <div\n    *ngIf=\"!isLoading && (!projets || projets.length === 0)\"\n    class=\"bg-white rounded-xl shadow-md p-8 text-center mb-8\"\n  >\n    <div\n      class=\"w-20 h-20 mx-auto mb-6 bg-[#f0e6ff] rounded-full flex items-center justify-center\"\n    >\n      <svg\n        xmlns=\"http://www.w3.org/2000/svg\"\n        class=\"h-10 w-10 text-[#7826b5]\"\n        fill=\"none\"\n        viewBox=\"0 0 24 24\"\n        stroke=\"currentColor\"\n      >\n        <path\n          stroke-linecap=\"round\"\n          stroke-linejoin=\"round\"\n          stroke-width=\"1.5\"\n          d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n        />\n      </svg>\n    </div>\n    <h3 class=\"text-xl font-bold text-[#4f5fad] mb-2\">\n      Aucun projet disponible\n    </h3>\n    <p class=\"text-[#6d6870] mb-6 max-w-md mx-auto\">\n      Commencez par créer votre premier projet en cliquant sur le bouton\n      ci-dessous\n    </p>\n    <a\n      routerLink=\"/admin/projects/new\"\n      class=\"inline-flex items-center px-4 py-2 bg-[#7826b5] hover:bg-[#5f1d8f] text-white rounded-lg shadow transition-all\"\n    >\n      <svg\n        xmlns=\"http://www.w3.org/2000/svg\"\n        class=\"h-5 w-5 mr-2\"\n        viewBox=\"0 0 20 20\"\n        fill=\"currentColor\"\n      >\n        <path\n          d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z\"\n        />\n      </svg>\n      Ajouter un projet\n    </a>\n  </div>\n\n  <!-- Confirmation Dialog -->\n  <div\n    *ngIf=\"showDeleteDialog\"\n    class=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50\"\n  >\n    <div class=\"bg-white rounded-xl shadow-lg w-full max-w-md p-6\">\n      <h2 class=\"text-xl font-bold text-[#4f5fad] mb-4\">\n        {{ dialogData.title }}\n      </h2>\n      <p class=\"text-[#6d6870] mb-6\">{{ dialogData.message }}</p>\n      <div class=\"flex justify-end space-x-4\">\n        <button\n          class=\"px-4 py-2 bg-[#edf1f4] text-[#4f5fad] hover:bg-[#dce4ec] rounded-lg\"\n          (click)=\"onDeleteCancel()\"\n        >\n          Annuler\n        </button>\n        <button\n          class=\"px-4 py-2 bg-[#7826b5] hover:bg-[#5f1d8f] text-white rounded-lg\"\n          (click)=\"onDeleteConfirm()\"\n        >\n          Supprimer\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAMA,SAASA,WAAW,QAAQ,8BAA8B;;;;;;;;;;ICgK9CC,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAE,cAAA,EAMC;IANDF,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAG,SAAA,eAKE;IACJH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,eAAA,EAAsC;IAAtCL,EAAA,CAAAC,cAAA,eAAsC;IAAAD,EAAA,CAAAM,MAAA,GAEpC;IAAAN,EAAA,CAAAI,YAAA,EAAO;IAEXJ,EAAA,CAAAC,cAAA,YAIC;IACCD,EAAA,CAAAE,cAAA,EAMC;IANDF,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAG,SAAA,eAKE;IACJH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,8BACF;IAA<PERSON>,EAAA,CAAAI,YAAA,EAAI;;;;;IAxBoCJ,EAAA,CAAAO,SAAA,GAEpC;IAFoCP,EAAA,CAAAQ,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAC,OAAA,EAEpC;IAGFX,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAY,UAAA,SAAAH,MAAA,CAAAI,UAAA,CAAAF,OAAA,GAAAX,EAAA,CAAAc,aAAA,CAAyB,aAAAL,MAAA,CAAAC,WAAA,CAAAC,OAAA;;;;;IA7CjCX,EAAA,CAAAC,cAAA,cAAwE;IAIpED,EAAA,CAAAE,cAAA,EAMC;IANDF,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAG,SAAA,eAKE;IACJH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAK,eAAA,EAAmD;IAAnDL,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAe,UAAA,IAAAC,uDAAA,mBA4CM;IACRhB,EAAA,CAAAI,YAAA,EAAM;;;;IAhDJJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAiB,kBAAA,gBAAAC,SAAA,CAAAC,QAAA,CAAAC,MAAA,OACF;IAGqBpB,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAY,UAAA,YAAAM,SAAA,CAAAC,QAAA,CAAkB;;;;;IAgDzCnB,EAAA,CAAAC,cAAA,cAGC;IAIGD,EAAA,CAAAE,cAAA,EAMC;IANDF,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAG,SAAA,eAKE;IACJH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,iBACF;IAAAN,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAK,eAAA,EAEC;IAFDL,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAE,cAAA,EAMC;IANDF,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAG,SAAA,eAKE;IACJH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,4BACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;;;;;;;;;;;;;;;;;IAxNZJ,EAAA,CAAAC,cAAA,cAGC;IAKOD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,cAEC;IAMGD,EAAA,CAAAE,cAAA,EAKC;IALDF,EAAA,CAAAC,cAAA,cAKC;IACCD,EAAA,CAAAG,SAAA,eAEE;IACJH,EAAA,CAAAI,YAAA,EAAM;IAIRJ,EAAA,CAAAK,eAAA,EAGC;IAHDL,EAAA,CAAAC,cAAA,YAGC;IAFCD,EAAA,CAAAqB,UAAA,mBAAAC,8DAAA;MAAA,MAAAC,WAAA,GAAAvB,EAAA,CAAAwB,aAAA,CAAAC,IAAA;MAAA,MAAAP,SAAA,GAAAK,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA3B,EAAA,CAAA4B,aAAA;MAAA,OAAS5B,EAAA,CAAA6B,WAAA,CAAAX,SAAA,CAAAY,GAAA,IAAcH,OAAA,CAAAI,gBAAA,CAAAb,SAAA,CAAAY,GAAA,CAA4B;IAAA,EAAC;IAGpD9B,EAAA,CAAAE,cAAA,EAKC;IALDF,EAAA,CAAAC,cAAA,eAKC;IACCD,EAAA,CAAAG,SAAA,gBAIE;IACJH,EAAA,CAAAI,YAAA,EAAM;IAIZJ,EAAA,CAAAK,eAAA,EAA4C;IAA5CL,EAAA,CAAAC,cAAA,eAA4C;IAGvCD,EAAA,CAAAM,MAAA,IAA6B;IAAAN,EAAA,CAAAI,YAAA,EAC/B;IACDJ,EAAA,CAAAC,cAAA,gBAAkC;IAAAD,EAAA,CAAAM,MAAA,cAAC;IAAAN,EAAA,CAAAI,YAAA,EAAO;IAC1CJ,EAAA,CAAAC,cAAA,gBAAqC;IACnCD,EAAA,CAAAE,cAAA,EAMC;IANDF,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAG,SAAA,gBAKE;IACJH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,IACF;;IAAAN,EAAA,CAAAI,YAAA,EAAO;IAKXJ,EAAA,CAAAK,eAAA,EAA2C;IAA3CL,EAAA,CAAAC,cAAA,eAA2C;IAMrCD,EAAA,CAAAE,cAAA,EAMC;IANDF,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAG,SAAA,gBAKE;IACJH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,qBACF;IAAAN,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAK,eAAA,EAA+C;IAA/CL,EAAA,CAAAC,cAAA,aAA+C;IAC7CD,EAAA,CAAAM,MAAA,IACF;IAAAN,EAAA,CAAAI,YAAA,EAAI;IAINJ,EAAA,CAAAe,UAAA,KAAAiB,iDAAA,kBAmEM;IAGNhC,EAAA,CAAAe,UAAA,KAAAkB,iDAAA,kBA0CM;IAGNjC,EAAA,CAAAC,cAAA,eAAiC;IAK7BD,EAAA,CAAAE,cAAA,EAMC;IANDF,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAG,SAAA,gBAKE;IACJH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,sBACF;IAAAN,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAK,eAAA,EAIC;IAJDL,EAAA,CAAAC,cAAA,aAIC;IACCD,EAAA,CAAAE,cAAA,EAMC;IANDF,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAG,SAAA,gBAKE;IACJH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,gBACF;IAAAN,EAAA,CAAAI,YAAA,EAAI;;;;IA7PFJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAiB,kBAAA,MAAAC,SAAA,CAAAgB,KAAA,MACF;IAMIlC,EAAA,CAAAO,SAAA,GAAyD;IAAzDP,EAAA,CAAAY,UAAA,eAAAZ,EAAA,CAAAmC,eAAA,KAAAC,GAAA,EAAAlB,SAAA,CAAAY,GAAA,EAAyD;IAsC1D9B,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAQ,iBAAA,CAAAU,SAAA,CAAAmB,MAAA,WAA6B;IAkB9BrC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAiB,kBAAA,MAAAjB,EAAA,CAAAsC,WAAA,SAAApB,SAAA,CAAAqB,UAAA,qBACF;IA4BEvC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAiB,kBAAA,MAAAC,SAAA,CAAAsB,WAAA,sCACF;IAIIxC,EAAA,CAAAO,SAAA,GAAmD;IAAnDP,EAAA,CAAAY,UAAA,SAAAM,SAAA,CAAAC,QAAA,IAAAD,SAAA,CAAAC,QAAA,CAAAC,MAAA,KAAmD;IAuEtDpB,EAAA,CAAAO,SAAA,GAAsD;IAAtDP,EAAA,CAAAY,UAAA,UAAAM,SAAA,CAAAC,QAAA,IAAAD,SAAA,CAAAC,QAAA,CAAAC,MAAA,OAAsD;IA8CrDpB,EAAA,CAAAO,SAAA,GAAsD;IAAtDP,EAAA,CAAAY,UAAA,eAAAZ,EAAA,CAAAmC,eAAA,KAAAM,GAAA,EAAAvB,SAAA,CAAAY,GAAA,EAAsD;IAoBtD9B,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAY,UAAA,eAAAZ,EAAA,CAAA0C,eAAA,KAAAC,GAAA,EAAyC,gBAAA3C,EAAA,CAAAmC,eAAA,KAAAS,GAAA,EAAA1B,SAAA,CAAAY,GAAA;;;;;;IAvPnD9B,EAAA,CAAAK,eAAA,EAGC;IAHDL,EAAA,CAAAC,cAAA,cAGC;IAECD,EAAA,CAAAe,UAAA,IAAA8B,0CAAA,oBAwQM;IACR7C,EAAA,CAAAI,YAAA,EAAM;;;;IAxQiBJ,EAAA,CAAAO,SAAA,GAAU;IAAVP,EAAA,CAAAY,UAAA,YAAAkC,MAAA,CAAAC,OAAA,CAAU;;;;;;IA2QjC/C,EAAA,CAAAK,eAAA,EAAiD;IAAjDL,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAG,SAAA,cAEO;IACPH,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAM,MAAA,gCAAyB;IAAAN,EAAA,CAAAI,YAAA,EAAI;;;;;;IAI1EJ,EAAA,CAAAK,eAAA,EAGC;IAHDL,EAAA,CAAAC,cAAA,cAGC;IAIGD,EAAA,CAAAE,cAAA,EAMC;IANDF,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAG,SAAA,eAKE;IACJH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAK,eAAA,EAAkD;IAAlDL,EAAA,CAAAC,cAAA,aAAkD;IAChDD,EAAA,CAAAM,MAAA,gCACF;IAAAN,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,YAAgD;IAC9CD,EAAA,CAAAM,MAAA,2FAEF;IAAAN,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAC,cAAA,YAGC;IACCD,EAAA,CAAAE,cAAA,EAKC;IALDF,EAAA,CAAAC,cAAA,cAKC;IACCD,EAAA,CAAAG,SAAA,gBAEE;IACJH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,2BACF;IAAAN,EAAA,CAAAI,YAAA,EAAI;;;;;;;IAINJ,EAAA,CAAAK,eAAA,EAGC;IAHDL,EAAA,CAAAC,cAAA,cAGC;IAGKD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,YAA+B;IAAAD,EAAA,CAAAM,MAAA,GAAwB;IAAAN,EAAA,CAAAI,YAAA,EAAI;IAC3DJ,EAAA,CAAAC,cAAA,cAAwC;IAGpCD,EAAA,CAAAqB,UAAA,mBAAA2B,6DAAA;MAAAhD,EAAA,CAAAwB,aAAA,CAAAyB,IAAA;MAAA,MAAAC,OAAA,GAAAlD,EAAA,CAAA4B,aAAA;MAAA,OAAS5B,EAAA,CAAA6B,WAAA,CAAAqB,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAE1BnD,EAAA,CAAAM,MAAA,gBACF;IAAAN,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,iBAGC;IADCD,EAAA,CAAAqB,UAAA,mBAAA+B,6DAAA;MAAApD,EAAA,CAAAwB,aAAA,CAAAyB,IAAA;MAAA,MAAAI,OAAA,GAAArD,EAAA,CAAA4B,aAAA;MAAA,OAAS5B,EAAA,CAAA6B,WAAA,CAAAwB,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAE3BtD,EAAA,CAAAM,MAAA,mBACF;IAAAN,EAAA,CAAAI,YAAA,EAAS;;;;IAfTJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAiB,kBAAA,MAAAsC,MAAA,CAAAC,UAAA,CAAAC,KAAA,MACF;IAC+BzD,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAQ,iBAAA,CAAA+C,MAAA,CAAAC,UAAA,CAAAE,OAAA,CAAwB;;;ADvW7D,OAAM,MAAOC,oBAAoB;EAgB/BC,YACUC,aAA4B,EAC5BC,MAAc,EACdC,MAAiB,EACjBC,WAAwB;IAHxB,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IAnBrB,KAAAjB,OAAO,GAAa,EAAE;IACtB,KAAAkB,SAAS,GAAG,IAAI;IAChB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,iBAAiB,GAAkB,IAAI;IAIvC,KAAAZ,UAAU,GAAG;MACXC,KAAK,EAAE,0BAA0B;MACjCC,OAAO,EAAE;KACV;EASE;EAEHW,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAD,WAAWA,CAAA;IACT,IAAI,CAACL,SAAS,GAAG,IAAI;IACrBO,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;IACxC,IAAI,CAACZ,aAAa,CAACa,UAAU,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAG7B,OAAO,IAAI;QAChByB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE1B,OAAO,CAAC;QACtC,IAAI,CAACA,OAAO,GAAGA,OAAO;QACtB,IAAI,CAACkB,SAAS,GAAG,KAAK;MACxB,CAAC;MACDY,KAAK,EAAGC,GAAG,IAAI;QACbN,OAAO,CAACK,KAAK,CAAC,wBAAwB,EAAEC,GAAG,CAAC;QAC5C,IAAI,CAACb,SAAS,GAAG,KAAK;QACtB;QACAc,KAAK,CACH,yCAAyC,IACtCD,GAAG,CAACD,KAAK,EAAEnB,OAAO,IAAIoB,GAAG,CAACpB,OAAO,IAAI,iBAAiB,CAAC,CAC3D;MACH;KACD,CAAC;EACJ;EAEA;EACAsB,YAAYA,CAAA;IACV,IAAI,CAACV,WAAW,EAAE;EACpB;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAACL,OAAO,GAAG,IAAI,CAACF,WAAW,CAACE,OAAO,EAAE;IACzCM,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACP,OAAO,CAAC;EAC5C;EAEAe,UAAUA,CAACC,QAA4B;IACrC,IAAI,CAACA,QAAQ,EAAE;MACbV,OAAO,CAACK,KAAK,CAAC,yBAAyB,CAAC;MACxC;;IAEFL,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAES,QAAQ,CAAC;IAC3C,IAAI,CAACpB,MAAM,CAACqB,QAAQ,CAAC,CAAC,sBAAsB,EAAED,QAAQ,CAAC,CAAC;EAC1D;EAEAE,iBAAiBA,CAACF,QAA4B;IAC5C,IAAI,CAACA,QAAQ,EAAE;MACbV,OAAO,CAACK,KAAK,CAAC,yBAAyB,CAAC;MACxC;;IAEFL,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAES,QAAQ,CAAC;IACzD,IAAI,CAACpB,MAAM,CAACqB,QAAQ,CAAC,CAAC,wBAAwB,EAAED,QAAQ,CAAC,CAAC;EAC5D;EAEAG,YAAYA,CAACH,QAA4B;IACvC,IAAI,CAACA,QAAQ,EAAE;MACbV,OAAO,CAACK,KAAK,CAAC,yBAAyB,CAAC;MACxC;;IAGF,IAAIS,OAAO,CAAC,gDAAgD,CAAC,EAAE;MAC7Dd,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAES,QAAQ,CAAC;MACzD,IAAI,CAACrB,aAAa,CAACwB,YAAY,CAACH,QAAQ,CAAC,CAACP,SAAS,CAAC;QAClDC,IAAI,EAAEA,CAAA,KAAK;UACTJ,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;UAC1CM,KAAK,CAAC,6BAA6B,CAAC;UACpC,IAAI,CAACT,WAAW,EAAE,CAAC,CAAC;QACtB,CAAC;;QACDO,KAAK,EAAGC,GAAG,IAAI;UACbN,OAAO,CAACK,KAAK,CAAC,wBAAwB,EAAEC,GAAG,CAAC;UAC5CC,KAAK,CACH,2CAA2C,IACxCD,GAAG,CAACD,KAAK,EAAEnB,OAAO,IAAIoB,GAAG,CAACpB,OAAO,IAAI,iBAAiB,CAAC,CAC3D;QACH;OACD,CAAC;;EAEN;EAEA3B,gBAAgBA,CAACwD,EAAU;IACzB,IAAI,CAACA,EAAE,EAAE;MACPf,OAAO,CAACK,KAAK,CAAC,yBAAyB,CAAC;MACxC;;IAEFL,OAAO,CAACC,GAAG,CACT,kFAAkF,EAClFc,EAAE,CACH;IACD,IAAI,CAACpB,gBAAgB,GAAG,IAAI,CAAC,CAAC;IAC9B,IAAI,CAACC,iBAAiB,GAAGmB,EAAE,CAAC,CAAC;EAC/B;;EAEAjC,eAAeA,CAAA;IACb,IAAI,IAAI,CAACc,iBAAiB,EAAE;MAC1BI,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAACL,iBAAiB,CAAC;MACvE,IAAI,CAACP,aAAa,CAACwB,YAAY,CAAC,IAAI,CAACjB,iBAAiB,CAAC,CAACO,SAAS,CAAC;QAChEC,IAAI,EAAEA,CAAA,KAAK;UACTJ,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;UAC1CM,KAAK,CAAC,6BAA6B,CAAC;UACpC,IAAI,CAACT,WAAW,EAAE,CAAC,CAAC;UACpB,IAAI,CAACH,gBAAgB,GAAG,KAAK,CAAC,CAAC;QACjC,CAAC;;QACDU,KAAK,EAAGC,GAAG,IAAI;UACbN,OAAO,CAACK,KAAK,CAAC,wBAAwB,EAAEC,GAAG,CAAC;UAC5CC,KAAK,CACH,2CAA2C,IACxCD,GAAG,CAACD,KAAK,EAAEnB,OAAO,IAAIoB,GAAG,CAACpB,OAAO,IAAI,iBAAiB,CAAC,CAC3D;UACD,IAAI,CAACS,gBAAgB,GAAG,KAAK;QAC/B;OACD,CAAC;;EAEN;EAEAhB,cAAcA,CAAA;IACZqB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5C,IAAI,CAACN,gBAAgB,GAAG,KAAK,CAAC,CAAC;EACjC;;EAEAtD,UAAUA,CAAC2E,QAAgB;IACzB,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB;IACA,IAAIC,QAAQ,GAAGD,QAAQ;IAEvB;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtCH,QAAQ,GAAGE,KAAK,CAACA,KAAK,CAACvE,MAAM,GAAG,CAAC,CAAC;;IAGpC;IACA,OAAO,GAAGrB,WAAW,CAAC8F,UAAU,uBAAuBJ,QAAQ,EAAE;EACnE;EAEA/E,WAAWA,CAAC8E,QAAgB;IAC1B,IAAI,CAACA,QAAQ,EAAE,OAAO,SAAS;IAE/B;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtC,OAAOD,KAAK,CAACA,KAAK,CAACvE,MAAM,GAAG,CAAC,CAAC;;IAGhC,OAAOoE,QAAQ;EACjB;;;uBAxKW7B,oBAAoB,EAAA3D,EAAA,CAAA8F,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAhG,EAAA,CAAA8F,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAlG,EAAA,CAAA8F,iBAAA,CAAAK,EAAA,CAAAC,SAAA,GAAApG,EAAA,CAAA8F,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAApB3C,oBAAoB;MAAA4C,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;UCZjC1G,EAAA,CAAAC,cAAA,aAAkE;UAO1DD,EAAA,CAAAM,MAAA,4BACF;UAAAN,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,WAAkC;UAChCD,EAAA,CAAAM,MAAA,sEACF;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAENJ,EAAA,CAAAC,cAAA,WAGC;UACCD,EAAA,CAAAE,cAAA,EAKC;UALDF,EAAA,CAAAC,cAAA,aAKC;UACCD,EAAA,CAAAG,SAAA,cAIE;UACJH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAM,MAAA,2BACF;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAINJ,EAAA,CAAAe,UAAA,KAAA6F,oCAAA,iBA8QM;UAGN5G,EAAA,CAAAe,UAAA,KAAA8F,oCAAA,iBAKM;UAGN7G,EAAA,CAAAe,UAAA,KAAA+F,oCAAA,kBA6CM;UAGN9G,EAAA,CAAAe,UAAA,KAAAgG,oCAAA,mBAwBM;UACR/G,EAAA,CAAAI,YAAA,EAAM;;;UAjWDJ,EAAA,CAAAO,SAAA,IAAiD;UAAjDP,EAAA,CAAAY,UAAA,UAAA+F,GAAA,CAAA1C,SAAA,IAAA0C,GAAA,CAAA5D,OAAA,IAAA4D,GAAA,CAAA5D,OAAA,CAAA3B,MAAA,KAAiD;UAgR9CpB,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAY,UAAA,SAAA+F,GAAA,CAAA1C,SAAA,CAAe;UASlBjE,EAAA,CAAAO,SAAA,GAAsD;UAAtDP,EAAA,CAAAY,UAAA,UAAA+F,GAAA,CAAA1C,SAAA,MAAA0C,GAAA,CAAA5D,OAAA,IAAA4D,GAAA,CAAA5D,OAAA,CAAA3B,MAAA,QAAsD;UAgDtDpB,EAAA,CAAAO,SAAA,GAAsB;UAAtBP,EAAA,CAAAY,UAAA,SAAA+F,GAAA,CAAAxC,gBAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}