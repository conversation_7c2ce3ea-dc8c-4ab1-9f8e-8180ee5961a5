{"ast": null, "code": "import { catchError, finalize, takeUntil } from 'rxjs/operators';\nimport { Subject, of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../services/rendus.service\";\nimport * as i2 from \"../../../../services/evaluation.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction EvaluationsListComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EvaluationsListComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"div\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_4_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.loadEvaluations());\n    });\n    i0.ɵɵtext(5, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction EvaluationsListComponent_div_5_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const groupe_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", groupe_r10);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(groupe_r10);\n  }\n}\nfunction EvaluationsListComponent_div_5_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const projet_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", projet_r11._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(projet_r11.titre);\n  }\n}\nfunction EvaluationsListComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14)(2, \"div\")(3, \"label\", 15);\n    i0.ɵɵtext(4, \"Recherche\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"input\", 16);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationsListComponent_div_5_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.searchTerm = $event);\n    })(\"input\", function EvaluationsListComponent_div_5_Template_input_input_5_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.onSearchChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\")(7, \"label\", 17);\n    i0.ɵɵtext(8, \"Groupe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"select\", 18);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationsListComponent_div_5_Template_select_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.filterGroupe = $event);\n    })(\"change\", function EvaluationsListComponent_div_5_Template_select_change_9_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.applyFilters());\n    });\n    i0.ɵɵelementStart(10, \"option\", 19);\n    i0.ɵɵtext(11, \"Tous les groupes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, EvaluationsListComponent_div_5_option_12_Template, 2, 2, \"option\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\")(14, \"label\", 21);\n    i0.ɵɵtext(15, \"Projet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"select\", 22);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationsListComponent_div_5_Template_select_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.filterProjet = $event);\n    })(\"change\", function EvaluationsListComponent_div_5_Template_select_change_16_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.applyFilters());\n    });\n    i0.ɵɵelementStart(17, \"option\", 19);\n    i0.ɵɵtext(18, \"Tous les projets\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, EvaluationsListComponent_div_5_option_19_Template, 2, 2, \"option\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 23)(21, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_5_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.resetFilters());\n    });\n    i0.ɵɵtext(22, \" R\\u00E9initialiser les filtres \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.searchTerm);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.filterGroupe);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.groupes);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.filterProjet);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.projets);\n  }\n}\nfunction EvaluationsListComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_6_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.updateMissingGroups());\n    });\n    i0.ɵɵtext(2, \" Mettre \\u00E0 jour les groupes manquants \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EvaluationsListComponent_div_7_tr_17_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const evaluation_r23 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" ID: \", evaluation_r23.projet || \"Non disponible\", \" \");\n  }\n}\nfunction EvaluationsListComponent_div_7_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 34)(2, \"div\", 35)(3, \"div\")(4, \"div\", 36);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 37);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(8, \"td\", 34)(9, \"div\", 38);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"td\", 34)(12, \"div\", 38);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, EvaluationsListComponent_div_7_tr_17_div_14_Template, 2, 1, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 34)(16, \"div\", 38);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"td\", 34)(19, \"span\", 40);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"td\", 41)(22, \"div\", 42)(23, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_7_tr_17_Template_button_click_23_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const evaluation_r23 = restoredCtx.$implicit;\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.viewEvaluationDetails(evaluation_r23.rendu));\n    });\n    i0.ɵɵtext(24, \" Voir \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_7_tr_17_Template_button_click_25_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const evaluation_r23 = restoredCtx.$implicit;\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.editEvaluation(evaluation_r23.rendu));\n    });\n    i0.ɵɵtext(26, \" Modifier \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const evaluation_r23 = ctx.$implicit;\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\" \", (evaluation_r23.etudiant == null ? null : evaluation_r23.etudiant.nom) || \"N/A\", \" \", (evaluation_r23.etudiant == null ? null : evaluation_r23.etudiant.prenom) || \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (evaluation_r23.etudiant == null ? null : evaluation_r23.etudiant.email) || \"Email non disponible\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((evaluation_r23.etudiant == null ? null : evaluation_r23.etudiant.groupe) || \"Non sp\\u00E9cifi\\u00E9\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (evaluation_r23.projetDetails == null ? null : evaluation_r23.projetDetails.titre) || (evaluation_r23.renduDetails == null ? null : evaluation_r23.renduDetails.projet == null ? null : evaluation_r23.renduDetails.projet.titre) || \"Projet inconnu\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(evaluation_r23.projetDetails == null ? null : evaluation_r23.projetDetails.titre) && !(evaluation_r23.renduDetails == null ? null : evaluation_r23.renduDetails.projet == null ? null : evaluation_r23.renduDetails.projet.titre));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r22.formatDate(evaluation_r23.dateEvaluation));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r22.getScoreClass(ctx_r22.getScoreTotal(evaluation_r23)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r22.getScoreTotal(evaluation_r23), \"/20 \");\n  }\n}\nfunction EvaluationsListComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"table\", 29)(2, \"thead\", 30)(3, \"tr\")(4, \"th\", 31);\n    i0.ɵɵtext(5, \" \\u00C9tudiant \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 31);\n    i0.ɵɵtext(7, \" Groupe \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 31);\n    i0.ɵɵtext(9, \" Projet \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 31);\n    i0.ɵɵtext(11, \" Date d'\\u00E9valuation \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 31);\n    i0.ɵɵtext(13, \" Score \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 31);\n    i0.ɵɵtext(15, \" Actions \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"tbody\", 32);\n    i0.ɵɵtemplate(17, EvaluationsListComponent_div_7_tr_17_Template, 27, 9, \"tr\", 33);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.filteredEvaluations);\n  }\n}\nfunction EvaluationsListComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"p\", 47);\n    i0.ɵɵtext(2, \"Aucune \\u00E9valuation trouv\\u00E9e\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class EvaluationsListComponent {\n  constructor(rendusService, evaluationService, router) {\n    this.rendusService = rendusService;\n    this.evaluationService = evaluationService;\n    this.router = router;\n    this.evaluations = [];\n    this.filteredEvaluations = [];\n    this.isLoading = true;\n    this.error = '';\n    this.searchTerm = '';\n    this.filterGroupe = '';\n    this.filterProjet = '';\n    this.groupes = [];\n    this.projets = [];\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.loadEvaluations();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  loadEvaluations() {\n    this.isLoading = true;\n    this.error = '';\n    this.evaluationService.getAllEvaluations().pipe(takeUntil(this.destroy$), catchError(err => {\n      this.error = 'Impossible de charger les évaluations. Veuillez réessayer plus tard.';\n      this.isLoading = false;\n      return of([]);\n    }), finalize(() => {\n      this.isLoading = false;\n    })).subscribe({\n      next: evaluations => {\n        if (!Array.isArray(evaluations)) {\n          this.error = 'Format de données incorrect. Veuillez réessayer plus tard.';\n          return;\n        }\n        this.evaluations = evaluations.map(evaluation => {\n          const evalWithDetails = evaluation;\n          if (!evalWithDetails.projetDetails || !evalWithDetails.projetDetails.titre) {\n            if (evalWithDetails.renduDetails && evalWithDetails.renduDetails.projet) {\n              evalWithDetails.projetDetails = evalWithDetails.renduDetails.projet;\n            }\n          }\n          return evalWithDetails;\n        });\n        this.extractGroupesAndProjets();\n        this.applyFilters();\n      }\n    });\n  }\n  extractGroupesAndProjets() {\n    // Extraire les groupes uniques\n    const groupesSet = new Set();\n    this.evaluations.forEach(evaluation => {\n      const groupe = evaluation.etudiant?.groupe;\n      if (groupe && groupe.trim() !== '') {\n        groupesSet.add(groupe);\n        console.log(`Groupe trouvé: ${groupe}`);\n      } else {\n        console.log(`Évaluation sans groupe: ${evaluation._id}`);\n      }\n    });\n    this.groupes = Array.from(groupesSet).sort();\n    console.log('Groupes extraits:', this.groupes);\n    // Extraire les projets uniques\n    const projetsMap = new Map();\n    this.evaluations.forEach(evaluation => {\n      if (evaluation.projetDetails && evaluation.projetDetails._id) {\n        projetsMap.set(evaluation.projetDetails._id, evaluation.projetDetails);\n      }\n    });\n    this.projets = Array.from(projetsMap.values());\n  }\n  applyFilters() {\n    let results = this.evaluations;\n    // Filtre par terme de recherche\n    if (this.searchTerm.trim() !== '') {\n      const term = this.searchTerm.toLowerCase().trim();\n      results = results.filter(evaluation => evaluation.etudiant?.nom?.toLowerCase().includes(term) || evaluation.etudiant?.prenom?.toLowerCase().includes(term) || evaluation.projetDetails?.titre?.toLowerCase().includes(term));\n    }\n    // Filtre par groupe\n    if (this.filterGroupe) {\n      results = results.filter(evaluation => evaluation.etudiant?.groupe === this.filterGroupe);\n    }\n    // Filtre par projet\n    if (this.filterProjet) {\n      results = results.filter(evaluation => evaluation.projetDetails?._id === this.filterProjet);\n    }\n    this.filteredEvaluations = results;\n  }\n  onSearchChange() {\n    this.applyFilters();\n  }\n  resetFilters() {\n    this.searchTerm = '';\n    this.filterGroupe = '';\n    this.filterProjet = '';\n    this.applyFilters();\n  }\n  editEvaluation(renduId) {\n    this.router.navigate(['/admin/projects/edit-evaluation', renduId]);\n  }\n  viewEvaluationDetails(renduId) {\n    this.router.navigate(['/admin/projects/evaluation-details', renduId]);\n  }\n  getScoreTotal(evaluation) {\n    if (!evaluation.scores) return 0;\n    const scores = evaluation.scores;\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\n  }\n  getScoreClass(score) {\n    if (score >= 16) return 'text-green-600 bg-green-100';\n    if (score >= 12) return 'text-blue-600 bg-blue-100';\n    if (score >= 8) return 'text-yellow-600 bg-yellow-100';\n    return 'text-red-600 bg-red-100';\n  }\n  formatDate(date) {\n    if (!date) return 'Non disponible';\n    return new Date(date).toLocaleDateString();\n  }\n  // Ajouter cette fonction pour déboguer les données d'étudiant\n  debugEtudiantData() {\n    console.group('Débogage données étudiants');\n    this.evaluations.forEach(evaluation => {\n      console.log(`Évaluation ${evaluation._id}:`);\n      console.log('- Étudiant:', evaluation.etudiant);\n      if (evaluation.etudiant) {\n        console.log('- Nom:', evaluation.etudiant.nom);\n        console.log('- Prénom:', evaluation.etudiant.prenom);\n        console.log('- Groupe:', evaluation.etudiant.groupe);\n      }\n      console.log('- Rendu:', evaluation.renduDetails);\n    });\n    console.groupEnd();\n  }\n  // Ajouter cette méthode pour mettre à jour les groupes manquants\n  updateMissingGroups() {\n    if (!confirm('Voulez-vous mettre à jour les groupes manquants des étudiants?')) {\n      return;\n    }\n    this.isLoading = true;\n    this.evaluationService.updateMissingGroups().subscribe({\n      next: response => {\n        console.log('Mise à jour des groupes:', response);\n        alert(`${response.updatedCount} étudiants mis à jour avec leur groupe.`);\n        this.loadEvaluations(); // Recharger les données\n      },\n\n      error: err => {\n        console.error('Erreur lors de la mise à jour des groupes:', err);\n        alert('Erreur lors de la mise à jour des groupes.');\n        this.isLoading = false;\n      }\n    });\n  }\n  static {\n    this.ɵfac = function EvaluationsListComponent_Factory(t) {\n      return new (t || EvaluationsListComponent)(i0.ɵɵdirectiveInject(i1.RendusService), i0.ɵɵdirectiveInject(i2.EvaluationService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EvaluationsListComponent,\n      selectors: [[\"app-evaluations-list\"]],\n      decls: 9,\n      vars: 6,\n      consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\"], [1, \"text-2xl\", \"font-bold\", \"mb-6\"], [\"class\", \"flex justify-center items-center py-10\", 4, \"ngIf\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [\"class\", \"bg-white rounded-lg shadow-md p-4 mb-6\", 4, \"ngIf\"], [\"class\", \"flex justify-end mb-4\", 4, \"ngIf\"], [\"class\", \"bg-white shadow-md rounded-lg overflow-hidden\", 4, \"ngIf\"], [\"class\", \"bg-white shadow-md rounded-lg p-6 text-center\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"items-center\", \"py-10\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-t-2\", \"border-b-2\", \"border-indigo-500\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"flex\", \"justify-between\", \"items-center\"], [1, \"bg-red-200\", \"hover:bg-red-300\", \"text-red-800\", \"font-bold\", \"py-1\", \"px-4\", \"rounded\", \"focus:outline-none\", \"focus:shadow-outline\", 3, \"click\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"p-4\", \"mb-6\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-4\", \"gap-4\"], [\"for\", \"search\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-1\"], [\"type\", \"text\", \"id\", \"search\", \"placeholder\", \"Nom, pr\\u00E9nom, projet...\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"border-gray-300\", \"rounded-md\", \"shadow-sm\", \"focus:outline-none\", \"focus:ring-indigo-500\", \"focus:border-indigo-500\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"for\", \"groupe\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-1\"], [\"id\", \"groupe\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"border-gray-300\", \"rounded-md\", \"shadow-sm\", \"focus:outline-none\", \"focus:ring-indigo-500\", \"focus:border-indigo-500\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"projet\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-1\"], [\"id\", \"projet\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"border-gray-300\", \"rounded-md\", \"shadow-sm\", \"focus:outline-none\", \"focus:ring-indigo-500\", \"focus:border-indigo-500\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [1, \"flex\", \"items-end\"], [1, \"w-full\", \"px-4\", \"py-2\", \"bg-gray-200\", \"text-gray-700\", \"rounded-md\", \"hover:bg-gray-300\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-gray-500\", 3, \"click\"], [3, \"value\"], [1, \"flex\", \"justify-end\", \"mb-4\"], [1, \"bg-indigo-600\", \"hover:bg-indigo-700\", \"text-white\", \"font-bold\", \"py-2\", \"px-4\", \"rounded\", \"focus:outline-none\", \"focus:shadow-outline\", 3, \"click\"], [1, \"bg-white\", \"shadow-md\", \"rounded-lg\", \"overflow-hidden\"], [1, \"min-w-full\", \"divide-y\", \"divide-gray-200\"], [1, \"bg-gray-50\"], [\"scope\", \"col\", 1, \"px-6\", \"py-3\", \"text-left\", \"text-xs\", \"font-medium\", \"text-gray-500\", \"uppercase\", \"tracking-wider\"], [1, \"bg-white\", \"divide-y\", \"divide-gray-200\"], [4, \"ngFor\", \"ngForOf\"], [1, \"px-6\", \"py-4\", \"whitespace-nowrap\"], [1, \"flex\", \"items-center\"], [1, \"text-sm\", \"font-medium\", \"text-gray-900\"], [1, \"text-sm\", \"text-gray-500\"], [1, \"text-sm\", \"text-gray-900\"], [\"class\", \"text-xs text-red-500\", 4, \"ngIf\"], [1, \"px-2\", \"py-1\", \"rounded-full\", \"text-sm\", \"font-medium\", 3, \"ngClass\"], [1, \"px-6\", \"py-4\", \"whitespace-nowrap\", \"text-sm\", \"font-medium\"], [1, \"flex\", \"space-x-2\"], [1, \"text-indigo-600\", \"hover:text-indigo-900\", 3, \"click\"], [1, \"text-green-600\", \"hover:text-green-900\", 3, \"click\"], [1, \"text-xs\", \"text-red-500\"], [1, \"bg-white\", \"shadow-md\", \"rounded-lg\", \"p-6\", \"text-center\"], [1, \"text-gray-500\"]],\n      template: function EvaluationsListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h1\", 1);\n          i0.ɵɵtext(2, \"Liste des \\u00E9valuations\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, EvaluationsListComponent_div_3_Template, 2, 0, \"div\", 2);\n          i0.ɵɵtemplate(4, EvaluationsListComponent_div_4_Template, 6, 1, \"div\", 3);\n          i0.ɵɵtemplate(5, EvaluationsListComponent_div_5_Template, 23, 5, \"div\", 4);\n          i0.ɵɵtemplate(6, EvaluationsListComponent_div_6_Template, 3, 0, \"div\", 5);\n          i0.ɵɵtemplate(7, EvaluationsListComponent_div_7_Template, 18, 1, \"div\", 6);\n          i0.ɵɵtemplate(8, EvaluationsListComponent_div_8_Template, 3, 0, \"div\", 7);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.filteredEvaluations.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.filteredEvaluations.length === 0);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel],\n      styles: [\".evaluations-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n.filters[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 15px;\\n  margin-bottom: 20px;\\n  padding: 15px;\\n  background-color: #f5f5f5;\\n  border-radius: 5px;\\n}\\n\\n.filter-item[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 200px;\\n}\\n\\n.evaluations-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n  margin-top: 20px;\\n}\\n\\n.evaluations-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .evaluations-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  text-align: left;\\n  border-bottom: 1px solid #ddd;\\n}\\n\\n.evaluations-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: #f2f2f2;\\n  font-weight: bold;\\n}\\n\\n.evaluations-table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n}\\n\\n.actions-cell[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin: 50px 0;\\n}\\n\\n.no-evaluations[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin: 50px 0;\\n  color: #666;\\n}\\n\\n.score-badge[_ngcontent-%COMP%] {\\n  padding: 5px 10px;\\n  border-radius: 15px;\\n  font-weight: bold;\\n  display: inline-block;\\n}\\n\\n.score-high[_ngcontent-%COMP%] {\\n  background-color: #c8e6c9;\\n  color: #2e7d32;\\n}\\n\\n.score-medium[_ngcontent-%COMP%] {\\n  background-color: #fff9c4;\\n  color: #f57f17;\\n}\\n\\n.score-low[_ngcontent-%COMP%] {\\n  background-color: #ffcdd2;\\n  color: #c62828;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["catchError", "finalize", "takeUntil", "Subject", "of", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "EvaluationsListComponent_div_4_Template_button_click_4_listener", "ɵɵrestoreView", "_r7", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "loadEvaluations", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "ɵɵproperty", "groupe_r10", "projet_r11", "_id", "titre", "EvaluationsListComponent_div_5_Template_input_ngModelChange_5_listener", "$event", "_r13", "ctx_r12", "searchTerm", "EvaluationsListComponent_div_5_Template_input_input_5_listener", "ctx_r14", "onSearchChange", "EvaluationsListComponent_div_5_Template_select_ngModelChange_9_listener", "ctx_r15", "filterGroupe", "EvaluationsListComponent_div_5_Template_select_change_9_listener", "ctx_r16", "applyFilters", "ɵɵtemplate", "EvaluationsListComponent_div_5_option_12_Template", "EvaluationsListComponent_div_5_Template_select_ngModelChange_16_listener", "ctx_r17", "filterProjet", "EvaluationsListComponent_div_5_Template_select_change_16_listener", "ctx_r18", "EvaluationsListComponent_div_5_option_19_Template", "EvaluationsListComponent_div_5_Template_button_click_21_listener", "ctx_r19", "resetFilters", "ctx_r2", "groupes", "projets", "EvaluationsListComponent_div_6_Template_button_click_1_listener", "_r21", "ctx_r20", "updateMissingGroups", "ɵɵtextInterpolate1", "evaluation_r23", "projet", "EvaluationsListComponent_div_7_tr_17_div_14_Template", "EvaluationsListComponent_div_7_tr_17_Template_button_click_23_listener", "restoredCtx", "_r27", "$implicit", "ctx_r26", "viewEvaluationDetails", "rendu", "EvaluationsListComponent_div_7_tr_17_Template_button_click_25_listener", "ctx_r28", "editEvaluation", "ɵɵtextInterpolate2", "etudiant", "nom", "prenom", "email", "groupe", "projetDetails", "renduDetails", "ctx_r22", "formatDate", "dateEvaluation", "getScoreClass", "getScoreTotal", "EvaluationsListComponent_div_7_tr_17_Template", "ctx_r4", "filteredEvaluations", "EvaluationsListComponent", "constructor", "rendusService", "evaluationService", "router", "evaluations", "isLoading", "destroy$", "ngOnInit", "ngOnDestroy", "next", "complete", "getAllEvaluations", "pipe", "err", "subscribe", "Array", "isArray", "map", "evaluation", "evalWithDetails", "extractGroupesAndProjets", "groupesSet", "Set", "for<PERSON>ach", "trim", "add", "console", "log", "from", "sort", "projetsMap", "Map", "set", "values", "results", "term", "toLowerCase", "filter", "includes", "renduId", "navigate", "scores", "structure", "pratiques", "fonctionnalite", "originalite", "score", "date", "Date", "toLocaleDateString", "debugEtudiantData", "group", "groupEnd", "confirm", "response", "alert", "updatedCount", "ɵɵdirectiveInject", "i1", "RendusService", "i2", "EvaluationService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "EvaluationsListComponent_Template", "rf", "ctx", "EvaluationsListComponent_div_3_Template", "EvaluationsListComponent_div_4_Template", "EvaluationsListComponent_div_5_Template", "EvaluationsListComponent_div_6_Template", "EvaluationsListComponent_div_7_Template", "EvaluationsListComponent_div_8_Template", "length"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\projects\\evaluations-list\\evaluations-list.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\projects\\evaluations-list\\evaluations-list.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { RendusService } from '../../../../services/rendus.service';\nimport { EvaluationService } from '../../../../services/evaluation.service';\nimport { catchError, finalize, takeUntil } from 'rxjs/operators';\nimport { Subject, of } from 'rxjs';\nimport { Evaluation } from '../../../../models/evaluation';\nimport { Rendu } from '../../../../models/rendu';\n\n// Interface pour les évaluations avec détails\ninterface EvaluationWithDetails extends Evaluation {\n  renduDetails?: Rendu;\n  etudiant?: any;\n  projetDetails?: any;\n}\n\n@Component({\n  selector: 'app-evaluations-list',\n  templateUrl: './evaluations-list.component.html',\n  styleUrls: ['./evaluations-list.component.css'],\n})\nexport class EvaluationsListComponent implements OnInit, OnDestroy {\n  evaluations: EvaluationWithDetails[] = [];\n  filteredEvaluations: EvaluationWithDetails[] = [];\n  isLoading: boolean = true;\n  error: string = '';\n  searchTerm: string = '';\n  filterGroupe: string = '';\n  filterProjet: string = '';\n  groupes: string[] = [];\n  projets: any[] = [];\n\n  private destroy$ = new Subject<void>();\n\n  constructor(\n    private rendusService: RendusService,\n    private evaluationService: EvaluationService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.loadEvaluations();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  loadEvaluations(): void {\n    this.isLoading = true;\n    this.error = '';\n\n    this.evaluationService\n      .getAllEvaluations()\n      .pipe(\n        takeUntil(this.destroy$),\n        catchError((err) => {\n          this.error =\n            'Impossible de charger les évaluations. Veuillez réessayer plus tard.';\n          this.isLoading = false;\n          return of([]);\n        }),\n        finalize(() => {\n          this.isLoading = false;\n        })\n      )\n      .subscribe({\n        next: (evaluations) => {\n          if (!Array.isArray(evaluations)) {\n            this.error =\n              'Format de données incorrect. Veuillez réessayer plus tard.';\n            return;\n          }\n\n          this.evaluations = evaluations.map((evaluation) => {\n            const evalWithDetails = evaluation as EvaluationWithDetails;\n\n            if (\n              !evalWithDetails.projetDetails ||\n              !evalWithDetails.projetDetails.titre\n            ) {\n              if (\n                evalWithDetails.renduDetails &&\n                evalWithDetails.renduDetails.projet\n              ) {\n                evalWithDetails.projetDetails =\n                  evalWithDetails.renduDetails.projet;\n              }\n            }\n\n            return evalWithDetails;\n          });\n\n          this.extractGroupesAndProjets();\n          this.applyFilters();\n        },\n      });\n  }\n\n  extractGroupesAndProjets(): void {\n    // Extraire les groupes uniques\n    const groupesSet = new Set<string>();\n\n    this.evaluations.forEach((evaluation) => {\n      const groupe = evaluation.etudiant?.groupe;\n      if (groupe && groupe.trim() !== '') {\n        groupesSet.add(groupe);\n        console.log(`Groupe trouvé: ${groupe}`);\n      } else {\n        console.log(`Évaluation sans groupe: ${evaluation._id}`);\n      }\n    });\n\n    this.groupes = Array.from(groupesSet).sort();\n    console.log('Groupes extraits:', this.groupes);\n\n    // Extraire les projets uniques\n    const projetsMap = new Map<string, any>();\n    this.evaluations.forEach((evaluation) => {\n      if (evaluation.projetDetails && evaluation.projetDetails._id) {\n        projetsMap.set(evaluation.projetDetails._id, evaluation.projetDetails);\n      }\n    });\n    this.projets = Array.from(projetsMap.values());\n  }\n\n  applyFilters(): void {\n    let results = this.evaluations;\n\n    // Filtre par terme de recherche\n    if (this.searchTerm.trim() !== '') {\n      const term = this.searchTerm.toLowerCase().trim();\n      results = results.filter(\n        (evaluation) =>\n          evaluation.etudiant?.nom?.toLowerCase().includes(term) ||\n          evaluation.etudiant?.prenom?.toLowerCase().includes(term) ||\n          evaluation.projetDetails?.titre?.toLowerCase().includes(term)\n      );\n    }\n\n    // Filtre par groupe\n    if (this.filterGroupe) {\n      results = results.filter(\n        (evaluation) => evaluation.etudiant?.groupe === this.filterGroupe\n      );\n    }\n\n    // Filtre par projet\n    if (this.filterProjet) {\n      results = results.filter(\n        (evaluation) => evaluation.projetDetails?._id === this.filterProjet\n      );\n    }\n\n    this.filteredEvaluations = results;\n  }\n\n  onSearchChange(): void {\n    this.applyFilters();\n  }\n\n  resetFilters(): void {\n    this.searchTerm = '';\n    this.filterGroupe = '';\n    this.filterProjet = '';\n    this.applyFilters();\n  }\n\n  editEvaluation(renduId: string): void {\n    this.router.navigate(['/admin/projects/edit-evaluation', renduId]);\n  }\n\n  viewEvaluationDetails(renduId: string): void {\n    this.router.navigate(['/admin/projects/evaluation-details', renduId]);\n  }\n\n  getScoreTotal(evaluation: EvaluationWithDetails): number {\n    if (!evaluation.scores) return 0;\n\n    const scores = evaluation.scores;\n    return (\n      scores.structure +\n      scores.pratiques +\n      scores.fonctionnalite +\n      scores.originalite\n    );\n  }\n\n  getScoreClass(score: number): string {\n    if (score >= 16) return 'text-green-600 bg-green-100';\n    if (score >= 12) return 'text-blue-600 bg-blue-100';\n    if (score >= 8) return 'text-yellow-600 bg-yellow-100';\n    return 'text-red-600 bg-red-100';\n  }\n\n  formatDate(date: string | Date | undefined): string {\n    if (!date) return 'Non disponible';\n    return new Date(date).toLocaleDateString();\n  }\n\n  // Ajouter cette fonction pour déboguer les données d'étudiant\n  debugEtudiantData(): void {\n    console.group('Débogage données étudiants');\n\n    this.evaluations.forEach((evaluation) => {\n      console.log(`Évaluation ${evaluation._id}:`);\n      console.log('- Étudiant:', evaluation.etudiant);\n      if (evaluation.etudiant) {\n        console.log('- Nom:', evaluation.etudiant.nom);\n        console.log('- Prénom:', evaluation.etudiant.prenom);\n        console.log('- Groupe:', evaluation.etudiant.groupe);\n      }\n      console.log('- Rendu:', evaluation.renduDetails);\n    });\n\n    console.groupEnd();\n  }\n\n  // Ajouter cette méthode pour mettre à jour les groupes manquants\n  updateMissingGroups(): void {\n    if (\n      !confirm('Voulez-vous mettre à jour les groupes manquants des étudiants?')\n    ) {\n      return;\n    }\n\n    this.isLoading = true;\n\n    this.evaluationService.updateMissingGroups().subscribe({\n      next: (response) => {\n        console.log('Mise à jour des groupes:', response);\n        alert(\n          `${response.updatedCount} étudiants mis à jour avec leur groupe.`\n        );\n        this.loadEvaluations(); // Recharger les données\n      },\n      error: (err) => {\n        console.error('Erreur lors de la mise à jour des groupes:', err);\n        alert('Erreur lors de la mise à jour des groupes.');\n        this.isLoading = false;\n      },\n    });\n  }\n}\n", "<div class=\"container mx-auto px-4 py-6\">\n  <h1 class=\"text-2xl font-bold mb-6\">Liste des évaluations</h1>\n\n  <!-- Loader -->\n  <div *ngIf=\"isLoading\" class=\"flex justify-center items-center py-10\">\n    <div class=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500\"></div>\n  </div>\n\n  <!-- Message d'erreur -->\n  <div *ngIf=\"error\" class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n    <div class=\"flex justify-between items-center\">\n      <div>{{ error }}</div>\n      <button (click)=\"loadEvaluations()\" \n        class=\"bg-red-200 hover:bg-red-300 text-red-800 font-bold py-1 px-4 rounded focus:outline-none focus:shadow-outline\">\n        Réessayer\n      </button>\n    </div>\n  </div>\n\n  <!-- Filtres -->\n  <div *ngIf=\"!isLoading && !error\" class=\"bg-white rounded-lg shadow-md p-4 mb-6\">\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n      <!-- Recherche -->\n      <div>\n        <label for=\"search\" class=\"block text-sm font-medium text-gray-700 mb-1\">Recherche</label>\n        <input type=\"text\" id=\"search\" [(ngModel)]=\"searchTerm\" (input)=\"onSearchChange()\"\n          class=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\n          placeholder=\"Nom, prénom, projet...\">\n      </div>\n\n      <!-- Filtre par groupe -->\n      <div>\n        <label for=\"groupe\" class=\"block text-sm font-medium text-gray-700 mb-1\">Groupe</label>\n        <select id=\"groupe\" [(ngModel)]=\"filterGroupe\" (change)=\"applyFilters()\"\n          class=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\">\n          <option value=\"\">Tous les groupes</option>\n          <option *ngFor=\"let groupe of groupes\" [value]=\"groupe\">{{ groupe }}</option>\n        </select>\n      </div>\n\n      <!-- Filtre par projet -->\n      <div>\n        <label for=\"projet\" class=\"block text-sm font-medium text-gray-700 mb-1\">Projet</label>\n        <select id=\"projet\" [(ngModel)]=\"filterProjet\" (change)=\"applyFilters()\"\n          class=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\">\n          <option value=\"\">Tous les projets</option>\n          <option *ngFor=\"let projet of projets\" [value]=\"projet._id\">{{ projet.titre }}</option>\n        </select>\n      </div>\n\n      <!-- Bouton de réinitialisation -->\n      <div class=\"flex items-end\">\n        <button (click)=\"resetFilters()\" \n          class=\"w-full px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500\">\n          Réinitialiser les filtres\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Ajouter ce bouton dans la section des filtres -->\n  <div *ngIf=\"!isLoading && !error\" class=\"flex justify-end mb-4\">\n    <button (click)=\"updateMissingGroups()\" \n      class=\"bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\">\n      Mettre à jour les groupes manquants\n    </button>\n  </div>\n\n  <!-- Tableau des évaluations -->\n  <div *ngIf=\"!isLoading && !error && filteredEvaluations.length > 0\" class=\"bg-white shadow-md rounded-lg overflow-hidden\">\n    <table class=\"min-w-full divide-y divide-gray-200\">\n      <thead class=\"bg-gray-50\">\n        <tr>\n          <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Étudiant\n          </th>\n          <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Groupe\n          </th>\n          <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Projet\n          </th>\n          <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Date d'évaluation\n          </th>\n          <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Score\n          </th>\n          <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Actions\n          </th>\n        </tr>\n      </thead>\n      <tbody class=\"bg-white divide-y divide-gray-200\">\n        <tr *ngFor=\"let evaluation of filteredEvaluations\">\n          <td class=\"px-6 py-4 whitespace-nowrap\">\n            <div class=\"flex items-center\">\n              <div>\n                <div class=\"text-sm font-medium text-gray-900\">\n                  {{ evaluation.etudiant?.nom || 'N/A' }} {{ evaluation.etudiant?.prenom || '' }}\n                </div>\n                <div class=\"text-sm text-gray-500\">\n                  {{ evaluation.etudiant?.email || 'Email non disponible' }}\n                </div>\n              </div>\n            </div>\n          </td>\n          <td class=\"px-6 py-4 whitespace-nowrap\">\n            <div class=\"text-sm text-gray-900\">{{ evaluation.etudiant?.groupe || 'Non spécifié' }}</div>\n          </td>\n          <td class=\"px-6 py-4 whitespace-nowrap\">\n            <div class=\"text-sm text-gray-900\">\n              {{ evaluation.projetDetails?.titre || \n                 evaluation.renduDetails?.projet?.titre || \n                 'Projet inconnu' }}\n            </div>\n            <div *ngIf=\"!evaluation.projetDetails?.titre && !evaluation.renduDetails?.projet?.titre\" \n                 class=\"text-xs text-red-500\">\n              ID: {{ evaluation.projet || 'Non disponible' }}\n            </div>\n          </td>\n          <td class=\"px-6 py-4 whitespace-nowrap\">\n            <div class=\"text-sm text-gray-900\">{{ formatDate(evaluation.dateEvaluation) }}</div>\n          </td>\n          <td class=\"px-6 py-4 whitespace-nowrap\">\n            <span [ngClass]=\"getScoreClass(getScoreTotal(evaluation))\" class=\"px-2 py-1 rounded-full text-sm font-medium\">\n              {{ getScoreTotal(evaluation) }}/20\n            </span>\n          </td>\n          <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n            <div class=\"flex space-x-2\">\n              <button (click)=\"viewEvaluationDetails(evaluation.rendu)\" \n                class=\"text-indigo-600 hover:text-indigo-900\">\n                Voir\n              </button>\n              <button (click)=\"editEvaluation(evaluation.rendu)\" \n                class=\"text-green-600 hover:text-green-900\">\n                Modifier\n              </button>\n            </div>\n          </td>\n        </tr>\n      </tbody>\n    </table>\n  </div>\n\n  <!-- Message si aucune évaluation trouvée -->\n  <div *ngIf=\"!isLoading && !error && filteredEvaluations.length === 0\" class=\"bg-white shadow-md rounded-lg p-6 text-center\">\n    <p class=\"text-gray-500\">Aucune évaluation trouvée</p>\n  </div>\n</div>\n\n\n\n"], "mappings": "AAIA,SAASA,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,gBAAgB;AAChE,SAASC,OAAO,EAAEC,EAAE,QAAQ,MAAM;;;;;;;;;ICDhCC,EAAA,CAAAC,cAAA,aAAsE;IACpED,EAAA,CAAAE,SAAA,aAA+F;IACjGF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAGNH,EAAA,CAAAC,cAAA,cAAgG;IAEvFD,EAAA,CAAAI,MAAA,GAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACtBH,EAAA,CAAAC,cAAA,iBACuH;IAD/GD,EAAA,CAAAK,UAAA,mBAAAC,gEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC;IAEjCZ,EAAA,CAAAI,MAAA,uBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IAJJH,EAAA,CAAAa,SAAA,GAAW;IAAXb,EAAA,CAAAc,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAyBZhB,EAAA,CAAAC,cAAA,iBAAwD;IAAAD,EAAA,CAAAI,MAAA,GAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IAAtCH,EAAA,CAAAiB,UAAA,UAAAC,UAAA,CAAgB;IAAClB,EAAA,CAAAa,SAAA,GAAY;IAAZb,EAAA,CAAAc,iBAAA,CAAAI,UAAA,CAAY;;;;;IAUpElB,EAAA,CAAAC,cAAA,iBAA4D;IAAAD,EAAA,CAAAI,MAAA,GAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IAAhDH,EAAA,CAAAiB,UAAA,UAAAE,UAAA,CAAAC,GAAA,CAAoB;IAACpB,EAAA,CAAAa,SAAA,GAAkB;IAAlBb,EAAA,CAAAc,iBAAA,CAAAK,UAAA,CAAAE,KAAA,CAAkB;;;;;;IA1BtFrB,EAAA,CAAAC,cAAA,cAAiF;IAIFD,EAAA,CAAAI,MAAA,gBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAC1FH,EAAA,CAAAC,cAAA,gBAEuC;IAFRD,EAAA,CAAAK,UAAA,2BAAAiB,uEAAAC,MAAA;MAAAvB,EAAA,CAAAO,aAAA,CAAAiB,IAAA;MAAA,MAAAC,OAAA,GAAAzB,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAc,OAAA,CAAAC,UAAA,GAAAH,MAAA;IAAA,EAAwB,mBAAAI,+DAAA;MAAA3B,EAAA,CAAAO,aAAA,CAAAiB,IAAA;MAAA,MAAAI,OAAA,GAAA5B,EAAA,CAAAU,aAAA;MAAA,OAAUV,EAAA,CAAAW,WAAA,CAAAiB,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAA1B;IAAvD7B,EAAA,CAAAG,YAAA,EAEuC;IAIzCH,EAAA,CAAAC,cAAA,UAAK;IACsED,EAAA,CAAAI,MAAA,aAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACvFH,EAAA,CAAAC,cAAA,iBACwI;IADpHD,EAAA,CAAAK,UAAA,2BAAAyB,wEAAAP,MAAA;MAAAvB,EAAA,CAAAO,aAAA,CAAAiB,IAAA;MAAA,MAAAO,OAAA,GAAA/B,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAoB,OAAA,CAAAC,YAAA,GAAAT,MAAA;IAAA,EAA0B,oBAAAU,iEAAA;MAAAjC,EAAA,CAAAO,aAAA,CAAAiB,IAAA;MAAA,MAAAU,OAAA,GAAAlC,EAAA,CAAAU,aAAA;MAAA,OAAWV,EAAA,CAAAW,WAAA,CAAAuB,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAzB;IAE5CnC,EAAA,CAAAC,cAAA,kBAAiB;IAAAD,EAAA,CAAAI,MAAA,wBAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAC1CH,EAAA,CAAAoC,UAAA,KAAAC,iDAAA,qBAA6E;IAC/ErC,EAAA,CAAAG,YAAA,EAAS;IAIXH,EAAA,CAAAC,cAAA,WAAK;IACsED,EAAA,CAAAI,MAAA,cAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACvFH,EAAA,CAAAC,cAAA,kBACwI;IADpHD,EAAA,CAAAK,UAAA,2BAAAiC,yEAAAf,MAAA;MAAAvB,EAAA,CAAAO,aAAA,CAAAiB,IAAA;MAAA,MAAAe,OAAA,GAAAvC,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAA4B,OAAA,CAAAC,YAAA,GAAAjB,MAAA;IAAA,EAA0B,oBAAAkB,kEAAA;MAAAzC,EAAA,CAAAO,aAAA,CAAAiB,IAAA;MAAA,MAAAkB,OAAA,GAAA1C,EAAA,CAAAU,aAAA;MAAA,OAAWV,EAAA,CAAAW,WAAA,CAAA+B,OAAA,CAAAP,YAAA,EAAc;IAAA,EAAzB;IAE5CnC,EAAA,CAAAC,cAAA,kBAAiB;IAAAD,EAAA,CAAAI,MAAA,wBAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAC1CH,EAAA,CAAAoC,UAAA,KAAAO,iDAAA,qBAAuF;IACzF3C,EAAA,CAAAG,YAAA,EAAS;IAIXH,EAAA,CAAAC,cAAA,eAA4B;IAClBD,EAAA,CAAAK,UAAA,mBAAAuC,iEAAA;MAAA5C,EAAA,CAAAO,aAAA,CAAAiB,IAAA;MAAA,MAAAqB,OAAA,GAAA7C,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAkC,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAE9B9C,EAAA,CAAAI,MAAA,wCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IA9BsBH,EAAA,CAAAa,SAAA,GAAwB;IAAxBb,EAAA,CAAAiB,UAAA,YAAA8B,MAAA,CAAArB,UAAA,CAAwB;IAQnC1B,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAiB,UAAA,YAAA8B,MAAA,CAAAf,YAAA,CAA0B;IAGjBhC,EAAA,CAAAa,SAAA,GAAU;IAAVb,EAAA,CAAAiB,UAAA,YAAA8B,MAAA,CAAAC,OAAA,CAAU;IAOnBhD,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAiB,UAAA,YAAA8B,MAAA,CAAAP,YAAA,CAA0B;IAGjBxC,EAAA,CAAAa,SAAA,GAAU;IAAVb,EAAA,CAAAiB,UAAA,YAAA8B,MAAA,CAAAE,OAAA,CAAU;;;;;;IAe7CjD,EAAA,CAAAC,cAAA,cAAgE;IACtDD,EAAA,CAAAK,UAAA,mBAAA6C,gEAAA;MAAAlD,EAAA,CAAAO,aAAA,CAAA4C,IAAA;MAAA,MAAAC,OAAA,GAAApD,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAyC,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IAErCrD,EAAA,CAAAI,MAAA,iDACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;IAmDDH,EAAA,CAAAC,cAAA,cACkC;IAChCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAsD,kBAAA,UAAAC,cAAA,CAAAC,MAAA,0BACF;;;;;;IAzBJxD,EAAA,CAAAC,cAAA,SAAmD;IAKzCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAmC;IACjCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAIZH,EAAA,CAAAC,cAAA,aAAwC;IACHD,EAAA,CAAAI,MAAA,IAAmD;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAE9FH,EAAA,CAAAC,cAAA,cAAwC;IAEpCD,EAAA,CAAAI,MAAA,IAGF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAoC,UAAA,KAAAqB,oDAAA,kBAGM;IACRzD,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAwC;IACHD,EAAA,CAAAI,MAAA,IAA2C;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAEtFH,EAAA,CAAAC,cAAA,cAAwC;IAEpCD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,cAA4D;IAEhDD,EAAA,CAAAK,UAAA,mBAAAqD,uEAAA;MAAA,MAAAC,WAAA,GAAA3D,EAAA,CAAAO,aAAA,CAAAqD,IAAA;MAAA,MAAAL,cAAA,GAAAI,WAAA,CAAAE,SAAA;MAAA,MAAAC,OAAA,GAAA9D,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAmD,OAAA,CAAAC,qBAAA,CAAAR,cAAA,CAAAS,KAAA,CAAuC;IAAA,EAAC;IAEvDhE,EAAA,CAAAI,MAAA,cACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAC8C;IADtCD,EAAA,CAAAK,UAAA,mBAAA4D,uEAAA;MAAA,MAAAN,WAAA,GAAA3D,EAAA,CAAAO,aAAA,CAAAqD,IAAA;MAAA,MAAAL,cAAA,GAAAI,WAAA,CAAAE,SAAA;MAAA,MAAAK,OAAA,GAAAlE,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAuD,OAAA,CAAAC,cAAA,CAAAZ,cAAA,CAAAS,KAAA,CAAgC;IAAA,EAAC;IAEhDhE,EAAA,CAAAI,MAAA,kBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;IAvCLH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAoE,kBAAA,OAAAb,cAAA,CAAAc,QAAA,kBAAAd,cAAA,CAAAc,QAAA,CAAAC,GAAA,kBAAAf,cAAA,CAAAc,QAAA,kBAAAd,cAAA,CAAAc,QAAA,CAAAE,MAAA,aACF;IAEEvE,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAsD,kBAAA,OAAAC,cAAA,CAAAc,QAAA,kBAAAd,cAAA,CAAAc,QAAA,CAAAG,KAAA,iCACF;IAK+BxE,EAAA,CAAAa,SAAA,GAAmD;IAAnDb,EAAA,CAAAc,iBAAA,EAAAyC,cAAA,CAAAc,QAAA,kBAAAd,cAAA,CAAAc,QAAA,CAAAI,MAAA,8BAAmD;IAIpFzE,EAAA,CAAAa,SAAA,GAGF;IAHEb,EAAA,CAAAsD,kBAAA,OAAAC,cAAA,CAAAmB,aAAA,kBAAAnB,cAAA,CAAAmB,aAAA,CAAArD,KAAA,MAAAkC,cAAA,CAAAoB,YAAA,kBAAApB,cAAA,CAAAoB,YAAA,CAAAnB,MAAA,kBAAAD,cAAA,CAAAoB,YAAA,CAAAnB,MAAA,CAAAnC,KAAA,2BAGF;IACMrB,EAAA,CAAAa,SAAA,GAAiF;IAAjFb,EAAA,CAAAiB,UAAA,WAAAsC,cAAA,CAAAmB,aAAA,kBAAAnB,cAAA,CAAAmB,aAAA,CAAArD,KAAA,OAAAkC,cAAA,CAAAoB,YAAA,kBAAApB,cAAA,CAAAoB,YAAA,CAAAnB,MAAA,kBAAAD,cAAA,CAAAoB,YAAA,CAAAnB,MAAA,CAAAnC,KAAA,EAAiF;IAMpDrB,EAAA,CAAAa,SAAA,GAA2C;IAA3Cb,EAAA,CAAAc,iBAAA,CAAA8D,OAAA,CAAAC,UAAA,CAAAtB,cAAA,CAAAuB,cAAA,EAA2C;IAGxE9E,EAAA,CAAAa,SAAA,GAAoD;IAApDb,EAAA,CAAAiB,UAAA,YAAA2D,OAAA,CAAAG,aAAA,CAAAH,OAAA,CAAAI,aAAA,CAAAzB,cAAA,GAAoD;IACxDvD,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAsD,kBAAA,MAAAsB,OAAA,CAAAI,aAAA,CAAAzB,cAAA,UACF;;;;;IA1DVvD,EAAA,CAAAC,cAAA,cAA0H;IAKhHD,EAAA,CAAAI,MAAA,sBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAuG;IACrGD,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAuG;IACrGD,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAuG;IACrGD,EAAA,CAAAI,MAAA,gCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAuG;IACrGD,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAuG;IACrGD,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAGTH,EAAA,CAAAC,cAAA,iBAAiD;IAC/CD,EAAA,CAAAoC,UAAA,KAAA6C,6CAAA,kBA+CK;IACPjF,EAAA,CAAAG,YAAA,EAAQ;;;;IAhDqBH,EAAA,CAAAa,SAAA,IAAsB;IAAtBb,EAAA,CAAAiB,UAAA,YAAAiE,MAAA,CAAAC,mBAAA,CAAsB;;;;;IAqDvDnF,EAAA,CAAAC,cAAA,cAA4H;IACjGD,EAAA,CAAAI,MAAA,0CAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;AD/H1D,OAAM,MAAOiF,wBAAwB;EAanCC,YACUC,aAA4B,EAC5BC,iBAAoC,EACpCC,MAAc;IAFd,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,MAAM,GAANA,MAAM;IAfhB,KAAAC,WAAW,GAA4B,EAAE;IACzC,KAAAN,mBAAmB,GAA4B,EAAE;IACjD,KAAAO,SAAS,GAAY,IAAI;IACzB,KAAA1E,KAAK,GAAW,EAAE;IAClB,KAAAU,UAAU,GAAW,EAAE;IACvB,KAAAM,YAAY,GAAW,EAAE;IACzB,KAAAQ,YAAY,GAAW,EAAE;IACzB,KAAAQ,OAAO,GAAa,EAAE;IACtB,KAAAC,OAAO,GAAU,EAAE;IAEX,KAAA0C,QAAQ,GAAG,IAAI7F,OAAO,EAAQ;EAMnC;EAEH8F,QAAQA,CAAA;IACN,IAAI,CAAChF,eAAe,EAAE;EACxB;EAEAiF,WAAWA,CAAA;IACT,IAAI,CAACF,QAAQ,CAACG,IAAI,EAAE;IACpB,IAAI,CAACH,QAAQ,CAACI,QAAQ,EAAE;EAC1B;EAEAnF,eAAeA,CAAA;IACb,IAAI,CAAC8E,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC1E,KAAK,GAAG,EAAE;IAEf,IAAI,CAACuE,iBAAiB,CACnBS,iBAAiB,EAAE,CACnBC,IAAI,CACHpG,SAAS,CAAC,IAAI,CAAC8F,QAAQ,CAAC,EACxBhG,UAAU,CAAEuG,GAAG,IAAI;MACjB,IAAI,CAAClF,KAAK,GACR,sEAAsE;MACxE,IAAI,CAAC0E,SAAS,GAAG,KAAK;MACtB,OAAO3F,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,EACFH,QAAQ,CAAC,MAAK;MACZ,IAAI,CAAC8F,SAAS,GAAG,KAAK;IACxB,CAAC,CAAC,CACH,CACAS,SAAS,CAAC;MACTL,IAAI,EAAGL,WAAW,IAAI;QACpB,IAAI,CAACW,KAAK,CAACC,OAAO,CAACZ,WAAW,CAAC,EAAE;UAC/B,IAAI,CAACzE,KAAK,GACR,4DAA4D;UAC9D;;QAGF,IAAI,CAACyE,WAAW,GAAGA,WAAW,CAACa,GAAG,CAAEC,UAAU,IAAI;UAChD,MAAMC,eAAe,GAAGD,UAAmC;UAE3D,IACE,CAACC,eAAe,CAAC9B,aAAa,IAC9B,CAAC8B,eAAe,CAAC9B,aAAa,CAACrD,KAAK,EACpC;YACA,IACEmF,eAAe,CAAC7B,YAAY,IAC5B6B,eAAe,CAAC7B,YAAY,CAACnB,MAAM,EACnC;cACAgD,eAAe,CAAC9B,aAAa,GAC3B8B,eAAe,CAAC7B,YAAY,CAACnB,MAAM;;;UAIzC,OAAOgD,eAAe;QACxB,CAAC,CAAC;QAEF,IAAI,CAACC,wBAAwB,EAAE;QAC/B,IAAI,CAACtE,YAAY,EAAE;MACrB;KACD,CAAC;EACN;EAEAsE,wBAAwBA,CAAA;IACtB;IACA,MAAMC,UAAU,GAAG,IAAIC,GAAG,EAAU;IAEpC,IAAI,CAAClB,WAAW,CAACmB,OAAO,CAAEL,UAAU,IAAI;MACtC,MAAM9B,MAAM,GAAG8B,UAAU,CAAClC,QAAQ,EAAEI,MAAM;MAC1C,IAAIA,MAAM,IAAIA,MAAM,CAACoC,IAAI,EAAE,KAAK,EAAE,EAAE;QAClCH,UAAU,CAACI,GAAG,CAACrC,MAAM,CAAC;QACtBsC,OAAO,CAACC,GAAG,CAAC,kBAAkBvC,MAAM,EAAE,CAAC;OACxC,MAAM;QACLsC,OAAO,CAACC,GAAG,CAAC,2BAA2BT,UAAU,CAACnF,GAAG,EAAE,CAAC;;IAE5D,CAAC,CAAC;IAEF,IAAI,CAAC4B,OAAO,GAAGoD,KAAK,CAACa,IAAI,CAACP,UAAU,CAAC,CAACQ,IAAI,EAAE;IAC5CH,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAChE,OAAO,CAAC;IAE9C;IACA,MAAMmE,UAAU,GAAG,IAAIC,GAAG,EAAe;IACzC,IAAI,CAAC3B,WAAW,CAACmB,OAAO,CAAEL,UAAU,IAAI;MACtC,IAAIA,UAAU,CAAC7B,aAAa,IAAI6B,UAAU,CAAC7B,aAAa,CAACtD,GAAG,EAAE;QAC5D+F,UAAU,CAACE,GAAG,CAACd,UAAU,CAAC7B,aAAa,CAACtD,GAAG,EAAEmF,UAAU,CAAC7B,aAAa,CAAC;;IAE1E,CAAC,CAAC;IACF,IAAI,CAACzB,OAAO,GAAGmD,KAAK,CAACa,IAAI,CAACE,UAAU,CAACG,MAAM,EAAE,CAAC;EAChD;EAEAnF,YAAYA,CAAA;IACV,IAAIoF,OAAO,GAAG,IAAI,CAAC9B,WAAW;IAE9B;IACA,IAAI,IAAI,CAAC/D,UAAU,CAACmF,IAAI,EAAE,KAAK,EAAE,EAAE;MACjC,MAAMW,IAAI,GAAG,IAAI,CAAC9F,UAAU,CAAC+F,WAAW,EAAE,CAACZ,IAAI,EAAE;MACjDU,OAAO,GAAGA,OAAO,CAACG,MAAM,CACrBnB,UAAU,IACTA,UAAU,CAAClC,QAAQ,EAAEC,GAAG,EAAEmD,WAAW,EAAE,CAACE,QAAQ,CAACH,IAAI,CAAC,IACtDjB,UAAU,CAAClC,QAAQ,EAAEE,MAAM,EAAEkD,WAAW,EAAE,CAACE,QAAQ,CAACH,IAAI,CAAC,IACzDjB,UAAU,CAAC7B,aAAa,EAAErD,KAAK,EAAEoG,WAAW,EAAE,CAACE,QAAQ,CAACH,IAAI,CAAC,CAChE;;IAGH;IACA,IAAI,IAAI,CAACxF,YAAY,EAAE;MACrBuF,OAAO,GAAGA,OAAO,CAACG,MAAM,CACrBnB,UAAU,IAAKA,UAAU,CAAClC,QAAQ,EAAEI,MAAM,KAAK,IAAI,CAACzC,YAAY,CAClE;;IAGH;IACA,IAAI,IAAI,CAACQ,YAAY,EAAE;MACrB+E,OAAO,GAAGA,OAAO,CAACG,MAAM,CACrBnB,UAAU,IAAKA,UAAU,CAAC7B,aAAa,EAAEtD,GAAG,KAAK,IAAI,CAACoB,YAAY,CACpE;;IAGH,IAAI,CAAC2C,mBAAmB,GAAGoC,OAAO;EACpC;EAEA1F,cAAcA,CAAA;IACZ,IAAI,CAACM,YAAY,EAAE;EACrB;EAEAW,YAAYA,CAAA;IACV,IAAI,CAACpB,UAAU,GAAG,EAAE;IACpB,IAAI,CAACM,YAAY,GAAG,EAAE;IACtB,IAAI,CAACQ,YAAY,GAAG,EAAE;IACtB,IAAI,CAACL,YAAY,EAAE;EACrB;EAEAgC,cAAcA,CAACyD,OAAe;IAC5B,IAAI,CAACpC,MAAM,CAACqC,QAAQ,CAAC,CAAC,iCAAiC,EAAED,OAAO,CAAC,CAAC;EACpE;EAEA7D,qBAAqBA,CAAC6D,OAAe;IACnC,IAAI,CAACpC,MAAM,CAACqC,QAAQ,CAAC,CAAC,oCAAoC,EAAED,OAAO,CAAC,CAAC;EACvE;EAEA5C,aAAaA,CAACuB,UAAiC;IAC7C,IAAI,CAACA,UAAU,CAACuB,MAAM,EAAE,OAAO,CAAC;IAEhC,MAAMA,MAAM,GAAGvB,UAAU,CAACuB,MAAM;IAChC,OACEA,MAAM,CAACC,SAAS,GAChBD,MAAM,CAACE,SAAS,GAChBF,MAAM,CAACG,cAAc,GACrBH,MAAM,CAACI,WAAW;EAEtB;EAEAnD,aAAaA,CAACoD,KAAa;IACzB,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,6BAA6B;IACrD,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,2BAA2B;IACnD,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,+BAA+B;IACtD,OAAO,yBAAyB;EAClC;EAEAtD,UAAUA,CAACuD,IAA+B;IACxC,IAAI,CAACA,IAAI,EAAE,OAAO,gBAAgB;IAClC,OAAO,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,kBAAkB,EAAE;EAC5C;EAEA;EACAC,iBAAiBA,CAAA;IACfxB,OAAO,CAACyB,KAAK,CAAC,4BAA4B,CAAC;IAE3C,IAAI,CAAC/C,WAAW,CAACmB,OAAO,CAAEL,UAAU,IAAI;MACtCQ,OAAO,CAACC,GAAG,CAAC,cAAcT,UAAU,CAACnF,GAAG,GAAG,CAAC;MAC5C2F,OAAO,CAACC,GAAG,CAAC,aAAa,EAAET,UAAU,CAAClC,QAAQ,CAAC;MAC/C,IAAIkC,UAAU,CAAClC,QAAQ,EAAE;QACvB0C,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAET,UAAU,CAAClC,QAAQ,CAACC,GAAG,CAAC;QAC9CyC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAET,UAAU,CAAClC,QAAQ,CAACE,MAAM,CAAC;QACpDwC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAET,UAAU,CAAClC,QAAQ,CAACI,MAAM,CAAC;;MAEtDsC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAET,UAAU,CAAC5B,YAAY,CAAC;IAClD,CAAC,CAAC;IAEFoC,OAAO,CAAC0B,QAAQ,EAAE;EACpB;EAEA;EACApF,mBAAmBA,CAAA;IACjB,IACE,CAACqF,OAAO,CAAC,gEAAgE,CAAC,EAC1E;MACA;;IAGF,IAAI,CAAChD,SAAS,GAAG,IAAI;IAErB,IAAI,CAACH,iBAAiB,CAAClC,mBAAmB,EAAE,CAAC8C,SAAS,CAAC;MACrDL,IAAI,EAAG6C,QAAQ,IAAI;QACjB5B,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE2B,QAAQ,CAAC;QACjDC,KAAK,CACH,GAAGD,QAAQ,CAACE,YAAY,yCAAyC,CAClE;QACD,IAAI,CAACjI,eAAe,EAAE,CAAC,CAAC;MAC1B,CAAC;;MACDI,KAAK,EAAGkF,GAAG,IAAI;QACba,OAAO,CAAC/F,KAAK,CAAC,4CAA4C,EAAEkF,GAAG,CAAC;QAChE0C,KAAK,CAAC,4CAA4C,CAAC;QACnD,IAAI,CAAClD,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;;;uBA9NWN,wBAAwB,EAAApF,EAAA,CAAA8I,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAhJ,EAAA,CAAA8I,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAAlJ,EAAA,CAAA8I,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAxBhE,wBAAwB;MAAAiE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrBrC3J,EAAA,CAAAC,cAAA,aAAyC;UACHD,EAAA,CAAAI,MAAA,iCAAqB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAG9DH,EAAA,CAAAoC,UAAA,IAAAyH,uCAAA,iBAEM;UAGN7J,EAAA,CAAAoC,UAAA,IAAA0H,uCAAA,iBAQM;UAGN9J,EAAA,CAAAoC,UAAA,IAAA2H,uCAAA,kBAsCM;UAGN/J,EAAA,CAAAoC,UAAA,IAAA4H,uCAAA,iBAKM;UAGNhK,EAAA,CAAAoC,UAAA,IAAA6H,uCAAA,kBA2EM;UAGNjK,EAAA,CAAAoC,UAAA,IAAA8H,uCAAA,iBAEM;UACRlK,EAAA,CAAAG,YAAA,EAAM;;;UAlJEH,EAAA,CAAAa,SAAA,GAAe;UAAfb,EAAA,CAAAiB,UAAA,SAAA2I,GAAA,CAAAlE,SAAA,CAAe;UAKf1F,EAAA,CAAAa,SAAA,GAAW;UAAXb,EAAA,CAAAiB,UAAA,SAAA2I,GAAA,CAAA5I,KAAA,CAAW;UAWXhB,EAAA,CAAAa,SAAA,GAA0B;UAA1Bb,EAAA,CAAAiB,UAAA,UAAA2I,GAAA,CAAAlE,SAAA,KAAAkE,GAAA,CAAA5I,KAAA,CAA0B;UAyC1BhB,EAAA,CAAAa,SAAA,GAA0B;UAA1Bb,EAAA,CAAAiB,UAAA,UAAA2I,GAAA,CAAAlE,SAAA,KAAAkE,GAAA,CAAA5I,KAAA,CAA0B;UAQ1BhB,EAAA,CAAAa,SAAA,GAA4D;UAA5Db,EAAA,CAAAiB,UAAA,UAAA2I,GAAA,CAAAlE,SAAA,KAAAkE,GAAA,CAAA5I,KAAA,IAAA4I,GAAA,CAAAzE,mBAAA,CAAAgF,MAAA,KAA4D;UA8E5DnK,EAAA,CAAAa,SAAA,GAA8D;UAA9Db,EAAA,CAAAiB,UAAA,UAAA2I,GAAA,CAAAlE,SAAA,KAAAkE,GAAA,CAAA5I,KAAA,IAAA4I,GAAA,CAAAzE,mBAAA,CAAAgF,MAAA,OAA8D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}